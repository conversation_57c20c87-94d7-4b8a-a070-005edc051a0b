'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Download, 
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  Clock,
  AlertTriangle,
  AlertCircle,
  School,
  CreditCard,
  Calendar,
  Users,
  TrendingUp,
  Pause
} from 'lucide-react'

// UI Components
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'

// Utils
import { AuthUtils } from '@/src/utils/authUtils'

// Simple Table Components
const Table = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`overflow-hidden rounded-lg border border-slate-200 ${className}`}>
    <table className="w-full border-collapse">{children}</table>
  </div>
)

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead className="bg-slate-50">{children}</thead>
)

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody className="divide-y divide-slate-200">{children}</tbody>
)

const TableRow = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <tr className={`hover:bg-slate-50 ${className}`}>{children}</tr>
)

const TableHead = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <th className={`px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider ${className}`}>
    {children}
  </th>
)

const TableCell = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <td className={`px-6 py-4 whitespace-nowrap text-sm text-slate-900 ${className}`}>
    {children}
  </td>
)

// Types
interface PartnerData {
  id: string
  name: string
  email: string
  phone: string
  profitSharePercentage: number
  totalSchools: number
}

interface SchoolData {
  id: string
  schoolName: string
  monthlyAmount: number
  paymentStatus: 'paid' | 'pending' | 'overdue' | 'hold_period'
  lastPaymentDate?: string
  nextDueDate: string
  daysOverdue?: number
  holdStatus: 'none' | 'active' | 'released'
  holdExpiryDate?: string
}

interface PortfolioSummary {
  totalSchools: number
  totalMonthlyRevenue: number
  pendingPayments: number
  schoolsInHold: number
  totalCommissionEarned: number
}

interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalRecords: number
  recordsPerPage: number
}

export default function PartnerSchoolPortfolioPage() {
  const params = useParams()
  const router = useRouter()
  const partnerId = params.partnerId as string

  // State Management
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [partnerData, setPartnerData] = useState<PartnerData | null>(null)
  const [schools, setSchools] = useState<SchoolData[]>([])
  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary | null>(null)
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    recordsPerPage: 15
  })

  // Filter States
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string>('all')
  const [holdStatusFilter, setHoldStatusFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    // Check admin authentication
    if (!AuthUtils.isAuthenticated('admin')) {
      router.push('/admin/login')
      return
    }

    fetchPartnerSchools()
  }, [partnerId, pagination.currentPage, paymentStatusFilter, holdStatusFilter, searchTerm])

  const fetchPartnerSchools = async () => {
    try {
      setLoading(true)
      setError(null)

      const token = AuthUtils.getToken('admin')
      if (!token) {
        router.push('/admin/login')
        return
      }

      const queryParams = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: pagination.recordsPerPage.toString(),
        paymentStatus: paymentStatusFilter,
        holdStatus: holdStatusFilter,
        search: searchTerm
      })

      const response = await fetch(`/api/admin/partners/${partnerId}/schools?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch partner schools')
      }

      const data = await response.json()
      
      setPartnerData(data.partner)
      setSchools(data.schools)
      setPortfolioSummary(data.summary)
      setPagination(prev => ({
        ...prev,
        totalPages: data.pagination.totalPages,
        totalRecords: data.pagination.totalRecords
      }))

    } catch (error) {
      console.error('Error fetching partner schools:', error)
      setError(error instanceof Error ? error.message : 'Failed to load partner schools')
    } finally {
      setLoading(false)
    }
  }

  const getPaymentStatusBadge = (school: SchoolData) => {
    switch (school.paymentStatus) {
      case 'paid':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Paid
          </Badge>
        )
      case 'pending':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
      case 'overdue':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Overdue ({school.daysOverdue} days)
          </Badge>
        )
      case 'hold_period':
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
            <Pause className="w-3 h-3 mr-1" />
            Hold Period
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getHoldStatusBadge = (school: SchoolData) => {
    switch (school.holdStatus) {
      case 'active':
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
            <Clock className="w-3 h-3 mr-1" />
            Active Hold
          </Badge>
        )
      case 'released':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Released
          </Badge>
        )
      default:
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
            No Hold
          </Badge>
        )
    }
  }

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  if (loading) {
    return <PartnerSchoolsLoadingSkeleton />
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-50 p-6">
        <Alert variant="destructive" className="max-w-2xl mx-auto">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm text-slate-600 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/admin/dashboard')}
              className="p-0 h-auto font-normal"
            >
              Admin Dashboard
            </Button>
            <span>/</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/admin/dashboard?tab=partners')}
              className="p-0 h-auto font-normal"
            >
              Partners
            </Button>
            <span>/</span>
            <span className="text-slate-900 font-medium">School Portfolio</span>
          </div>

          {/* Partner Info Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back</span>
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-slate-900">{partnerData?.name}</h1>
                <div className="flex items-center space-x-4 text-sm text-slate-600 mt-1">
                  <span>{partnerData?.email}</span>
                  <span>•</span>
                  <span>{partnerData?.phone}</span>
                  <span>•</span>
                  <Badge variant="default" className="bg-blue-100 text-blue-800">
                    {partnerData?.profitSharePercentage || 0}% Commission
                  </Badge>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 mt-4 lg:mt-0">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Portfolio
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Portfolio Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-700">Total Schools</p>
                    <p className="text-2xl font-bold text-blue-900">
                      {portfolioSummary?.totalSchools || 0}
                    </p>
                    <p className="text-xs text-blue-600">Active referrals</p>
                  </div>
                  <School className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-700">Monthly Revenue</p>
                    <p className="text-2xl font-bold text-green-900">
                      {formatCurrency(portfolioSummary?.totalMonthlyRevenue || 0)}
                    </p>
                    <p className="text-xs text-green-600">Total from schools</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-700">Pending Payments</p>
                    <p className="text-2xl font-bold text-orange-900">
                      {portfolioSummary?.pendingPayments || 0}
                    </p>
                    <p className="text-xs text-orange-600">Schools pending</p>
                  </div>
                  <Clock className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="border-purple-200 bg-purple-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-700">Schools in Hold</p>
                    <p className="text-2xl font-bold text-purple-900">
                      {portfolioSummary?.schoolsInHold || 0}
                    </p>
                    <p className="text-xs text-purple-600">3-day hold period</p>
                  </div>
                  <Pause className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card className="border-indigo-200 bg-indigo-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-indigo-700">Commission Earned</p>
                    <p className="text-2xl font-bold text-indigo-900">
                      {formatCurrency(portfolioSummary?.totalCommissionEarned || 0)}
                    </p>
                    <p className="text-xs text-indigo-600">Total earned</p>
                  </div>
                  <CreditCard className="w-8 h-8 text-indigo-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Schools Table */}
        <Card>
          <CardHeader>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <CardTitle className="text-xl font-semibold">Partner School Portfolio</CardTitle>
              
              {/* Filters */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                <div className="flex items-center space-x-2">
                  <Search className="w-4 h-4 text-slate-400" />
                  <Input
                    placeholder="Search schools..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-48"
                  />
                </div>
                
                <Select value={paymentStatusFilter} onValueChange={setPaymentStatusFilter}>
                  <SelectTrigger className="w-40">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Payment Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                    <SelectItem value="hold_period">Hold Period</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={holdStatusFilter} onValueChange={setHoldStatusFilter}>
                  <SelectTrigger className="w-40">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Hold Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Hold Status</SelectItem>
                    <SelectItem value="none">No Hold</SelectItem>
                    <SelectItem value="active">Active Hold</SelectItem>
                    <SelectItem value="released">Released</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {schools.length === 0 ? (
              <div className="text-center py-12">
                <School className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">No schools found</h3>
                <p className="text-slate-600">This partner doesn't have any referred schools yet.</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>School Name</TableHead>
                        <TableHead>Monthly Amount</TableHead>
                        <TableHead>Payment Status</TableHead>
                        <TableHead>Last Payment</TableHead>
                        <TableHead>Next Due Date</TableHead>
                        <TableHead>Hold Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {schools.map((school) => (
                        <TableRow key={school.id}>
                          <TableCell className="font-medium">
                            {school.schoolName}
                          </TableCell>
                          <TableCell className="font-semibold">
                            {formatCurrency(school.monthlyAmount)}
                          </TableCell>
                          <TableCell>{getPaymentStatusBadge(school)}</TableCell>
                          <TableCell>
                            {school.lastPaymentDate ? formatDate(school.lastPaymentDate) : '-'}
                          </TableCell>
                          <TableCell>{formatDate(school.nextDueDate)}</TableCell>
                          <TableCell>{getHoldStatusBadge(school)}</TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => window.open(`/admin/clients/${school.id}/billing-history`, '_blank')}
                              >
                                View Details
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                <div className="flex flex-col sm:flex-row items-center justify-between mt-6 space-y-4 sm:space-y-0">
                  <div className="text-sm text-slate-600">
                    Showing {((pagination.currentPage - 1) * pagination.recordsPerPage) + 1} to{' '}
                    {Math.min(pagination.currentPage * pagination.recordsPerPage, pagination.totalRecords)} of{' '}
                    {pagination.totalRecords} schools
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage - 1 }))}
                      disabled={pagination.currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        const pageNum = i + 1
                        return (
                          <Button
                            key={pageNum}
                            variant={pagination.currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => setPagination(prev => ({ ...prev, currentPage: pageNum }))}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage + 1 }))}
                      disabled={pagination.currentPage === pagination.totalPages}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Loading Skeleton Component
function PartnerSchoolsLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <Skeleton className="h-4 w-48 mb-4" />
          <div className="flex items-center space-x-4">
            <Skeleton className="h-8 w-16" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
          </div>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          {Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-32 mb-1" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
