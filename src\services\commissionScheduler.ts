import { db } from '@/src/db'
import {
  partnerCommissions,
  partnerHoldPeriods,
  billingTransactions,
  schoolReferrals,
  partners
} from '@/src/db/schema'
import { eq, and, lte, isNull } from 'drizzle-orm'
import { partnerBillingService } from './partnerBillingService'

export class CommissionScheduler {
  
  /**
   * Process new school payments and calculate commissions
   * This should be called whenever a school payment is confirmed
   */
  async processNewSchoolPayments(): Promise<void> {
    try {
      console.log('🔄 Processing new school payments for commission calculation...')

      // Get all confirmed payments that haven't been processed for commission
      const unprocessedPayments = await db
        .select({
          paymentId: billingTransactions.id,
          schoolId: billingTransactions.clientId,
          paymentAmount: billingTransactions.amount,
          paymentDate: billingTransactions.createdAt,
          partnerId: schoolReferrals.partnerId,
          schoolName: billingTransactions.description // Assuming description contains school name
        })
        .from(billingTransactions)
        .innerJoin(schoolReferrals, eq(schoolReferrals.clientId, billingTransactions.clientId))
        .leftJoin(partnerCommissions, and(
          eq(partnerCommissions.schoolId, billingTransactions.clientId),
          eq(partnerCommissions.paymentDate, billingTransactions.createdAt)
        ))
        .where(and(
          eq(billingTransactions.status, 'completed'),
          isNull(partnerCommissions.id) // Not yet processed for commission
        ))

      console.log(`📊 Found ${unprocessedPayments.length} unprocessed payments`)

      for (const payment of unprocessedPayments) {
        if (payment.partnerId) {
          try {
            await partnerBillingService.processSchoolPayment({
              schoolId: payment.schoolId,
              schoolName: payment.schoolName || 'Unknown School',
              paymentAmount: parseFloat(payment.paymentAmount),
              paymentDate: payment.paymentDate.toISOString(),
              partnerId: payment.partnerId,
              operationalExpenses: 0 // This should come from subscription expenses
            })

            console.log(`✅ Processed commission for payment ${payment.paymentId}`)
          } catch (error) {
            console.error(`❌ Failed to process commission for payment ${payment.paymentId}:`, error)
          }
        }
      }

    } catch (error) {
      console.error('❌ Error processing new school payments:', error)
      throw error
    }
  }

  /**
   * Release expired hold periods and trigger payouts
   * This should run daily to check for expired hold periods
   */
  async releaseExpiredHoldPeriods(): Promise<void> {
    try {
      console.log('🔄 Checking for expired hold periods...')

      const now = new Date()

      // Get all active hold periods that have expired
      const expiredHoldPeriods = await db
        .select({
          commissionId: partnerHoldPeriods.commissionId,
          partnerId: partnerHoldPeriods.partnerId,
          holdAmount: partnerHoldPeriods.holdAmount,
          holdEndDate: partnerHoldPeriods.holdEndDate
        })
        .from(partnerHoldPeriods)
        .innerJoin(partnerCommissions, eq(partnerCommissions.id, partnerHoldPeriods.commissionId))
        .where(and(
          eq(partnerHoldPeriods.holdStatus, 'active'),
          lte(partnerHoldPeriods.holdEndDate, now),
          eq(partnerHoldPeriods.autoReleaseEnabled, true),
          eq(partnerCommissions.adminApprovalStatus, 'pending') // Only auto-release if no manual approval required
        ))

      console.log(`📊 Found ${expiredHoldPeriods.length} expired hold periods`)

      for (const holdPeriod of expiredHoldPeriods) {
        try {
          const result = await partnerBillingService.releaseHoldPeriod(holdPeriod.commissionId)
          
          if (result.success) {
            console.log(`✅ Released hold period for commission ${holdPeriod.commissionId}`)
          } else {
            console.error(`❌ Failed to release hold period for commission ${holdPeriod.commissionId}: ${result.error}`)
          }
        } catch (error) {
          console.error(`❌ Error releasing hold period for commission ${holdPeriod.commissionId}:`, error)
        }
      }

    } catch (error) {
      console.error('❌ Error releasing expired hold periods:', error)
      throw error
    }
  }

  /**
   * Update partner billing summaries
   * This should run daily to ensure all partner billing data is up to date
   */
  async updateAllPartnerBillingSummaries(): Promise<void> {
    try {
      console.log('🔄 Updating all partner billing summaries...')

      // Get all active partners
      const activePartners = await db
        .select({
          id: partners.id,
          name: partners.name
        })
        .from(partners)
        .where(eq(partners.isActive, true))

      console.log(`📊 Found ${activePartners.length} active partners`)

      for (const partner of activePartners) {
        try {
          // This will be implemented in the partnerBillingService
          // await partnerBillingService.updatePartnerBillingSummary(partner.id)
          console.log(`✅ Updated billing summary for partner ${partner.name}`)
        } catch (error) {
          console.error(`❌ Failed to update billing summary for partner ${partner.name}:`, error)
        }
      }

    } catch (error) {
      console.error('❌ Error updating partner billing summaries:', error)
      throw error
    }
  }

  /**
   * Generate monthly TDS reports
   * This should run at the end of each month
   */
  async generateMonthlyTdsReports(): Promise<void> {
    try {
      console.log('🔄 Generating monthly TDS reports...')

      const now = new Date()
      const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`

      // Get all TDS records for the current month
      const monthlyTdsData = await db
        .select({
          partnerId: partnerCommissions.partnerId,
          partnerName: partners.name,
          totalGrossAmount: db.raw('SUM(CAST(gross_amount AS DECIMAL))'),
          totalTdsAmount: db.raw('SUM(CAST(tds_amount AS DECIMAL))'),
          totalNetAmount: db.raw('SUM(CAST(net_amount AS DECIMAL))'),
          transactionCount: db.raw('COUNT(*)')
        })
        .from(partnerCommissions)
        .innerJoin(partners, eq(partners.id, partnerCommissions.partnerId))
        .where(eq(partnerCommissions.paymentMonth, currentMonth))
        .groupBy(partnerCommissions.partnerId, partners.name)

      console.log(`📊 Generated TDS report for ${monthlyTdsData.length} partners`)

      // Here you would typically:
      // 1. Generate PDF reports
      // 2. Send reports to partners
      // 3. Submit TDS returns to tax authorities
      // 4. Update TDS certificate status

    } catch (error) {
      console.error('❌ Error generating monthly TDS reports:', error)
      throw error
    }
  }

  /**
   * Handle Razorpay webhook events
   * This processes payout status updates from Razorpay
   */
  async handleRazorpayWebhook(webhookData: any): Promise<void> {
    try {
      console.log('🔄 Processing Razorpay webhook:', webhookData.event)

      const { event, payload } = webhookData

      switch (event) {
        case 'payout.processed':
          await this.handlePayoutProcessed(payload.payout.entity)
          break
        
        case 'payout.failed':
          await this.handlePayoutFailed(payload.payout.entity)
          break
        
        case 'payout.reversed':
          await this.handlePayoutReversed(payload.payout.entity)
          break
        
        default:
          console.log(`ℹ️ Unhandled webhook event: ${event}`)
      }

    } catch (error) {
      console.error('❌ Error handling Razorpay webhook:', error)
      throw error
    }
  }

  /**
   * Handle successful payout
   */
  private async handlePayoutProcessed(payoutData: any): Promise<void> {
    try {
      const razorpayPayoutId = payoutData.id

      // Update commission status
      await db
        .update(partnerCommissions)
        .set({
          payoutStatus: 'completed',
          updatedAt: new Date()
        })
        .where(eq(partnerCommissions.razorpayPayoutId, razorpayPayoutId))

      // Update payout log
      await db
        .update(razorpayPayoutLogs)
        .set({
          status: 'processed',
          processedAt: new Date(),
          webhookData: payoutData,
          updatedAt: new Date()
        })
        .where(eq(razorpayPayoutLogs.razorpayPayoutId, razorpayPayoutId))

      console.log(`✅ Payout processed successfully: ${razorpayPayoutId}`)

    } catch (error) {
      console.error('❌ Error handling payout processed:', error)
      throw error
    }
  }

  /**
   * Handle failed payout
   */
  private async handlePayoutFailed(payoutData: any): Promise<void> {
    try {
      const razorpayPayoutId = payoutData.id

      // Update commission status
      await db
        .update(partnerCommissions)
        .set({
          payoutStatus: 'failed',
          updatedAt: new Date()
        })
        .where(eq(partnerCommissions.razorpayPayoutId, razorpayPayoutId))

      // Update payout log
      await db
        .update(razorpayPayoutLogs)
        .set({
          status: 'failed',
          failureReason: payoutData.failure_reason || 'Unknown failure',
          webhookData: payoutData,
          updatedAt: new Date()
        })
        .where(eq(razorpayPayoutLogs.razorpayPayoutId, razorpayPayoutId))

      console.log(`❌ Payout failed: ${razorpayPayoutId} - ${payoutData.failure_reason}`)

    } catch (error) {
      console.error('❌ Error handling payout failed:', error)
      throw error
    }
  }

  /**
   * Handle reversed payout
   */
  private async handlePayoutReversed(payoutData: any): Promise<void> {
    try {
      const razorpayPayoutId = payoutData.id

      // Update commission status back to pending
      await db
        .update(partnerCommissions)
        .set({
          payoutStatus: 'pending',
          holdStatus: 'active',
          razorpayPayoutId: null,
          updatedAt: new Date()
        })
        .where(eq(partnerCommissions.razorpayPayoutId, razorpayPayoutId))

      // Update payout log
      await db
        .update(razorpayPayoutLogs)
        .set({
          status: 'reversed',
          webhookData: payoutData,
          updatedAt: new Date()
        })
        .where(eq(razorpayPayoutLogs.razorpayPayoutId, razorpayPayoutId))

      console.log(`🔄 Payout reversed: ${razorpayPayoutId}`)

    } catch (error) {
      console.error('❌ Error handling payout reversed:', error)
      throw error
    }
  }

  /**
   * Run all scheduled tasks
   * This should be called by a cron job or scheduler
   */
  async runScheduledTasks(): Promise<void> {
    try {
      console.log('🚀 Running scheduled commission tasks...')

      // Process new school payments
      await this.processNewSchoolPayments()

      // Release expired hold periods
      await this.releaseExpiredHoldPeriods()

      // Update billing summaries
      await this.updateAllPartnerBillingSummaries()

      console.log('✅ All scheduled commission tasks completed')

    } catch (error) {
      console.error('❌ Error running scheduled tasks:', error)
      throw error
    }
  }
}

export const commissionScheduler = new CommissionScheduler()
