import { Context } from 'hono'
import jwt from 'jsonwebtoken'
import { db } from '@/src/db'
import { adminUsers } from '@/src/db/schema'
import { eq } from 'drizzle-orm'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

interface JWTPayload {
  userId: string
  email: string
  role: string
  iat?: number
  exp?: number
}

// Admin authentication middleware
export const adminAuthMiddleware = async (c: Context, next: () => Promise<void>) => {
  try {
    const authHeader = c.req.header('Authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload
      
      // Verify admin user exists and is active
      const [adminUser] = await db
        .select()
        .from(adminUsers)
        .where(eq(adminUsers.id, decoded.userId))
        .limit(1)

      if (!adminUser || !adminUser.isActive) {
        return c.json({ error: 'Invalid or inactive admin user' }, 401)
      }

      // Add user info to context
      c.set('adminUser', adminUser)
      c.set('userId', decoded.userId)
      
      await next()
    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError)
      return c.json({ error: 'Invalid token' }, 401)
    }
  } catch (error) {
    console.error('Auth middleware error:', error)
    return c.json({ error: 'Authentication failed' }, 500)
  }
}

// Permission-based access control
export const requirePermission = (permission: string) => {
  return async (c: Context, next: () => Promise<void>) => {
    const adminUser = c.get('adminUser')
    
    if (!adminUser) {
      return c.json({ error: 'Admin user not found in context' }, 401)
    }

    // For now, we'll implement basic role-based permissions
    // In the future, this can be expanded to a more granular permission system
    const userRole = adminUser.role || 'admin'
    
    // Define permission mappings
    const permissionMap: Record<string, string[]> = {
      'partners:read': ['admin', 'manager'],
      'partners:write': ['admin'],
      'billing:read': ['admin', 'manager', 'billing'],
      'billing:write': ['admin', 'billing'],
      'clients:read': ['admin', 'manager'],
      'clients:write': ['admin'],
      'analytics:read': ['admin', 'manager'],
      'system:admin': ['admin']
    }

    const allowedRoles = permissionMap[permission] || ['admin']
    
    if (!allowedRoles.includes(userRole)) {
      return c.json({ 
        error: 'Insufficient permissions',
        required: permission,
        userRole 
      }, 403)
    }

    await next()
  }
}

// Partner authentication middleware (for partner portal)
export const partnerAuthMiddleware = async (c: Context, next: () => Promise<void>) => {
  try {
    const authHeader = c.req.header('Authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload
      
      if (decoded.role !== 'partner') {
        return c.json({ error: 'Partner access required' }, 403)
      }

      // Add partner info to context
      c.set('partnerId', decoded.userId)
      c.set('partnerEmail', decoded.email)
      
      await next()
    } catch (jwtError) {
      console.error('Partner JWT verification failed:', jwtError)
      return c.json({ error: 'Invalid token' }, 401)
    }
  } catch (error) {
    console.error('Partner auth middleware error:', error)
    return c.json({ error: 'Authentication failed' }, 500)
  }
}

// Rate limiting middleware
export const rateLimitMiddleware = (maxRequests: number = 100, windowMs: number = 60000) => {
  const requests = new Map<string, { count: number; resetTime: number }>()

  return async (c: Context, next: () => Promise<void>) => {
    const clientId = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const now = Date.now()
    
    const clientData = requests.get(clientId)
    
    if (!clientData || now > clientData.resetTime) {
      requests.set(clientId, { count: 1, resetTime: now + windowMs })
    } else {
      clientData.count++
      
      if (clientData.count > maxRequests) {
        return c.json({ 
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
        }, 429)
      }
    }

    await next()
  }
}

export default {
  adminAuthMiddleware,
  partnerAuthMiddleware,
  requirePermission,
  rateLimitMiddleware
}
