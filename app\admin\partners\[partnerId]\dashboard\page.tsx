'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'

// Components
import PartnerHeader from './components/PartnerHeader'
import PartnerSummaryCards from './components/PartnerSummaryCards'
import PartnerBillingOverview from './components/PartnerBillingOverview'
import PartnerSchoolsList from './components/PartnerSchoolsList'
import PartnerPaymentHistory from './components/PartnerPaymentHistory'

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'

// Utils
import { AuthUtils } from '@/src/utils/authUtils'

// Types
interface PartnerData {
  id: string
  name: string
  email: string
  phone: string
  partnerCode: string
  profitSharePercentage: number
  panCard: string
  city: string
  age: number
  bankAccountHolderName: string
  totalSchools: number
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
}

interface PartnerBillingData {
  pendingAmount: number
  holdAmount: number
  paidAmount: number
  totalEarned: number
  totalTdsDeducted: number
  netPayoutAmount: number
  totalTransactions: number
  successfulPayouts: number
  failedPayouts: number
  lastPayoutDate?: string
}

interface PartnerDashboardData {
  partner: PartnerData
  billing: PartnerBillingData
  recentActivity: any[]
  monthlyEarnings: any[]
}

export default function PartnerDashboardPage() {
  const params = useParams()
  const router = useRouter()
  const partnerId = params.partnerId as string

  // State Management
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [partnerData, setPartnerData] = useState<PartnerDashboardData | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    // Check admin authentication
    if (!AuthUtils.isAuthenticated('admin')) {
      router.push('/admin/login')
      return
    }

    fetchPartnerDashboard()
  }, [partnerId])

  const fetchPartnerDashboard = async () => {
    try {
      setLoading(true)
      setError(null)

      const token = AuthUtils.getToken('admin')
      if (!token) {
        router.push('/admin/login')
        return
      }

      const response = await fetch(`/api/admin/partners/${partnerId}/dashboard`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch partner dashboard data')
      }

      const data = await response.json()
      setPartnerData(data)

    } catch (error) {
      console.error('Error fetching partner dashboard:', error)
      setError(error instanceof Error ? error.message : 'Failed to load partner dashboard')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <PartnerDashboardLoadingSkeleton />
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-50 p-6">
        <Alert variant="destructive" className="max-w-2xl mx-auto">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!partnerData) {
    return (
      <div className="min-h-screen bg-slate-50 p-6">
        <Alert className="max-w-2xl mx-auto">
          <AlertDescription>Partner not found</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Partner Header */}
      <PartnerHeader 
        partner={partnerData.partner}
        onBack={() => router.back()}
        onRefresh={fetchPartnerDashboard}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Summary Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <PartnerSummaryCards billing={partnerData.billing} />
        </motion.div>

        {/* Billing Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <PartnerBillingOverview 
            partner={partnerData.partner}
            billing={partnerData.billing}
          />
        </motion.div>

        {/* Detailed Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Partner Management</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="schools">Referred Schools</TabsTrigger>
                  <TabsTrigger value="payments">Payment History</TabsTrigger>
                  <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>

                <TabsContent value="schools" className="mt-6">
                  <PartnerSchoolsList partnerId={partnerId} />
                </TabsContent>

                <TabsContent value="payments" className="mt-6">
                  <PartnerPaymentHistory 
                    partnerId={partnerId}
                    billing={partnerData.billing}
                  />
                </TabsContent>

                <TabsContent value="analytics" className="mt-6">
                  <div className="text-center py-12">
                    <h3 className="text-lg font-medium text-slate-900 mb-2">Analytics Coming Soon</h3>
                    <p className="text-slate-600">Detailed analytics and reporting will be available here.</p>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

// Loading Skeleton Component
function PartnerDashboardLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header Skeleton */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <Skeleton className="h-4 w-48 mb-4" />
          <div className="flex items-center space-x-4">
            <Skeleton className="h-8 w-16" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Content Skeleton */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Summary Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-32 mb-1" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Billing Overview Skeleton */}
        <Card className="mb-8">
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Skeleton className="h-4 w-32 mb-4" />
                <div className="space-y-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>
              <div>
                <Skeleton className="h-4 w-32 mb-4" />
                <div className="space-y-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Tabs Skeleton */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
