import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/src/db'
import { 
  partners, 
  schoolReferrals, 
  partnerBilling,
  partnerCommissions
} from '@/src/db/schema'
import { eq, desc, count } from 'drizzle-orm'

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { success: false, error: 'Authorization token required' }
  }

  const token = authHeader.substring(7)
  
  try {
    // Here you would verify the JWT token
    // For now, we'll assume the token is valid
    return { success: true, adminId: 'admin-123' }
  } catch (error) {
    return { success: false, error: 'Invalid token' }
  }
}

// GET /api/admin/partners/[id] - Get partner details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    const partnerId = params.id
    console.log(`📊 [Admin] Fetching partner details for: ${partnerId}`)

    // Get partner details
    const [partner] = await db
      .select()
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 })
    }

    // Get school count
    const [schoolCount] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(eq(schoolReferrals.partnerId, partnerId))

    // Get billing information
    const [billing] = await db
      .select()
      .from(partnerBilling)
      .where(eq(partnerBilling.partnerId, partnerId))
      .limit(1)

    // Get recent commissions
    const recentCommissions = await db
      .select({
        id: partnerCommissions.id,
        schoolName: partnerCommissions.schoolName,
        commissionAmount: partnerCommissions.commissionAmount,
        paymentDate: partnerCommissions.paymentDate,
        payoutStatus: partnerCommissions.payoutStatus
      })
      .from(partnerCommissions)
      .where(eq(partnerCommissions.partnerId, partnerId))
      .orderBy(desc(partnerCommissions.createdAt))
      .limit(5)

    // Prepare response
    const partnerDetails = {
      ...partner,
      profitSharePercentage: parseFloat(partner.profitSharePercentage || '0'),
      schoolCount: schoolCount?.count || 0,
      billing: billing ? {
        totalEarned: parseFloat(billing.totalEarned || '0'),
        paidAmount: parseFloat(billing.paidAmount || '0'),
        pendingAmount: parseFloat(billing.pendingAmount || '0'),
        totalTdsDeducted: parseFloat(billing.totalTdsDeducted || '0'),
        netPayoutAmount: parseFloat(billing.netPayoutAmount || '0'),
        totalTransactions: billing.totalTransactions || 0,
        successfulPayouts: billing.successfulPayouts || 0,
        failedPayouts: billing.failedPayouts || 0,
        lastPayoutDate: billing.lastPayoutDate
      } : null,
      recentCommissions: recentCommissions.map(commission => ({
        ...commission,
        commissionAmount: parseFloat(commission.commissionAmount || '0')
      }))
    }

    console.log(`✅ [Admin] Partner details fetched for: ${partner.name}`)

    return NextResponse.json({ partner: partnerDetails })

  } catch (error) {
    console.error('❌ [Admin] Error fetching partner details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch partner details' }, 
      { status: 500 }
    )
  }
}

// PUT /api/admin/partners/[id] - Update partner
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    const partnerId = params.id
    const data = await request.json()

    console.log(`🔄 [Admin] Updating partner: ${partnerId}`)

    // Check if partner exists
    const [existingPartner] = await db
      .select({ id: partners.id })
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!existingPartner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 })
    }

    // Update partner
    const [updatedPartner] = await db
      .update(partners)
      .set({
        name: data.name,
        email: data.email,
        phone: data.phone,
        profitSharePercentage: data.profitSharePercentage?.toString(),
        isActive: data.isActive,
        updatedAt: new Date()
      })
      .where(eq(partners.id, partnerId))
      .returning()

    console.log(`✅ [Admin] Partner updated: ${updatedPartner.name}`)

    return NextResponse.json({ 
      success: true,
      partner: updatedPartner
    })

  } catch (error) {
    console.error('❌ [Admin] Error updating partner:', error)
    return NextResponse.json(
      { error: 'Failed to update partner' }, 
      { status: 500 }
    )
  }
}

// DELETE /api/admin/partners/[id] - Delete partner
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    const partnerId = params.id

    console.log(`🗑️ [Admin] Deleting partner: ${partnerId}`)

    // Check if partner exists
    const [existingPartner] = await db
      .select({ id: partners.id, name: partners.name })
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!existingPartner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 })
    }

    // Soft delete by setting isActive to false
    await db
      .update(partners)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(partners.id, partnerId))

    console.log(`✅ [Admin] Partner deleted: ${existingPartner.name}`)

    return NextResponse.json({ 
      success: true,
      message: 'Partner deleted successfully'
    })

  } catch (error) {
    console.error('❌ [Admin] Error deleting partner:', error)
    return NextResponse.json(
      { error: 'Failed to delete partner' }, 
      { status: 500 }
    )
  }
}
