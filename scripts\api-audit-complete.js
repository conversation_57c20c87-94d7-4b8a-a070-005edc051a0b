/**
 * Comprehensive API Audit Script
 * Checks all frontend API calls against Hono.js backend endpoints
 */

const fs = require('fs')
const path = require('path')

// Frontend API calls found in the codebase
const frontendAPICalls = [
  // Auth endpoints
  '/api/auth/profile',
  '/api/auth/partner/login',
  '/api/auth/school/login',
  
  // School/Profile endpoints
  '/api/school/billing',
  '/api/school/subscription/update-request',
  '/api/school/subscription/update-requests',
  
  // Admin endpoints
  '/api/admin/partners',
  '/api/admin/clients',
  '/api/admin/clients/analytics',
  '/api/admin/financial/overview',
  '/api/admin/software-requests',
  '/api/admin/billing/analytics',
  '/api/admin/dashboard',
  '/api/admin/clients/{clientId}/billing-history',
  '/api/admin/partners/{partnerId}/payments',
  '/api/admin/partners/{partnerId}/schools',
  
  // Partner endpoints
  '/api/partner/profile',
  '/api/partner/clients',
  '/api/partner/billing/dashboard',
  
  // Monitoring endpoints
  '/api/monitoring/dashboard',
  
  // Payment endpoints
  '/api/client-payments/create-order',
  '/api/payments/webhook',
  
  // Webhook endpoints
  '/api/webhooks/razorpay'
]

// Hono.js route files to check
const honoRouteFiles = [
  'app/api/[[...route]]/auth.ts',
  'app/api/[[...route]]/school.ts', 
  'app/api/[[...route]]/admin.ts',
  'app/api/[[...route]]/admin-partners.ts',
  'app/api/[[...route]]/partner.ts',
  'app/api/[[...route]]/monitoring.ts',
  'app/api/[[...route]]/client-payments.ts',
  'app/api/[[...route]]/payments.ts',
  'app/api/[[...route]]/webhooks.ts',
  'app/api/[[...route]]/billing.ts',
  'app/api/[[...route]]/subscriptions.ts'
]

function auditAPIEndpoints() {
  console.log('🔍 COMPREHENSIVE API AUDIT')
  console.log('=' .repeat(60))
  
  const missingEndpoints = []
  const foundEndpoints = []
  
  // Check each frontend API call
  frontendAPICalls.forEach(apiCall => {
    const found = checkEndpointExists(apiCall)
    if (found) {
      foundEndpoints.push({ endpoint: apiCall, file: found })
    } else {
      missingEndpoints.push(apiCall)
    }
  })
  
  // Results
  console.log('\n✅ FOUND ENDPOINTS:')
  foundEndpoints.forEach(({ endpoint, file }) => {
    console.log(`   ${endpoint} → ${file}`)
  })
  
  console.log('\n❌ MISSING ENDPOINTS:')
  missingEndpoints.forEach(endpoint => {
    console.log(`   ${endpoint}`)
  })
  
  console.log('\n📊 SUMMARY:')
  console.log(`   Total API calls: ${frontendAPICalls.length}`)
  console.log(`   Found: ${foundEndpoints.length}`)
  console.log(`   Missing: ${missingEndpoints.length}`)
  console.log(`   Coverage: ${Math.round((foundEndpoints.length / frontendAPICalls.length) * 100)}%`)
  
  if (missingEndpoints.length > 0) {
    console.log('\n🚨 ACTION REQUIRED:')
    console.log('   The following endpoints need to be implemented in Hono.js:')
    missingEndpoints.forEach(endpoint => {
      console.log(`   - ${endpoint}`)
    })
  } else {
    console.log('\n🎉 ALL ENDPOINTS FOUND!')
    console.log('   All frontend API calls have corresponding Hono.js implementations.')
  }
}

function checkEndpointExists(apiCall) {
  // Convert API call to route pattern
  const routePattern = apiCall.replace('/api/', '').replace(/\{[^}]+\}/g, ':id')
  
  for (const routeFile of honoRouteFiles) {
    if (!fs.existsSync(routeFile)) continue
    
    try {
      const content = fs.readFileSync(routeFile, 'utf8')
      
      // Check for various route patterns
      const patterns = [
        `app.get('/${routePattern}'`,
        `app.post('/${routePattern}'`,
        `app.put('/${routePattern}'`,
        `app.delete('/${routePattern}'`,
        `app.get("/${routePattern}"`,
        `app.post("/${routePattern}"`,
        `app.put("/${routePattern}"`,
        `app.delete("/${routePattern}"`,
        // Handle parameter routes
        routePattern.includes(':id') ? routePattern.replace(':id', '/:id') : null,
        // Handle nested routes
        routePattern.split('/').slice(-2).join('/'),
        routePattern.split('/').slice(-1)[0]
      ].filter(Boolean)
      
      for (const pattern of patterns) {
        if (content.includes(pattern)) {
          return routeFile
        }
      }
      
      // Special cases for specific endpoints
      if (apiCall.includes('/auth/profile') && content.includes("app.get('/profile'")) {
        return routeFile
      }
      
      if (apiCall.includes('/partner/login') && content.includes("app.post('/partner/login'")) {
        return routeFile
      }
      
      if (apiCall.includes('/school/billing') && content.includes("app.get('/billing'")) {
        return routeFile
      }
      
    } catch (error) {
      console.error(`Error reading ${routeFile}:`, error.message)
    }
  }
  
  return null
}

// Run the audit
auditAPIEndpoints()

// Additional check for route registration
console.log('\n🔧 ROUTE REGISTRATION CHECK:')
try {
  const mainRouteFile = 'app/api/[[...route]]/route.ts'
  const mainContent = fs.readFileSync(mainRouteFile, 'utf8')
  
  const expectedRoutes = [
    'adminRoutes',
    'schoolRoutes', 
    'partnerRoutes',
    'authRoutes',
    'monitoringRoutes',
    'clientPaymentsRoutes',
    'paymentsRoutes',
    'webhooksRoutes',
    'billingRoutes'
  ]
  
  expectedRoutes.forEach(route => {
    if (mainContent.includes(route)) {
      console.log(`   ✅ ${route} registered`)
    } else {
      console.log(`   ❌ ${route} NOT registered`)
    }
  })
  
} catch (error) {
  console.error('Error checking route registration:', error.message)
}
