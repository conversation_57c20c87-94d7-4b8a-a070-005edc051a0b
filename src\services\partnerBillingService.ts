import { db } from '@/src/db'
import {
  partnerBilling,
  partnerCommissions,
  partnerTdsRecords,
  partnerHoldPeriods,
  razorpayPayoutLogs,
  financialAuditTrail,
  partners,
  clients,
  billingTransactions
} from '@/src/db/schema'
import { eq, and, desc, sum, count, gte, lte } from 'drizzle-orm'

interface SchoolPaymentData {
  schoolId: string
  schoolName: string
  paymentAmount: number
  paymentDate: string
  partnerId: string
  operationalExpenses: number
}

interface PayoutResult {
  success: boolean
  payoutId?: string
  error?: string
  amount?: number
}

export class PartnerBillingService {
  
  /**
   * Process school payment and calculate partner commission
   * This is triggered when a school makes a payment
   */
  async processSchoolPayment(paymentData: SchoolPaymentData): Promise<void> {
    try {
      console.log(`🏫 Processing school payment for ${paymentData.schoolName}: ₹${paymentData.paymentAmount}`)

      // Get partner details
      const [partner] = await db
        .select({
          id: partners.id,
          profitSharePercentage: partners.profitSharePercentage,
          panCard: partners.panCard,
          bankAccountNumber: partners.bankAccountNumber,
          bankIfscCode: partners.bankIfscCode,
          bankAccountHolderName: partners.bankAccountHolderName
        })
        .from(partners)
        .where(eq(partners.id, paymentData.partnerId))
        .limit(1)

      if (!partner) {
        throw new Error('Partner not found')
      }

      // Calculate commission
      const commissionableAmount = paymentData.paymentAmount - paymentData.operationalExpenses
      const commissionPercentage = parseFloat(partner.profitSharePercentage || '0')
      const grossCommission = (commissionableAmount * commissionPercentage) / 100

      // Calculate TDS (5% of gross commission)
      const tdsPercentage = 5.0
      const tdsAmount = (grossCommission * tdsPercentage) / 100
      const netCommission = grossCommission - tdsAmount

      // Get current financial year and quarter
      const paymentDate = new Date(paymentData.paymentDate)
      const financialYear = this.getFinancialYear(paymentDate)
      const quarter = this.getFinancialQuarter(paymentDate)

      // Create commission record
      const [commissionRecord] = await db
        .insert(partnerCommissions)
        .values({
          partnerId: paymentData.partnerId,
          schoolId: paymentData.schoolId,
          schoolName: paymentData.schoolName,
          paymentMonth: `${paymentDate.getFullYear()}-${String(paymentDate.getMonth() + 1).padStart(2, '0')}`,
          paymentDate: paymentData.paymentDate,
          schoolPaymentAmount: paymentData.paymentAmount.toString(),
          operationalExpenses: paymentData.operationalExpenses.toString(),
          commissionableAmount: commissionableAmount.toString(),
          commissionPercentage: commissionPercentage.toString(),
          commissionAmount: grossCommission.toString(),
          holdPeriodStart: new Date(),
          holdPeriodEnd: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
          holdStatus: 'active',
          payoutStatus: 'pending',
          adminApprovalStatus: 'pending'
        })
        .returning()

      // Create TDS record
      await db
        .insert(partnerTdsRecords)
        .values({
          partnerId: paymentData.partnerId,
          commissionId: commissionRecord.id,
          grossAmount: grossCommission.toString(),
          tdsPercentage: tdsPercentage.toString(),
          tdsAmount: tdsAmount.toString(),
          netAmount: netCommission.toString(),
          financialYear,
          quarter,
          panCard: partner.panCard || '',
          deductorPan: process.env.COMPANY_PAN || 'COMPANY123'
        })

      // Create hold period record
      await db
        .insert(partnerHoldPeriods)
        .values({
          partnerId: paymentData.partnerId,
          commissionId: commissionRecord.id,
          schoolId: paymentData.schoolId,
          holdAmount: netCommission.toString(),
          holdStartDate: new Date(),
          holdEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
          holdStatus: 'active',
          autoReleaseEnabled: true
        })

      // Update partner billing summary
      await this.updatePartnerBillingSummary(paymentData.partnerId)

      // Create audit trail
      await this.createAuditTrail({
        transactionType: 'commission_calculated',
        entityType: 'commission',
        entityId: commissionRecord.id,
        partnerId: paymentData.partnerId,
        schoolId: paymentData.schoolId,
        amount: grossCommission,
        description: `Commission calculated for ${paymentData.schoolName} payment of ₹${paymentData.paymentAmount}`
      })

      console.log(`✅ Commission processed: Gross ₹${grossCommission}, TDS ₹${tdsAmount}, Net ₹${netCommission}`)

    } catch (error) {
      console.error('❌ Error processing school payment:', error)
      throw error
    }
  }

  /**
   * Release hold period and trigger payout
   * This runs automatically after 3 days or can be triggered manually
   */
  async releaseHoldPeriod(commissionId: string): Promise<PayoutResult> {
    try {
      console.log(`🔓 Releasing hold period for commission: ${commissionId}`)

      // Get commission details
      const [commission] = await db
        .select()
        .from(partnerCommissions)
        .where(eq(partnerCommissions.id, commissionId))
        .limit(1)

      if (!commission) {
        throw new Error('Commission record not found')
      }

      // Check if hold period has expired
      const now = new Date()
      const holdEndDate = new Date(commission.holdPeriodEnd)
      
      if (now < holdEndDate && commission.adminApprovalStatus !== 'approved') {
        throw new Error('Hold period has not expired and admin approval not received')
      }

      // Get partner bank details
      const [partner] = await db
        .select()
        .from(partners)
        .where(eq(partners.id, commission.partnerId))
        .limit(1)

      if (!partner) {
        throw new Error('Partner not found')
      }

      // Calculate net payout amount (after TDS)
      const [tdsRecord] = await db
        .select()
        .from(partnerTdsRecords)
        .where(eq(partnerTdsRecords.commissionId, commissionId))
        .limit(1)

      const netAmount = parseFloat(tdsRecord?.netAmount || commission.commissionAmount)

      // Trigger Razorpay payout
      const payoutResult = await this.triggerRazorpayPayout({
        partnerId: commission.partnerId,
        amount: netAmount,
        accountNumber: partner.bankAccountNumber || '',
        ifscCode: partner.bankIfscCode || '',
        accountHolderName: partner.bankAccountHolderName || '',
        purpose: `Commission for ${commission.schoolName} - ${commission.paymentMonth}`,
        commissionId
      })

      if (payoutResult.success) {
        // Update commission status
        await db
          .update(partnerCommissions)
          .set({
            holdStatus: 'released',
            payoutStatus: 'processing',
            payoutDate: new Date(),
            payoutAmount: netAmount.toString(),
            razorpayPayoutId: payoutResult.payoutId,
            updatedAt: new Date()
          })
          .where(eq(partnerCommissions.id, commissionId))

        // Update hold period status
        await db
          .update(partnerHoldPeriods)
          .set({
            holdStatus: 'released',
            releasedAt: new Date(),
            releaseReason: 'auto_release',
            updatedAt: new Date()
          })
          .where(eq(partnerHoldPeriods.commissionId, commissionId))

        // Update partner billing summary
        await this.updatePartnerBillingSummary(commission.partnerId)

        // Create audit trail
        await this.createAuditTrail({
          transactionType: 'payout_initiated',
          entityType: 'payout',
          entityId: payoutResult.payoutId || '',
          partnerId: commission.partnerId,
          schoolId: commission.schoolId,
          amount: netAmount,
          description: `Payout initiated for commission ${commissionId}`
        })

        console.log(`✅ Payout initiated: ₹${netAmount} to ${partner.bankAccountHolderName}`)
      }

      return payoutResult

    } catch (error) {
      console.error('❌ Error releasing hold period:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Trigger Razorpay payout
   */
  private async triggerRazorpayPayout(payoutData: {
    partnerId: string
    amount: number
    accountNumber: string
    ifscCode: string
    accountHolderName: string
    purpose: string
    commissionId: string
  }): Promise<PayoutResult> {
    try {
      // This would integrate with Razorpay Payout API
      // For now, we'll simulate the payout process
      
      const payoutId = `pout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      // Log the payout attempt
      await db
        .insert(razorpayPayoutLogs)
        .values({
          commissionId: payoutData.commissionId,
          partnerId: payoutData.partnerId,
          razorpayPayoutId: payoutId,
          amount: payoutData.amount.toString(),
          currency: 'INR',
          status: 'processing',
          mode: 'IMPS',
          purpose: 'commission',
          recipientName: payoutData.accountHolderName,
          recipientAccount: payoutData.accountNumber.slice(-4), // Store only last 4 digits
          recipientIfsc: payoutData.ifscCode,
          razorpayResponse: {
            payout_id: payoutId,
            status: 'processing',
            created_at: new Date().toISOString()
          }
        })

      // Simulate successful payout initiation
      return {
        success: true,
        payoutId,
        amount: payoutData.amount
      }

    } catch (error) {
      console.error('❌ Razorpay payout failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payout failed'
      }
    }
  }

  /**
   * Update partner billing summary
   */
  private async updatePartnerBillingSummary(partnerId: string): Promise<void> {
    try {
      // Get all commission data for the partner
      const commissionSummary = await db
        .select({
          totalEarned: sum(partnerCommissions.commissionAmount),
          pendingAmount: sum(partnerCommissions.commissionAmount),
          holdAmount: sum(partnerCommissions.commissionAmount),
          paidAmount: sum(partnerCommissions.payoutAmount),
          totalTransactions: count()
        })
        .from(partnerCommissions)
        .where(eq(partnerCommissions.partnerId, partnerId))

      // Get TDS summary
      const [tdsSummary] = await db
        .select({
          totalTdsDeducted: sum(partnerTdsRecords.tdsAmount),
          netPayoutAmount: sum(partnerTdsRecords.netAmount)
        })
        .from(partnerTdsRecords)
        .where(eq(partnerTdsRecords.partnerId, partnerId))

      // Update or create billing record
      await db
        .insert(partnerBilling)
        .values({
          partnerId,
          totalEarned: commissionSummary[0]?.totalEarned || '0',
          pendingAmount: '0', // Will be calculated based on status
          holdAmount: '0',    // Will be calculated based on status
          paidAmount: commissionSummary[0]?.paidAmount || '0',
          totalTdsDeducted: tdsSummary?.totalTdsDeducted || '0',
          netPayoutAmount: tdsSummary?.netPayoutAmount || '0',
          totalTransactions: commissionSummary[0]?.totalTransactions || 0,
          lastCalculatedAt: new Date(),
          updatedAt: new Date()
        })
        .onConflictDoUpdate({
          target: partnerBilling.partnerId,
          set: {
            totalEarned: commissionSummary[0]?.totalEarned || '0',
            paidAmount: commissionSummary[0]?.paidAmount || '0',
            totalTdsDeducted: tdsSummary?.totalTdsDeducted || '0',
            netPayoutAmount: tdsSummary?.netPayoutAmount || '0',
            totalTransactions: commissionSummary[0]?.totalTransactions || 0,
            lastCalculatedAt: new Date(),
            updatedAt: new Date()
          }
        })

    } catch (error) {
      console.error('❌ Error updating partner billing summary:', error)
      throw error
    }
  }

  /**
   * Create audit trail record
   */
  private async createAuditTrail(data: {
    transactionType: string
    entityType: string
    entityId: string
    partnerId: string
    schoolId?: string
    amount?: number
    description: string
  }): Promise<void> {
    await db
      .insert(financialAuditTrail)
      .values({
        transactionType: data.transactionType,
        entityType: data.entityType,
        entityId: data.entityId,
        partnerId: data.partnerId,
        schoolId: data.schoolId,
        amount: data.amount?.toString(),
        description: data.description,
        metadata: {
          timestamp: new Date().toISOString(),
          source: 'partner_billing_service'
        }
      })
  }

  /**
   * Get financial year for a given date
   */
  private getFinancialYear(date: Date): string {
    const year = date.getFullYear()
    const month = date.getMonth() + 1 // JavaScript months are 0-indexed
    
    if (month >= 4) {
      return `${year}-${year + 1}`
    } else {
      return `${year - 1}-${year}`
    }
  }

  /**
   * Get financial quarter for a given date
   */
  private getFinancialQuarter(date: Date): string {
    const month = date.getMonth() + 1 // JavaScript months are 0-indexed
    
    if (month >= 4 && month <= 6) return 'Q1'
    if (month >= 7 && month <= 9) return 'Q2'
    if (month >= 10 && month <= 12) return 'Q3'
    return 'Q4' // Jan-Mar
  }
}

export const partnerBillingService = new PartnerBillingService()
