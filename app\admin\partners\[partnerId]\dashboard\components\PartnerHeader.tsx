'use client'

import { ArrowLeft, RefreshCw, User, Phone, Mail, CreditCard, MapPin, Calendar } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'

interface PartnerData {
  id: string
  name: string
  email: string
  phone: string
  partnerCode: string
  profitSharePercentage: number
  panCard: string
  city: string
  age: number
  bankAccountHolderName: string
  totalSchools: number
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
}

interface PartnerHeaderProps {
  partner: PartnerData
  onBack: () => void
  onRefresh: () => void
}

export default function PartnerHeader({ partner, onBack, onRefresh }: PartnerHeaderProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            Active
          </Badge>
        )
      case 'inactive':
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
            Inactive
          </Badge>
        )
      case 'suspended':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
            Suspended
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <div className="bg-white border-b border-slate-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-slate-600 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.open('/admin/dashboard', '_blank')}
            className="p-0 h-auto font-normal"
          >
            Admin Dashboard
          </Button>
          <span>/</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.open('/admin/dashboard?tab=partners', '_blank')}
            className="p-0 h-auto font-normal"
          >
            Partners
          </Button>
          <span>/</span>
          <span className="text-slate-900 font-medium">Partner Dashboard</span>
        </div>

        {/* Partner Header */}
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
          <div className="flex items-start space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={onBack}
              className="flex items-center space-x-2 mt-1"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back</span>
            </Button>
            
            <div className="flex-1">
              {/* Partner Name & Status */}
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-slate-900">{partner.name}</h1>
                {getStatusBadge(partner.status)}
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  {partner.partnerCode}
                </Badge>
              </div>

              {/* Partner Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                {/* Contact Information */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-slate-600">
                    <Mail className="w-4 h-4" />
                    <span>{partner.email}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-slate-600">
                    <Phone className="w-4 h-4" />
                    <span>{partner.phone}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-slate-600">
                    <MapPin className="w-4 h-4" />
                    <span>{partner.city}</span>
                  </div>
                </div>

                {/* Financial Information */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-slate-600">
                    <CreditCard className="w-4 h-4" />
                    <span>PAN: {partner.panCard}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-slate-600">
                    <User className="w-4 h-4" />
                    <span>Age: {partner.age} years</span>
                  </div>
                  <div className="flex items-center space-x-2 text-slate-600">
                    <span className="font-medium">Commission: {partner.profitSharePercentage}%</span>
                  </div>
                </div>

                {/* Account Information */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-slate-600">
                    <span className="font-medium">Bank A/C: {partner.bankAccountHolderName}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-slate-600">
                    <span>Schools: {partner.totalSchools}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-slate-600">
                    <Calendar className="w-4 h-4" />
                    <span>Joined: {formatDate(partner.createdAt)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2 mt-4 lg:mt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/admin/partners/${partner.id}/schools`, '_blank')}
              className="flex items-center space-x-2"
            >
              <User className="w-4 h-4" />
              <span>View Schools</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
