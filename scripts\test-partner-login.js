/**
 * Test Partner Login Flow
 * This script tests the complete partner login and dashboard access flow
 */

const BASE_URL = 'http://localhost:3000'

async function testPartnerLogin() {
  console.log('🧪 TESTING PARTNER LOGIN FLOW')
  console.log('=' .repeat(50))

  try {
    // Step 1: Test partner login
    console.log('\n📝 Step 1: Testing partner login...')
    
    const loginResponse = await fetch(`${BASE_URL}/api/partner/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>', // You'll need to use a real partner email
        password: 'password123'    // You'll need to use a real partner password
      })
    })

    console.log(`Login Response Status: ${loginResponse.status}`)
    
    if (!loginResponse.ok) {
      const errorData = await loginResponse.text()
      console.log('❌ Login failed:', errorData)
      return
    }

    const loginData = await loginResponse.json()
    console.log('✅ Login successful!')
    console.log('Token received:', loginData.token ? 'Yes' : 'No')
    console.log('Partner data:', loginData.partner ? 'Yes' : 'No')

    if (!loginData.token) {
      console.log('❌ No token received from login')
      return
    }

    // Step 2: Test dashboard access
    console.log('\n📊 Step 2: Testing dashboard access...')
    
    const dashboardResponse = await fetch(`${BASE_URL}/api/partner/dashboard`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    })

    console.log(`Dashboard Response Status: ${dashboardResponse.status}`)
    
    if (!dashboardResponse.ok) {
      const errorData = await dashboardResponse.text()
      console.log('❌ Dashboard access failed:', errorData)
      return
    }

    const dashboardData = await dashboardResponse.json()
    console.log('✅ Dashboard access successful!')
    console.log('Dashboard data structure:')
    console.log('- Partner info:', dashboardData.data?.partner ? 'Yes' : 'No')
    console.log('- Performance metrics:', dashboardData.data?.performanceMetrics ? 'Yes' : 'No')
    console.log('- Earnings:', dashboardData.data?.earnings ? 'Yes' : 'No')
    console.log('- Recent activity:', dashboardData.data?.recentActivity ? 'Yes' : 'No')

    // Step 3: Test token validation
    console.log('\n🔐 Step 3: Testing token validation...')
    
    const profileResponse = await fetch(`${BASE_URL}/api/partner/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    })

    console.log(`Profile Response Status: ${profileResponse.status}`)
    
    if (profileResponse.ok) {
      console.log('✅ Token validation successful!')
    } else {
      const errorData = await profileResponse.text()
      console.log('❌ Token validation failed:', errorData)
    }

    console.log('\n🎉 PARTNER LOGIN FLOW TEST COMPLETE!')
    console.log('=' .repeat(50))

  } catch (error) {
    console.error('❌ Test failed with error:', error.message)
  }
}

// Test with different scenarios
async function testLoginScenarios() {
  console.log('\n🔍 TESTING DIFFERENT LOGIN SCENARIOS')
  console.log('=' .repeat(50))

  // Test 1: Invalid credentials
  console.log('\n📝 Test 1: Invalid credentials...')
  try {
    const response = await fetch(`${BASE_URL}/api/partner/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    })
    
    console.log(`Status: ${response.status}`)
    if (response.status === 401) {
      console.log('✅ Correctly rejected invalid credentials')
    } else {
      console.log('❌ Unexpected response for invalid credentials')
    }
  } catch (error) {
    console.log('❌ Error testing invalid credentials:', error.message)
  }

  // Test 2: Missing authorization header
  console.log('\n📝 Test 2: Missing authorization header...')
  try {
    const response = await fetch(`${BASE_URL}/api/partner/dashboard`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    console.log(`Status: ${response.status}`)
    if (response.status === 401) {
      console.log('✅ Correctly rejected missing authorization')
    } else {
      console.log('❌ Unexpected response for missing authorization')
    }
  } catch (error) {
    console.log('❌ Error testing missing authorization:', error.message)
  }

  // Test 3: Invalid token
  console.log('\n📝 Test 3: Invalid token...')
  try {
    const response = await fetch(`${BASE_URL}/api/partner/dashboard`, {
      method: 'GET',
      headers: { 
        'Authorization': 'Bearer invalid-token',
        'Content-Type': 'application/json' 
      }
    })
    
    console.log(`Status: ${response.status}`)
    if (response.status === 401) {
      console.log('✅ Correctly rejected invalid token')
    } else {
      console.log('❌ Unexpected response for invalid token')
    }
  } catch (error) {
    console.log('❌ Error testing invalid token:', error.message)
  }
}

// Run tests
async function runAllTests() {
  await testPartnerLogin()
  await testLoginScenarios()
}

// Check if we can connect to the server first
async function checkServerConnection() {
  try {
    const response = await fetch(`${BASE_URL}/api/health`)
    if (response.ok) {
      console.log('✅ Server is running and accessible')
      return true
    } else {
      console.log('❌ Server responded but with error status:', response.status)
      return false
    }
  } catch (error) {
    console.log('❌ Cannot connect to server:', error.message)
    console.log('💡 Make sure the development server is running with: npm run dev')
    return false
  }
}

// Main execution
async function main() {
  console.log('🚀 PARTNER LOGIN FLOW TESTING SUITE')
  console.log('=' .repeat(60))
  
  const serverOk = await checkServerConnection()
  if (!serverOk) {
    process.exit(1)
  }
  
  await runAllTests()
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { testPartnerLogin, testLoginScenarios }
