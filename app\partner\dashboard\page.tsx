'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AuthUtils } from '@/src/utils/authUtils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/Card'
import { Badge } from '@/src/components/ui/badge'
import { Button } from '@/src/components/ui/Button'
import {
  Users,
  TrendingUp,
  Banknote,
  MessageSquare,
  School,
  ArrowUpRight,
  ArrowDownRight,
  Eye
} from 'lucide-react'
import Link from 'next/link'

interface DashboardData {
  partner: {
    name: string
    email: string
    companyName: string
    partnerCode: string
    referralCode: string
  }
  performanceMetrics: {
    totalReferrals: number
    activeClients: number
    clientsWithSubscriptions: number
    conversionRate: number
    totalMonthlyRevenue: string
    thisMonthReferrals: number
    lastMonthReferrals: number
    referralGrowth: number
  }
  earnings: {
    total: string
    thisMonth: string
    lastMonth: string
    yearToDate: string
    growth: number
  }
  recentActivity: {
    referrals: any[]
    supportTickets: any[]
  }
}

export default function PartnerDashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const router = useRouter()

  useEffect(() => {
    // Check partner authentication using AuthUtils (same as admin)
    if (!AuthUtils.isAuthenticated('partner')) {
      window.location.href = '/partner/login'
      return
    }

    const userData = localStorage.getItem('partner')
    if (!userData) {
      window.location.href = '/partner/login'
      return
    }

    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/partner/dashboard', {
        headers: {
          ...AuthUtils.getAuthHeader('partner')
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          // Clear tokens and redirect (same as admin)
          AuthUtils.removeToken('partner')
          localStorage.removeItem('partner')
          window.location.href = '/partner/login'
          return
        }
        throw new Error('Failed to fetch dashboard data')
      }

      const data = await response.json()
      setDashboardData(data.data)
    } catch (error) {
      console.error('Dashboard error:', error)
      setError('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchDashboardData}>Try Again</Button>
        </div>
      </div>
    )
  }

  if (!dashboardData) return null

  const { partner, performanceMetrics, earnings, recentActivity } = dashboardData

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Welcome back, {partner.name}!</h1>
          <p className="text-gray-600 mt-1">
            {partner.companyName} • Partner Code: <span className="font-mono text-emerald-600">{partner.partnerCode}</span>
          </p>
          <p className="text-gray-600 mt-1">
            Referral Code: <span className="font-mono text-blue-600 font-semibold">{partner.referralCode}</span>
          </p>
        </div>
        <div className="flex gap-3">
          <Link href="/partner/analytics">
            <Button variant="outline">
              <TrendingUp className="w-4 h-4 mr-2" />
              View Analytics
            </Button>
          </Link>
          <Link href="/partner/clients">
            <Button>
              <Users className="w-4 h-4 mr-2" />
              Manage Schools
            </Button>
          </Link>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Referrals</p>
                <p className="text-3xl font-bold text-gray-900">{performanceMetrics.totalReferrals}</p>
                <div className="flex items-center mt-2">
                  {performanceMetrics.referralGrowth >= 0 ? (
                    <ArrowUpRight className="w-4 h-4 text-green-600" />
                  ) : (
                    <ArrowDownRight className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm ml-1 ${performanceMetrics.referralGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.abs(performanceMetrics.referralGrowth)}% this month
                  </span>
                </div>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Schools</p>
                <p className="text-3xl font-bold text-gray-900">{performanceMetrics.activeClients}</p>
                <p className="text-sm text-gray-500 mt-2">
                  {performanceMetrics.conversionRate}% conversion rate
                </p>
              </div>
              <School className="w-8 h-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Commission</p>
                <p className="text-3xl font-bold text-gray-900">₹{performanceMetrics.totalMonthlyRevenue}</p>
                <p className="text-sm text-gray-500 mt-2">
                  From {performanceMetrics.clientsWithSubscriptions} active subscriptions
                </p>
              </div>
              <Banknote className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-3xl font-bold text-gray-900">₹{earnings.total}</p>
                <div className="flex items-center mt-2">
                  {earnings.growth >= 0 ? (
                    <ArrowUpRight className="w-4 h-4 text-green-600" />
                  ) : (
                    <ArrowDownRight className="w-4 h-4 text-red-600" />
                  )}
                  <span className={`text-sm ml-1 ${earnings.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.abs(earnings.growth)}% this month
                  </span>
                </div>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Referrals */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Referrals</CardTitle>
              <Link href="/partner/clients">
                <Button variant="ghost" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  View All
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.referrals.length > 0 ? (
                recentActivity.referrals.map((referral, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{referral.school?.name}</p>
                      <p className="text-sm text-gray-600">
                        {referral.school?.studentCount} students • {referral.school?.status}
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant={referral.subscription?.status === 'active' ? 'default' : 'secondary'}>
                        {referral.subscription?.status || 'No subscription'}
                      </Badge>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(referral.referralDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent referrals</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Support Tickets */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Support Tickets</CardTitle>
              <Link href="/partner/support">
                <Button variant="ghost" size="sm">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  View All
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.supportTickets.length > 0 ? (
                recentActivity.supportTickets.map((ticket) => (
                  <div key={ticket.ticket.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{ticket.ticket.title}</p>
                      <p className="text-sm text-gray-600">{ticket.school?.name}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant={
                        ticket.ticket.status === 'open' ? 'destructive' :
                        ticket.ticket.status === 'in_progress' ? 'default' :
                        'secondary'
                      }>
                        {ticket.ticket.status}
                      </Badge>
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(ticket.ticket.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent tickets</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
