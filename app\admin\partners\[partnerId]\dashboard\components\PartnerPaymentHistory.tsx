'use client'

import { useState, useEffect } from 'react'
import { 
  Credit<PERSON>ard, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  Download,
  Calendar,
  DollarSign
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
import { Skeleton } from '@/components/ui/skeleton'
import { AuthUtils } from '@/src/utils/authUtils'

// Simple Table Components
const Table = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`overflow-hidden rounded-lg border border-slate-200 ${className}`}>
    <table className="w-full border-collapse">{children}</table>
  </div>
)

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead className="bg-slate-50">{children}</thead>
)

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody className="divide-y divide-slate-200">{children}</tbody>
)

const TableRow = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <tr className={`hover:bg-slate-50 ${className}`}>{children}</tr>
)

const TableHead = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <th className={`px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider ${className}`}>
    {children}
  </th>
)

const TableCell = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <td className={`px-6 py-4 whitespace-nowrap text-sm text-slate-900 ${className}`}>
    {children}
  </td>
)

interface PaymentRecord {
  id: string
  schoolName: string
  paymentMonth: string
  grossAmount: number
  tdsAmount: number
  netAmount: number
  payoutStatus: 'pending' | 'processing' | 'completed' | 'failed'
  payoutDate?: string
  razorpayPayoutId?: string
}

interface PartnerBillingData {
  pendingAmount: number
  holdAmount: number
  paidAmount: number
  totalEarned: number
  totalTdsDeducted: number
  netPayoutAmount: number
  totalTransactions: number
  successfulPayouts: number
  failedPayouts: number
  lastPayoutDate?: string
}

interface PartnerPaymentHistoryProps {
  partnerId: string
  billing: PartnerBillingData
}

export default function PartnerPaymentHistory({ partnerId, billing }: PartnerPaymentHistoryProps) {
  const [loading, setLoading] = useState(true)
  const [payments, setPayments] = useState<PaymentRecord[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchPaymentHistory()
  }, [partnerId])

  const fetchPaymentHistory = async () => {
    try {
      setLoading(true)
      setError(null)

      const token = AuthUtils.getToken('admin')
      if (!token) return

      const response = await fetch(`/api/admin/partners/${partnerId}/payments`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch payment history')
      }

      const data = await response.json()
      setPayments(data.payments || [])

    } catch (error) {
      console.error('Error fetching payment history:', error)
      setError(error instanceof Error ? error.message : 'Failed to load payment history')
    } finally {
      setLoading(false)
    }
  }

  const getPayoutStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        )
      case 'processing':
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
            <Clock className="w-3 h-3 mr-1" />
            Processing
          </Badge>
        )
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
      case 'failed':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  const formatMonth = (monthString: string) => {
    const [year, month] = monthString.split('-')
    const date = new Date(parseInt(year), parseInt(month) - 1)
    return date.toLocaleDateString('en-IN', {
      month: 'short',
      year: 'numeric'
    })
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <p className="text-red-600">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchPaymentHistory}
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="w-5 h-5" />
            <span>Payment Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(billing.paidAmount)}
              </div>
              <div className="text-sm text-green-700">Total Paid</div>
              <div className="text-xs text-green-600 mt-1">
                {billing.successfulPayouts} transactions
              </div>
            </div>
            
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {formatCurrency(billing.pendingAmount)}
              </div>
              <div className="text-sm text-yellow-700">Pending</div>
              <div className="text-xs text-yellow-600 mt-1">
                In processing queue
              </div>
            </div>
            
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(billing.totalTdsDeducted)}
              </div>
              <div className="text-sm text-red-700">TDS Deducted</div>
              <div className="text-xs text-red-600 mt-1">
                5% tax deducted
              </div>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-slate-200">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-600">Last Payout:</span>
              <span className="font-medium">{formatDate(billing.lastPayoutDate)}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment History Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="w-5 h-5" />
              <span>Payment History ({payments.length})</span>
            </CardTitle>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {payments.length === 0 ? (
            <div className="text-center py-12">
              <CreditCard className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">No payment history</h3>
              <p className="text-slate-600">Payment records will appear here once commissions are processed.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>School</TableHead>
                    <TableHead>Month</TableHead>
                    <TableHead>Gross Amount</TableHead>
                    <TableHead>TDS Deducted</TableHead>
                    <TableHead>Net Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Payout Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">
                        {payment.schoolName}
                      </TableCell>
                      <TableCell>
                        {formatMonth(payment.paymentMonth)}
                      </TableCell>
                      <TableCell className="font-semibold">
                        {formatCurrency(payment.grossAmount)}
                      </TableCell>
                      <TableCell className="text-red-600">
                        -{formatCurrency(payment.tdsAmount)}
                      </TableCell>
                      <TableCell className="font-semibold text-green-600">
                        {formatCurrency(payment.netAmount)}
                      </TableCell>
                      <TableCell>
                        {getPayoutStatusBadge(payment.payoutStatus)}
                      </TableCell>
                      <TableCell>
                        {formatDate(payment.payoutDate)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {payment.razorpayPayoutId && (
                            <Button variant="outline" size="sm">
                              View Receipt
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
