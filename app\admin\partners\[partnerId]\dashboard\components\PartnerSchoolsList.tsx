'use client'

import { useState, useEffect } from 'react'
import { 
  School, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  Pause,
  ExternalLink
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
import { Skeleton } from '@/components/ui/skeleton'
import { AuthUtils } from '@/src/utils/authUtils'

// Simple Table Components
const Table = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`overflow-hidden rounded-lg border border-slate-200 ${className}`}>
    <table className="w-full border-collapse">{children}</table>
  </div>
)

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead className="bg-slate-50">{children}</thead>
)

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody className="divide-y divide-slate-200">{children}</tbody>
)

const TableRow = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <tr className={`hover:bg-slate-50 ${className}`}>{children}</tr>
)

const TableHead = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <th className={`px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider ${className}`}>
    {children}
  </th>
)

const TableCell = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <td className={`px-6 py-4 whitespace-nowrap text-sm text-slate-900 ${className}`}>
    {children}
  </td>
)

interface SchoolData {
  id: string
  schoolName: string
  monthlyAmount: number
  paymentStatus: 'paid' | 'pending' | 'overdue' | 'hold_period'
  lastPaymentDate?: string
  nextDueDate: string
  commissionEarned: number
  commissionStatus: 'paid' | 'pending' | 'hold'
}

interface PartnerSchoolsListProps {
  partnerId: string
}

export default function PartnerSchoolsList({ partnerId }: PartnerSchoolsListProps) {
  const [loading, setLoading] = useState(true)
  const [schools, setSchools] = useState<SchoolData[]>([])
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchPartnerSchools()
  }, [partnerId])

  const fetchPartnerSchools = async () => {
    try {
      setLoading(true)
      setError(null)

      const token = AuthUtils.getToken('admin')
      if (!token) return

      const response = await fetch(`/api/admin/partners/${partnerId}/schools`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch partner schools')
      }

      const data = await response.json()
      setSchools(data.schools || [])

    } catch (error) {
      console.error('Error fetching partner schools:', error)
      setError(error instanceof Error ? error.message : 'Failed to load schools')
    } finally {
      setLoading(false)
    }
  }

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Paid
          </Badge>
        )
      case 'pending':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
      case 'overdue':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Overdue
          </Badge>
        )
      case 'hold_period':
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
            <Pause className="w-3 h-3 mr-1" />
            Hold Period
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getCommissionStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            Paid
          </Badge>
        )
      case 'pending':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            Pending
          </Badge>
        )
      case 'hold':
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
            Hold
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referred Schools</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Referred Schools</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <p className="text-red-600">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchPartnerSchools}
              className="mt-4"
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <School className="w-5 h-5" />
            <span>Referred Schools ({schools.length})</span>
          </CardTitle>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.open(`/admin/partners/${partnerId}/schools`, '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            View Full Portfolio
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {schools.length === 0 ? (
          <div className="text-center py-12">
            <School className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">No schools found</h3>
            <p className="text-slate-600">This partner hasn't referred any schools yet.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>School Name</TableHead>
                  <TableHead>Monthly Amount</TableHead>
                  <TableHead>Payment Status</TableHead>
                  <TableHead>Commission Earned</TableHead>
                  <TableHead>Commission Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {schools.map((school) => (
                  <TableRow key={school.id}>
                    <TableCell className="font-medium">
                      {school.schoolName}
                    </TableCell>
                    <TableCell className="font-semibold">
                      {formatCurrency(school.monthlyAmount)}
                    </TableCell>
                    <TableCell>
                      {getPaymentStatusBadge(school.paymentStatus)}
                    </TableCell>
                    <TableCell className="font-semibold text-green-600">
                      {formatCurrency(school.commissionEarned)}
                    </TableCell>
                    <TableCell>
                      {getCommissionStatusBadge(school.commissionStatus)}
                    </TableCell>
                    <TableCell>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => window.open(`/admin/clients/${school.id}/billing-history`, '_blank')}
                      >
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
