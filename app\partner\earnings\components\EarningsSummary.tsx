'use client'

import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  CreditCard,
  DollarSign,
  Pause,
  AlertTriangle,
  BarChart3
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/Card'

interface EarningsSummaryProps {
  totalEarnings: number
  pendingAmount: number
  holdAmount: number
  paidAmount: number
  transactionCount: number
  successfulPayouts: number
  failedPayouts: number
}

export default function EarningsSummary({
  totalEarnings,
  pendingAmount,
  holdAmount,
  paidAmount,
  transactionCount,
  successfulPayouts,
  failedPayouts
}: EarningsSummaryProps) {
  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  // Calculate TDS (5% of gross earnings)
  const tdsAmount = totalEarnings * 0.05
  const netAmount = totalEarnings - tdsAmount

  // Calculate success rate
  const calculateSuccessRate = () => {
    if (transactionCount === 0) return 0
    return Math.round((successfulPayouts / transactionCount) * 100)
  }

  const summaryCards = [
    {
      title: 'Total Earnings',
      value: formatCurrency(totalEarnings),
      subtitle: 'Gross earnings',
      icon: TrendingUp,
      color: 'green',
      delay: 0.1
    },
    {
      title: 'Net Payout',
      value: formatCurrency(netAmount),
      subtitle: 'After TDS deduction',
      icon: CreditCard,
      color: 'blue',
      delay: 0.2
    },
    {
      title: 'Pending Amount',
      value: formatCurrency(pendingAmount),
      subtitle: 'In processing',
      icon: Clock,
      color: 'yellow',
      delay: 0.3
    },
    {
      title: 'Hold Amount',
      value: formatCurrency(holdAmount),
      subtitle: '3-day hold period',
      icon: Pause,
      color: 'orange',
      delay: 0.4
    },
    {
      title: 'Paid Amount',
      value: formatCurrency(paidAmount),
      subtitle: 'Successfully transferred',
      icon: CheckCircle,
      color: 'purple',
      delay: 0.5
    },
    {
      title: 'TDS Deducted',
      value: formatCurrency(tdsAmount),
      subtitle: '5% tax deducted',
      icon: DollarSign,
      color: 'red',
      delay: 0.6
    },
    {
      title: 'Transactions',
      value: transactionCount.toString(),
      subtitle: `${successfulPayouts} successful`,
      icon: BarChart3,
      color: 'indigo',
      delay: 0.7
    },
    {
      title: 'Success Rate',
      value: `${calculateSuccessRate()}%`,
      subtitle: `${failedPayouts} failed`,
      icon: failedPayouts > 0 ? AlertTriangle : CheckCircle,
      color: failedPayouts > 0 ? 'red' : 'green',
      delay: 0.8
    }
  ]

  const getCardStyles = (color: string) => {
    const styles = {
      green: 'border-green-200 bg-green-50',
      blue: 'border-blue-200 bg-blue-50',
      yellow: 'border-yellow-200 bg-yellow-50',
      orange: 'border-orange-200 bg-orange-50',
      purple: 'border-purple-200 bg-purple-50',
      red: 'border-red-200 bg-red-50',
      indigo: 'border-indigo-200 bg-indigo-50'
    }
    return styles[color as keyof typeof styles] || 'border-gray-200 bg-gray-50'
  }

  const getIconStyles = (color: string) => {
    const styles = {
      green: 'text-green-600',
      blue: 'text-blue-600',
      yellow: 'text-yellow-600',
      orange: 'text-orange-600',
      purple: 'text-purple-600',
      red: 'text-red-600',
      indigo: 'text-indigo-600'
    }
    return styles[color as keyof typeof styles] || 'text-gray-600'
  }

  const getTextStyles = (color: string) => {
    const styles = {
      green: 'text-green-900',
      blue: 'text-blue-900',
      yellow: 'text-yellow-900',
      orange: 'text-orange-900',
      purple: 'text-purple-900',
      red: 'text-red-900',
      indigo: 'text-indigo-900'
    }
    return styles[color as keyof typeof styles] || 'text-gray-900'
  }

  const getSubtitleStyles = (color: string) => {
    const styles = {
      green: 'text-green-600',
      blue: 'text-blue-600',
      yellow: 'text-yellow-600',
      orange: 'text-orange-600',
      purple: 'text-purple-600',
      red: 'text-red-600',
      indigo: 'text-indigo-600'
    }
    return styles[color as keyof typeof styles] || 'text-gray-600'
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {summaryCards.map((card, index) => {
        const IconComponent = card.icon
        
        return (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: card.delay }}
          >
            <Card className={getCardStyles(card.color)}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${getSubtitleStyles(card.color)}`}>
                      {card.title}
                    </p>
                    <p className={`text-2xl font-bold ${getTextStyles(card.color)} mt-1`}>
                      {card.value}
                    </p>
                    <p className={`text-xs ${getSubtitleStyles(card.color)} mt-1`}>
                      {card.subtitle}
                    </p>
                  </div>
                  <IconComponent className={`w-8 h-8 ${getIconStyles(card.color)}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )
      })}
    </div>
  )
}
