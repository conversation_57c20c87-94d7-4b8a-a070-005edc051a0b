'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  ArrowLeft, 
  Download, 
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  AlertTriangle,
  Mail,
  Send,
  Clock,
  Users,
  CreditCard,
  Calendar,
  CheckSquare,
  Square
} from 'lucide-react'

// UI Components
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
// Simple Checkbox Component
const Checkbox = ({ checked, onCheckedChange }: { checked: boolean, onCheckedChange: (checked: boolean) => void }) => (
  <input
    type="checkbox"
    checked={checked}
    onChange={(e) => onCheckedChange(e.target.checked)}
    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
  />
)

// Utils
import { AuthUtils } from '@/src/utils/authUtils'

// Simple Table Components
const Table = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`overflow-hidden rounded-lg border border-slate-200 ${className}`}>
    <table className="w-full border-collapse">{children}</table>
  </div>
)

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead className="bg-slate-50">{children}</thead>
)

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody className="divide-y divide-slate-200">{children}</tbody>
)

const TableRow = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <tr className={`hover:bg-slate-50 ${className}`}>{children}</tr>
)

const TableHead = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <th className={`px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider ${className}`}>
    {children}
  </th>
)

const TableCell = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <td className={`px-6 py-4 whitespace-nowrap text-sm text-slate-900 ${className}`}>
    {children}
  </td>
)

// Types
interface OverdueSchool {
  id: string
  schoolName: string
  email: string
  phone: string
  monthlyAmount: number
  daysOverdue: number
  lastPaymentDate?: string
  nextDueDate: string
  partnerId: string
  partnerName: string
  partnerEmail: string
  escalationStatus: 'none' | 'first_notice' | 'second_notice' | 'final_notice'
  lastEmailSent?: string
  emailHistory: EmailRecord[]
}

interface EmailRecord {
  id: string
  sentDate: string
  emailType: 'reminder' | 'escalation' | 'final_notice'
  recipient: 'school' | 'partner' | 'both'
  status: 'sent' | 'delivered' | 'opened' | 'failed'
}

interface OverdueSummary {
  totalOverdueSchools: number
  totalOverdueAmount: number
  averageDaysOverdue: number
  partnersAffected: number
}

interface PaginationInfo {
  currentPage: number
  totalPages: number
  totalRecords: number
  recordsPerPage: number
}

export default function OverdueSchoolsPage() {
  const router = useRouter()

  // State Management
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [overdueSchools, setOverdueSchools] = useState<OverdueSchool[]>([])
  const [overdueSummary, setOverdueSummary] = useState<OverdueSummary | null>(null)
  const [selectedSchools, setSelectedSchools] = useState<Set<string>>(new Set())
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    recordsPerPage: 15
  })

  // Filter States
  const [escalationFilter, setEscalationFilter] = useState<string>('all')
  const [daysOverdueFilter, setDaysOverdueFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [sendingEmails, setSendingEmails] = useState(false)

  useEffect(() => {
    // Check admin authentication
    if (!AuthUtils.isAuthenticated('admin')) {
      router.push('/admin/login')
      return
    }

    fetchOverdueSchools()
  }, [pagination.currentPage, escalationFilter, daysOverdueFilter, searchTerm])

  const fetchOverdueSchools = async () => {
    try {
      setLoading(true)
      setError(null)

      const token = AuthUtils.getToken('admin')
      if (!token) {
        router.push('/admin/login')
        return
      }

      const queryParams = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: pagination.recordsPerPage.toString(),
        escalation: escalationFilter,
        daysOverdue: daysOverdueFilter,
        search: searchTerm
      })

      const response = await fetch(`/api/admin/schools/overdue?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch overdue schools')
      }

      const data = await response.json()
      
      setOverdueSchools(data.schools)
      setOverdueSummary(data.summary)
      setPagination(prev => ({
        ...prev,
        totalPages: data.pagination.totalPages,
        totalRecords: data.pagination.totalRecords
      }))

    } catch (error) {
      console.error('Error fetching overdue schools:', error)
      setError(error instanceof Error ? error.message : 'Failed to load overdue schools')
    } finally {
      setLoading(false)
    }
  }

  const handleSelectSchool = (schoolId: string, checked: boolean) => {
    const newSelected = new Set(selectedSchools)
    if (checked) {
      newSelected.add(schoolId)
    } else {
      newSelected.delete(schoolId)
    }
    setSelectedSchools(newSelected)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSchools(new Set(overdueSchools.map(school => school.id)))
    } else {
      setSelectedSchools(new Set())
    }
  }

  const sendBulkEmails = async (emailType: 'reminder' | 'escalation' | 'final_notice') => {
    if (selectedSchools.size === 0) {
      alert('Please select schools to send emails to.')
      return
    }

    try {
      setSendingEmails(true)

      const token = AuthUtils.getToken('admin')
      const response = await fetch('/api/admin/schools/overdue/send-emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          schoolIds: Array.from(selectedSchools),
          emailType
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send emails')
      }

      const result = await response.json()
      alert(`Successfully sent ${result.emailsSent} emails`)
      
      // Refresh data
      fetchOverdueSchools()
      setSelectedSchools(new Set())

    } catch (error) {
      console.error('Error sending emails:', error)
      alert('Failed to send emails. Please try again.')
    } finally {
      setSendingEmails(false)
    }
  }

  const getEscalationBadge = (status: string) => {
    switch (status) {
      case 'first_notice':
        return (
          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Mail className="w-3 h-3 mr-1" />
            First Notice
          </Badge>
        )
      case 'second_notice':
        return (
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
            <Mail className="w-3 h-3 mr-1" />
            Second Notice
          </Badge>
        )
      case 'final_notice':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Final Notice
          </Badge>
        )
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 border-gray-200">
            No Notice
          </Badge>
        )
    }
  }

  const getDaysOverdueBadge = (days: number) => {
    if (days <= 7) {
      return (
        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          {days} days
        </Badge>
      )
    } else if (days <= 30) {
      return (
        <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-200">
          {days} days
        </Badge>
      )
    } else {
      return (
        <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
          {days} days
        </Badge>
      )
    }
  }

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  if (loading) {
    return <OverdueSchoolsLoadingSkeleton />
  }

  if (error) {
    return (
      <div className="min-h-screen bg-slate-50 p-6">
        <Alert variant="destructive" className="max-w-2xl mx-auto">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Breadcrumb */}
          <div className="flex items-center space-x-2 text-sm text-slate-600 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/admin/dashboard')}
              className="p-0 h-auto font-normal"
            >
              Admin Dashboard
            </Button>
            <span>/</span>
            <span className="text-slate-900 font-medium">Overdue Schools</span>
          </div>

          {/* Page Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back</span>
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Overdue Schools Management</h1>
                <p className="text-sm text-slate-600 mt-1">
                  Manage schools with overdue payments and notify partners
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2 mt-4 lg:mt-0">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export Report
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-red-700">Overdue Schools</p>
                    <p className="text-2xl font-bold text-red-900">
                      {overdueSummary?.totalOverdueSchools || 0}
                    </p>
                    <p className="text-xs text-red-600">Need attention</p>
                  </div>
                  <AlertTriangle className="w-8 h-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-700">Overdue Amount</p>
                    <p className="text-2xl font-bold text-orange-900">
                      {formatCurrency(overdueSummary?.totalOverdueAmount || 0)}
                    </p>
                    <p className="text-xs text-orange-600">Total pending</p>
                  </div>
                  <CreditCard className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-700">Avg Days Overdue</p>
                    <p className="text-2xl font-bold text-blue-900">
                      {overdueSummary?.averageDaysOverdue || 0}
                    </p>
                    <p className="text-xs text-blue-600">Average delay</p>
                  </div>
                  <Clock className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="border-purple-200 bg-purple-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-700">Partners Affected</p>
                    <p className="text-2xl font-bold text-purple-900">
                      {overdueSummary?.partnersAffected || 0}
                    </p>
                    <p className="text-xs text-purple-600">Need notification</p>
                  </div>
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Bulk Actions */}
        {selectedSchools.size > 0 && (
          <Card className="mb-6 border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0">
                <div className="flex items-center space-x-2">
                  <CheckSquare className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    {selectedSchools.size} school(s) selected
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    onClick={() => sendBulkEmails('reminder')}
                    disabled={sendingEmails}
                    className="bg-yellow-600 hover:bg-yellow-700"
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Send Reminder
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => sendBulkEmails('escalation')}
                    disabled={sendingEmails}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Send Escalation
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => sendBulkEmails('final_notice')}
                    disabled={sendingEmails}
                    variant="destructive"
                  >
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Final Notice
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Overdue Schools Table */}
        <Card>
          <CardHeader>
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <CardTitle className="text-xl font-semibold">Overdue Schools</CardTitle>
              
              {/* Filters */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                <div className="flex items-center space-x-2">
                  <Search className="w-4 h-4 text-slate-400" />
                  <Input
                    placeholder="Search schools..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-48"
                  />
                </div>
                
                <Select value={escalationFilter} onValueChange={setEscalationFilter}>
                  <SelectTrigger className="w-40">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Escalation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Escalations</SelectItem>
                    <SelectItem value="none">No Notice</SelectItem>
                    <SelectItem value="first_notice">First Notice</SelectItem>
                    <SelectItem value="second_notice">Second Notice</SelectItem>
                    <SelectItem value="final_notice">Final Notice</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={daysOverdueFilter} onValueChange={setDaysOverdueFilter}>
                  <SelectTrigger className="w-40">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Days Overdue" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Days</SelectItem>
                    <SelectItem value="1-7">1-7 days</SelectItem>
                    <SelectItem value="8-30">8-30 days</SelectItem>
                    <SelectItem value="31+">31+ days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {overdueSchools.length === 0 ? (
              <div className="text-center py-12">
                <CheckSquare className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">No overdue schools found</h3>
                <p className="text-slate-600">All schools are up to date with their payments!</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedSchools.size === overdueSchools.length && overdueSchools.length > 0}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>School Name</TableHead>
                        <TableHead>Partner</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Days Overdue</TableHead>
                        <TableHead>Escalation Status</TableHead>
                        <TableHead>Last Email</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {overdueSchools.map((school) => (
                        <TableRow key={school.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedSchools.has(school.id)}
                              onCheckedChange={(checked) => handleSelectSchool(school.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            <div>
                              <div className="font-semibold">{school.schoolName}</div>
                              <div className="text-xs text-slate-500">{school.email}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{school.partnerName}</div>
                              <div className="text-xs text-slate-500">{school.partnerEmail}</div>
                            </div>
                          </TableCell>
                          <TableCell className="font-semibold">
                            {formatCurrency(school.monthlyAmount)}
                          </TableCell>
                          <TableCell>{getDaysOverdueBadge(school.daysOverdue)}</TableCell>
                          <TableCell>{getEscalationBadge(school.escalationStatus)}</TableCell>
                          <TableCell>
                            {school.lastEmailSent ? formatDate(school.lastEmailSent) : '-'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => window.open(`/admin/clients/${school.id}/billing-history`, '_blank')}
                              >
                                View Details
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                <div className="flex flex-col sm:flex-row items-center justify-between mt-6 space-y-4 sm:space-y-0">
                  <div className="text-sm text-slate-600">
                    Showing {((pagination.currentPage - 1) * pagination.recordsPerPage) + 1} to{' '}
                    {Math.min(pagination.currentPage * pagination.recordsPerPage, pagination.totalRecords)} of{' '}
                    {pagination.totalRecords} schools
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage - 1 }))}
                      disabled={pagination.currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        const pageNum = i + 1
                        return (
                          <Button
                            key={pageNum}
                            variant={pagination.currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => setPagination(prev => ({ ...prev, currentPage: pageNum }))}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        )
                      })}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPagination(prev => ({ ...prev, currentPage: prev.currentPage + 1 }))}
                      disabled={pagination.currentPage === pagination.totalPages}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Loading Skeleton Component
function OverdueSchoolsLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-slate-50">
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <Skeleton className="h-4 w-48 mb-4" />
          <div className="flex items-center space-x-4">
            <Skeleton className="h-8 w-16" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
          </div>
        </div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-8 w-32 mb-1" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
