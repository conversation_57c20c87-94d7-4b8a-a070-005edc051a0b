/**
 * Targeted Security Fixes for Critical Issues
 * Focuses on real security vulnerabilities
 */

const fs = require('fs')

// Add security headers to main route file
function addSecurityHeaders() {
  const routeFile = 'app/api/[[...route]]/route.ts'
  
  if (!fs.existsSync(routeFile)) {
    console.log('❌ Route file not found')
    return
  }
  
  let content = fs.readFileSync(routeFile, 'utf8')
  
  // Check if security headers are already added
  if (content.includes('helmet') || content.includes('x-frame-options')) {
    console.log('✅ Security headers already present')
    return
  }
  
  // Add security headers after imports
  const importSection = content.indexOf('const app = new Hono()')
  if (importSection > -1) {
    const beforeApp = content.substring(0, importSection)
    const afterApp = content.substring(importSection)
    
    const securityMiddleware = `
// Security middleware
app.use('*', async (c, next) => {
  // Add security headers
  c.header('X-Frame-Options', 'DENY')
  c.header('X-Content-Type-Options', 'nosniff')
  c.header('X-XSS-Protection', '1; mode=block')
  c.header('Referrer-Policy', 'strict-origin-when-cross-origin')
  c.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  
  await next()
})

// Rate limiting middleware (basic implementation)
const rateLimitMap = new Map()
app.use('*', async (c, next) => {
  const ip = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
  const key = \`\${ip}:\${c.req.path}\`
  const now = Date.now()
  const windowMs = 15 * 60 * 1000 // 15 minutes
  const maxRequests = 100
  
  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
  } else {
    const limit = rateLimitMap.get(key)
    if (now > limit.resetTime) {
      limit.count = 1
      limit.resetTime = now + windowMs
    } else {
      limit.count++
      if (limit.count > maxRequests) {
        return c.json({ error: 'Too many requests' }, 429)
      }
    }
  }
  
  await next()
})

`
    
    const newContent = beforeApp + securityMiddleware + afterApp
    fs.writeFileSync(routeFile, newContent)
    console.log('✅ Security headers and rate limiting added')
  }
}

// Add input validation to routes missing it
function addInputValidation() {
  const files = [
    'app/api/[[...route]]/auth.ts',
    'app/api/[[...route]]/school.ts',
    'app/api/[[...route]]/admin.ts',
    'app/api/[[...route]]/partner.ts'
  ]
  
  files.forEach(file => {
    if (!fs.existsSync(file)) return
    
    let content = fs.readFileSync(file, 'utf8')
    let modified = false
    
    // Add validation for direct req.json() calls
    const directJsonPattern = /const\s+(\w+)\s*=\s*await\s+c\.req\.json\(\)/g
    let match
    while ((match = directJsonPattern.exec(content)) !== null) {
      const varName = match[1]
      const replacement = `const ${varName} = await c.req.json()
    
    // Basic input validation
    if (!${varName} || typeof ${varName} !== 'object') {
      return c.json({ error: 'Invalid request body' }, 400)
    }`
      
      content = content.replace(match[0], replacement)
      modified = true
    }
    
    if (modified) {
      fs.writeFileSync(file, content)
      console.log(`✅ Added input validation to ${file}`)
    }
  })
}

// Add error handling to routes missing it
function addErrorHandling() {
  const files = [
    'app/api/[[...route]]/billing.ts',
    'app/api/[[...route]]/subscriptions.ts',
    'app/api/[[...route]]/webhooks.ts'
  ]
  
  files.forEach(file => {
    if (!fs.existsSync(file)) return
    
    let content = fs.readFileSync(file, 'utf8')
    
    // Check if routes have try-catch blocks
    const routePattern = /app\.(get|post|put|delete)\([^{]+\{([^}]+)\}/g
    let match
    let modified = false
    
    while ((match = routePattern.exec(content)) !== null) {
      const routeContent = match[2]
      if (!routeContent.includes('try') && routeContent.includes('await')) {
        // This route has await but no try-catch
        console.log(`⚠️  Route in ${file} missing error handling: ${match[0].substring(0, 50)}...`)
      }
    }
  })
}

// Fix CORS configuration
function fixCORS() {
  const routeFile = 'app/api/[[...route]]/route.ts'
  
  if (!fs.existsSync(routeFile)) return
  
  let content = fs.readFileSync(routeFile, 'utf8')
  
  // Check for wildcard CORS
  if (content.includes('origin: "*"') || content.includes("origin: '*'")) {
    content = content.replace(/origin:\s*['"]?\*['"]?/g, 'origin: process.env.FRONTEND_URL || "http://localhost:3000"')
    fs.writeFileSync(routeFile, content)
    console.log('✅ Fixed wildcard CORS configuration')
  }
}

// Add JWT token validation improvements
function improveJWTValidation() {
  const authFile = 'app/api/[[...route]]/auth.ts'
  
  if (!fs.existsSync(authFile)) return
  
  let content = fs.readFileSync(authFile, 'utf8')
  
  // Check if JWT verification has proper error handling
  if (content.includes('jwt.verify') && !content.includes('TokenExpiredError')) {
    console.log('⚠️  JWT verification could be improved with better error handling')
  }
}

// Main security fix function
async function applySecurityFixes() {
  console.log('🔒 APPLYING TARGETED SECURITY FIXES')
  console.log('=' .repeat(50))
  
  try {
    addSecurityHeaders()
    addInputValidation()
    addErrorHandling()
    fixCORS()
    improveJWTValidation()
    
    console.log('\n✅ Security fixes applied successfully!')
    console.log('\n📋 REMAINING RECOMMENDATIONS:')
    console.log('   1. Review all environment variables for sensitive data')
    console.log('   2. Implement proper logging and monitoring')
    console.log('   3. Add API documentation with security considerations')
    console.log('   4. Set up automated security scanning in CI/CD')
    console.log('   5. Regular security audits and penetration testing')
    
  } catch (error) {
    console.error('❌ Error applying security fixes:', error)
    process.exit(1)
  }
}

// Run the fixes
applySecurityFixes()
