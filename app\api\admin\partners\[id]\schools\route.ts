import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/src/db'
import { 
  partners, 
  schoolReferrals, 
  clients,
  billingSubscriptions
} from '@/src/db/schema'
import { eq, desc } from 'drizzle-orm'

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { success: false, error: 'Authorization token required' }
  }

  const token = authHeader.substring(7)
  
  try {
    // Here you would verify the JWT token
    // For now, we'll assume the token is valid
    return { success: true, adminId: 'admin-123' }
  } catch (error) {
    return { success: false, error: 'Invalid token' }
  }
}

// GET /api/admin/partners/[id]/schools - Get schools referred by partner
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    const partnerId = params.id
    console.log(`🏫 [Admin] Fetching schools for partner: ${partnerId}`)

    // Check if partner exists
    const [partner] = await db
      .select({ id: partners.id, name: partners.name })
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 })
    }

    // Get schools referred by this partner
    const schoolReferralsList = await db
      .select({
        id: schoolReferrals.id,
        clientId: schoolReferrals.clientId,
        referralDate: schoolReferrals.referralDate,
        status: schoolReferrals.status,
        conversionDate: schoolReferrals.conversionDate,
        // Client details
        schoolName: clients.name,
        schoolEmail: clients.email,
        schoolPhone: clients.phone,
        schoolCity: clients.city,
        schoolState: clients.state
      })
      .from(schoolReferrals)
      .innerJoin(clients, eq(clients.id, schoolReferrals.clientId))
      .where(eq(schoolReferrals.partnerId, partnerId))
      .orderBy(desc(schoolReferrals.referralDate))

    // Get subscription information for each school
    const clientIds = schoolReferralsList.map(school => school.clientId)
    const subscriptions = await db
      .select({
        clientId: billingSubscriptions.clientId,
        status: billingSubscriptions.status,
        monthlyFee: billingSubscriptions.monthlyFee,
        startDate: billingSubscriptions.startDate,
        nextDueDate: billingSubscriptions.nextDueDate
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.clientId, clientIds[0])) // This needs to be fixed for multiple clients

    // Combine data
    const schoolsWithDetails = schoolReferralsList.map(school => {
      const subscription = subscriptions.find(sub => sub.clientId === school.clientId)
      
      return {
        ...school,
        subscription: subscription ? {
          status: subscription.status,
          monthlyFee: parseFloat(subscription.monthlyFee || '0'),
          startDate: subscription.startDate,
          nextDueDate: subscription.nextDueDate
        } : null
      }
    })

    console.log(`✅ [Admin] Schools fetched for partner: ${partner.name} (${schoolsWithDetails.length} schools)`)

    return NextResponse.json({
      partner: {
        id: partner.id,
        name: partner.name
      },
      schools: schoolsWithDetails,
      totalSchools: schoolsWithDetails.length,
      convertedSchools: schoolsWithDetails.filter(school => school.status === 'converted').length,
      activeSubscriptions: schoolsWithDetails.filter(school => school.subscription?.status === 'active').length
    })

  } catch (error) {
    console.error('❌ [Admin] Error fetching partner schools:', error)
    return NextResponse.json(
      { error: 'Failed to fetch partner schools' }, 
      { status: 500 }
    )
  }
}
