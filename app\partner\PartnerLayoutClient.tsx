'use client'

import { But<PERSON> } from "@/src/components/ui/Button";
import {
  Home,
  BarChart3,
  Users,
  MessageSquare,
  Wallet,
  FileText,
  Settings,
  LogOut,
  Handshake
} from "lucide-react";
import Link from "next/link";

interface PartnerLayoutClientProps {
  children: React.ReactNode;
}

export function PartnerLayoutClient({ children }: PartnerLayoutClientProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/partner/dashboard" className="flex items-center space-x-2">
                <Handshake className="h-8 w-8 text-emerald-600" />
                <span className="text-xl font-bold text-gray-900">Schopio</span>
              </Link>
              <span className="ml-4 px-2 py-1 bg-emerald-100 text-emerald-800 text-xs font-medium rounded">
                Partner Portal
              </span>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => {
                  localStorage.removeItem('partnerToken')
                  localStorage.removeItem('partner')
                  window.location.href = '/partner/login'
                }}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-4">
            <div className="space-y-2">
              <Link href="/partner/dashboard">
                <Button variant="ghost" className="w-full justify-start">
                  <Home className="h-4 w-4 mr-3" />
                  Dashboard
                </Button>
              </Link>

              <Link href="/partner/analytics">
                <Button variant="ghost" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-3" />
                  Analytics
                </Button>
              </Link>

              <Link href="/partner/clients">
                <Button variant="ghost" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-3" />
                  Referred Schools
                </Button>
              </Link>

              <Link href="/partner/support">
                <Button variant="ghost" className="w-full justify-start">
                  <MessageSquare className="h-4 w-4 mr-3" />
                  Support Tickets
                </Button>
              </Link>

              <Link href="/partner/earnings">
                <Button variant="ghost" className="w-full justify-start">
                  <Wallet className="h-4 w-4 mr-3" />
                  Earnings & Withdrawals
                </Button>
              </Link>

              <Link href="/partner/reports">
                <Button variant="ghost" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-3" />
                  Reports
                </Button>
              </Link>

              <div className="border-t pt-4 mt-4">
                <Link href="/partner/profile">
                  <Button variant="ghost" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-3" />
                    Profile & Settings
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
