/**
 * Migration script to handle discount data after schema changes
 * This script ensures no discount data is lost during the transition
 * from old discount fields to the new subscriptionDiscounts table
 */

import { db } from '@/src/db'
import {
  billingSubscriptions,
  subscriptionDiscounts,
  clients
} from '@/src/db/schema'
import { eq } from 'drizzle-orm'

interface OldDiscountData {
  subscriptionId: string
  clientName: string
  discountStartDate?: string
  discountReason?: string
  // We'll assume some default values for missing data
}

async function migrateDiscountData() {
  try {
    console.log('🔄 Starting discount data migration...')

    // Since the old fields are already removed, we'll create default discounts
    // for any schools that might have been affected
    
    // Get all active subscriptions that might need discount restoration
    const subscriptions = await db
      .select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        hasActiveDiscount: billingSubscriptions.hasActiveDiscount,
        currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage,
        discountEndDate: billingSubscriptions.discountEndDate
      })
      .from(billingSubscriptions)
      .innerJoin(clients, eq(clients.id, billingSubscriptions.clientId))

    console.log(`📊 Found ${subscriptions.length} subscriptions to check`)

    let migratedCount = 0
    let restoredCount = 0

    for (const subscription of subscriptions) {
      try {
        // Check if subscription already has discounts in the new table
        const [existingDiscount] = await db
          .select()
          .from(subscriptionDiscounts)
          .where(eq(subscriptionDiscounts.subscriptionId, subscription.id))
          .limit(1)

        if (existingDiscount) {
          console.log(`✅ Subscription ${subscription.id} already has discount records`)
          continue
        }

        // If subscription has discount indicators but no records, restore them
        if (subscription.hasActiveDiscount || 
            (subscription.currentDiscountPercentage && parseFloat(subscription.currentDiscountPercentage) > 0)) {
          
          const discountPercentage = parseFloat(subscription.currentDiscountPercentage || '10')
          const startDate = new Date()
          const endDate = subscription.discountEndDate ? new Date(subscription.discountEndDate) : new Date()
          
          // If no end date, assume 6 months from now
          if (!subscription.discountEndDate) {
            endDate.setMonth(endDate.getMonth() + 6)
          }

          // Calculate remaining months
          const monthsRemaining = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30)))

          // Create discount record
          await db
            .insert(subscriptionDiscounts)
            .values({
              subscriptionId: subscription.id,
              discountPercentage: discountPercentage.toString(),
              discountDurationMonths: monthsRemaining,
              startDate: startDate.toISOString().split('T')[0],
              endDate: endDate.toISOString().split('T')[0],
              remainingMonths: monthsRemaining,
              isActive: true,
              reason: 'Migrated from old discount system - please verify details',
              createdBy: 'system-migration'
            })

          restoredCount++
          console.log(`🔄 Restored discount for subscription ${subscription.id}: ${discountPercentage}% for ${monthsRemaining} months`)
        }

        migratedCount++

      } catch (error) {
        console.error(`❌ Error migrating subscription ${subscription.id}:`, error)
      }
    }

    console.log(`✅ Migration completed:`)
    console.log(`   📊 Total subscriptions checked: ${subscriptions.length}`)
    console.log(`   🔄 Subscriptions processed: ${migratedCount}`)
    console.log(`   💰 Discounts restored: ${restoredCount}`)

    // Update any subscriptions that might have inconsistent discount status
    await updateDiscountStatus()

  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  }
}

async function updateDiscountStatus() {
  try {
    console.log('🔄 Updating subscription discount status...')

    // Get all subscriptions with active discounts
    const activeDiscounts = await db
      .select({
        subscriptionId: subscriptionDiscounts.subscriptionId,
        discountPercentage: subscriptionDiscounts.discountPercentage,
        endDate: subscriptionDiscounts.endDate,
        isActive: subscriptionDiscounts.isActive
      })
      .from(subscriptionDiscounts)
      .where(eq(subscriptionDiscounts.isActive, true))

    for (const discount of activeDiscounts) {
      await db
        .update(billingSubscriptions)
        .set({
          hasActiveDiscount: true,
          currentDiscountPercentage: discount.discountPercentage,
          discountEndDate: discount.endDate,
          updatedAt: new Date()
        })
        .where(eq(billingSubscriptions.id, discount.subscriptionId))
    }

    console.log(`✅ Updated ${activeDiscounts.length} subscription discount statuses`)

  } catch (error) {
    console.error('❌ Error updating discount status:', error)
    throw error
  }
}

// Create some sample discounts for testing (optional)
async function createSampleDiscounts() {
  try {
    console.log('🎯 Creating sample discounts for testing...')

    // Get first 3 active subscriptions
    const sampleSubscriptions = await db
      .select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))
      .limit(3)

    const sampleDiscounts = [
      { percentage: 20, months: 6, reason: 'New customer discount' },
      { percentage: 15, months: 3, reason: 'Loyalty discount' },
      { percentage: 25, months: 12, reason: 'Annual subscription discount' }
    ]

    for (let i = 0; i < Math.min(sampleSubscriptions.length, sampleDiscounts.length); i++) {
      const subscription = sampleSubscriptions[i]
      const discountData = sampleDiscounts[i]

      const startDate = new Date()
      const endDate = new Date()
      endDate.setMonth(endDate.getMonth() + discountData.months)

      await db
        .insert(subscriptionDiscounts)
        .values({
          subscriptionId: subscription.id,
          discountPercentage: discountData.percentage.toString(),
          discountDurationMonths: discountData.months,
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          remainingMonths: discountData.months,
          isActive: true,
          reason: discountData.reason,
          createdBy: 'system-sample'
        })

      console.log(`✅ Created sample discount: ${discountData.percentage}% for ${discountData.months} months`)
    }

  } catch (error) {
    console.error('❌ Error creating sample discounts:', error)
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting discount migration process...')
    
    await migrateDiscountData()
    
    // Uncomment to create sample discounts for testing
    // await createSampleDiscounts()
    
    console.log('🎉 Discount migration completed successfully!')
    
  } catch (error) {
    console.error('💥 Migration process failed:', error)
    process.exit(1)
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  main()
}

export { migrateDiscountData, updateDiscountStatus, createSampleDiscounts }
