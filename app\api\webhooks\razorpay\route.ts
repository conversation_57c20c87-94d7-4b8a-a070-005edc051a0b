import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'
import { commissionScheduler } from '@/src/services/commissionScheduler'

const RAZORPAY_WEBHOOK_SECRET = process.env.RAZORPAY_WEBHOOK_SECRET || ''

export async function POST(request: NextRequest) {
  try {
    console.log('🔔 [Webhook] Received Razorpay webhook')

    // Get the raw body
    const body = await request.text()
    const signature = request.headers.get('x-razorpay-signature')

    if (!signature) {
      console.error('❌ [Webhook] Missing Razorpay signature')
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 })
    }

    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', RAZORPAY_WEBHOOK_SECRET)
      .update(body)
      .digest('hex')

    if (signature !== expectedSignature) {
      console.error('❌ [Webhook] Invalid Razorpay signature')
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    // Parse webhook data
    const webhookData = JSON.parse(body)
    console.log(`📨 [Webhook] Processing event: ${webhookData.event}`)

    // Handle different webhook events
    await commissionScheduler.handleRazorpayWebhook(webhookData)

    console.log('✅ [Webhook] Razorpay webhook processed successfully')
    return NextResponse.json({ status: 'success' })

  } catch (error) {
    console.error('❌ [Webhook] Error processing Razorpay webhook:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' }, 
      { status: 500 }
    )
  }
}

// Handle GET requests (for webhook verification)
export async function GET() {
  return NextResponse.json({ 
    message: 'Razorpay webhook endpoint is active',
    timestamp: new Date().toISOString()
  })
}
