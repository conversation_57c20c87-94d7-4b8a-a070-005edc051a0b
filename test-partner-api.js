// Simple test script to verify the partner API endpoint
const testPartnerAPI = async () => {
  try {
    console.log('🧪 Testing Partner API endpoint...')
    
    const response = await fetch('http://localhost:3000/api/admin/partners', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json'
      }
    })
    
    console.log(`📊 Response Status: ${response.status}`)
    console.log(`📊 Response Headers:`, Object.fromEntries(response.headers.entries()))
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ API Response:', data)
    } else {
      const error = await response.text()
      console.log('❌ API Error:', error)
    }
    
  } catch (error) {
    console.error('❌ Network Error:', error)
  }
}

// Run the test if this file is executed directly
if (typeof window === 'undefined') {
  testPartnerAPI()
}

module.exports = { testPartnerAPI }
