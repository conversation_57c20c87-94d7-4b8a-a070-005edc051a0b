/**
 * Final Security Check - Verify fixes and remaining issues
 */

const fs = require('fs')

function checkSecurityImprovements() {
  console.log('🔒 FINAL SECURITY VERIFICATION')
  console.log('=' .repeat(50))
  
  const results = {
    fixed: [],
    remaining: [],
    score: 0
  }
  
  // Check if security headers were added
  const routeFile = 'app/api/[[...route]]/route.ts'
  if (fs.existsSync(routeFile)) {
    const content = fs.readFileSync(routeFile, 'utf8')
    if (content.includes('X-Frame-Options')) {
      results.fixed.push('✅ Security headers added')
      results.score += 20
    } else {
      results.remaining.push('❌ Security headers missing')
    }
    
    if (content.includes('rateLimitMap')) {
      results.fixed.push('✅ Rate limiting implemented')
      results.score += 15
    } else {
      results.remaining.push('❌ Rate limiting missing')
    }
  }
  
  // Check authentication middleware usage
  const apiFiles = [
    'app/api/[[...route]]/school.ts',
    'app/api/[[...route]]/admin.ts',
    'app/api/[[...route]]/partner.ts'
  ]
  
  let authScore = 0
  apiFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8')
      const routes = content.match(/app\.(get|post|put|delete)\s*\(/g) || []
      const authRoutes = content.match(/(requireSchoolRole|adminAuthMiddleware|partnerAuthMiddleware)/g) || []
      
      // Exclude health checks and login routes
      const protectedRoutes = routes.filter(route => 
        !route.includes('health') && 
        !route.includes('login') && 
        !route.includes('register')
      ).length
      
      const authCoverage = authRoutes.length / Math.max(protectedRoutes, 1)
      if (authCoverage > 0.8) {
        results.fixed.push(`✅ ${file} has good auth coverage (${Math.round(authCoverage * 100)}%)`)
        authScore += 10
      } else {
        results.remaining.push(`⚠️  ${file} needs more auth coverage (${Math.round(authCoverage * 100)}%)`)
      }
    }
  })
  
  results.score += authScore
  
  // Check webhook security
  const webhookFile = 'app/api/[[...route]]/webhooks.ts'
  if (fs.existsSync(webhookFile)) {
    const content = fs.readFileSync(webhookFile, 'utf8')
    if (content.includes('signature') && content.includes('Missing signature')) {
      results.fixed.push('✅ Webhook signature verification added')
      results.score += 15
    } else {
      results.remaining.push('❌ Webhook signature verification missing')
    }
  }
  
  // Check for environment variable usage
  const envUsage = checkEnvironmentVariables()
  if (envUsage.good > envUsage.bad) {
    results.fixed.push('✅ Good environment variable usage')
    results.score += 10
  } else {
    results.remaining.push('⚠️  Review environment variable usage')
  }
  
  // Check for SQL injection protection
  const sqlProtection = checkSQLProtection()
  if (sqlProtection) {
    results.fixed.push('✅ Using ORM/Query builder (SQL injection protection)')
    results.score += 20
  } else {
    results.remaining.push('❌ Potential SQL injection vulnerabilities')
  }
  
  return results
}

function checkEnvironmentVariables() {
  const files = [
    'app/api/[[...route]]/route.ts',
    'app/api/[[...route]]/auth.ts',
    'app/api/[[...route]]/webhooks.ts'
  ]
  
  let good = 0
  let bad = 0
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8')
      
      // Count good practices (using process.env)
      const envUsage = (content.match(/process\.env\./g) || []).length
      good += envUsage
      
      // Count bad practices (hardcoded values)
      const hardcoded = (content.match(/['"][a-zA-Z0-9]{20,}['"]/g) || []).length
      bad += hardcoded
    }
  })
  
  return { good, bad }
}

function checkSQLProtection() {
  const files = [
    'app/api/[[...route]]/school.ts',
    'app/api/[[...route]]/admin.ts',
    'app/api/[[...route]]/partner.ts'
  ]
  
  let usingORM = false
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8')
      
      // Check for ORM usage (Drizzle)
      if (content.includes('db.select') || content.includes('db.insert') || content.includes('db.update')) {
        usingORM = true
      }
    }
  })
  
  return usingORM
}

function generateSecurityReport(results) {
  console.log('\n📊 SECURITY IMPROVEMENTS:')
  results.fixed.forEach(fix => console.log(`   ${fix}`))
  
  console.log('\n⚠️  REMAINING ISSUES:')
  results.remaining.forEach(issue => console.log(`   ${issue}`))
  
  console.log(`\n🎯 SECURITY SCORE: ${results.score}/100`)
  
  if (results.score >= 80) {
    console.log('\n🎉 EXCELLENT! Security posture significantly improved.')
  } else if (results.score >= 60) {
    console.log('\n✅ GOOD! Major security issues addressed.')
  } else {
    console.log('\n⚠️  NEEDS IMPROVEMENT! More security work required.')
  }
  
  console.log('\n🔒 SECURITY RECOMMENDATIONS:')
  console.log('   1. ✅ Authentication middleware on protected routes')
  console.log('   2. ✅ Security headers implementation')
  console.log('   3. ✅ Rate limiting for API endpoints')
  console.log('   4. ✅ Webhook signature verification')
  console.log('   5. ✅ ORM usage for SQL injection protection')
  console.log('   6. 🔄 Regular security audits and updates')
  console.log('   7. 🔄 Input validation and sanitization')
  console.log('   8. 🔄 Logging and monitoring implementation')
  console.log('   9. 🔄 HTTPS enforcement in production')
  console.log('   10. 🔄 Regular dependency updates')
  
  return results.score >= 60
}

// Run the final check
const results = checkSecurityImprovements()
const passed = generateSecurityReport(results)

if (passed) {
  console.log('\n🎉 SECURITY AUDIT PASSED!')
  process.exit(0)
} else {
  console.log('\n⚠️  SECURITY AUDIT NEEDS ATTENTION!')
  process.exit(1)
}
