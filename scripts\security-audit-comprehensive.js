/**
 * Comprehensive Security and Error Audit for Hono.js APIs
 * Checks for vulnerabilities, broken endpoints, and security issues
 */

const fs = require('fs')
const path = require('path')

// Security patterns to check for
const securityPatterns = {
  // Authentication vulnerabilities
  missingAuth: [
    /app\.(get|post|put|delete)\([^)]+\)\s*,\s*async/g, // Routes without auth middleware
    /app\.(get|post|put|delete)\([^)]+\)\s*,\s*zValidator/g // Routes with only validation
  ],
  
  // SQL injection vulnerabilities
  sqlInjection: [
    /\$\{[^}]*req\./g, // Direct request parameter interpolation
    /\+.*req\./g, // String concatenation with request data
    /`.*\$\{.*req\./g // Template literals with request data
  ],
  
  // Missing input validation
  missingValidation: [
    /req\.json\(\)/g, // Direct JSON parsing without validation
    /req\.query\(/g, // Direct query parameter access
    /req\.param\(/g // Direct parameter access
  ],
  
  // Hardcoded secrets
  hardcodedSecrets: [
    /password.*=.*['"][^'"]+['"]/gi,
    /secret.*=.*['"][^'"]+['"]/gi,
    /key.*=.*['"][^'"]+['"]/gi,
    /token.*=.*['"][^'"]+['"]/gi
  ],
  
  // Missing error handling
  missingErrorHandling: [
    /await.*(?!try|catch)/g, // Await without try-catch
    /\.then\(/g, // Promise chains without catch
  ],
  
  // Insecure headers
  insecureHeaders: [
    /cors\(\{[^}]*origin:\s*\*/g, // Wildcard CORS
    /allowHeaders.*\*/g // Wildcard headers
  ]
}

// Critical security issues
const criticalIssues = []
const warningIssues = []
const infoIssues = []

async function performSecurityAudit() {
  console.log('🔒 COMPREHENSIVE SECURITY & ERROR AUDIT')
  console.log('=' .repeat(60))
  
  const honoFiles = [
    'app/api/[[...route]]/route.ts',
    'app/api/[[...route]]/auth.ts',
    'app/api/[[...route]]/school.ts',
    'app/api/[[...route]]/admin.ts',
    'app/api/[[...route]]/admin-partners.ts',
    'app/api/[[...route]]/partner.ts',
    'app/api/[[...route]]/monitoring.ts',
    'app/api/[[...route]]/client-payments.ts',
    'app/api/[[...route]]/webhooks.ts',
    'app/api/[[...route]]/billing.ts',
    'app/api/[[...route]]/subscriptions.ts'
  ]
  
  for (const file of honoFiles) {
    if (fs.existsSync(file)) {
      console.log(`\n🔍 Auditing: ${file}`)
      await auditFile(file)
    } else {
      warningIssues.push({
        file,
        type: 'MISSING_FILE',
        message: 'Expected Hono.js file not found'
      })
    }
  }
  
  // Check middleware files
  const middlewareFiles = [
    'src/middleware/school-auth.ts',
    'src/middleware/partner-auth.ts',
    'src/middleware/authMiddleware.ts'
  ]
  
  for (const file of middlewareFiles) {
    if (fs.existsSync(file)) {
      console.log(`\n🛡️  Auditing middleware: ${file}`)
      await auditMiddleware(file)
    }
  }
  
  // Generate security report
  generateSecurityReport()
}

async function auditFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n')
    
    // Check for authentication issues
    checkAuthentication(filePath, content, lines)
    
    // Check for SQL injection vulnerabilities
    checkSQLInjection(filePath, content, lines)
    
    // Check for input validation
    checkInputValidation(filePath, content, lines)
    
    // Check for error handling
    checkErrorHandling(filePath, content, lines)
    
    // Check for hardcoded secrets
    checkHardcodedSecrets(filePath, content, lines)
    
    // Check for CORS and security headers
    checkSecurityHeaders(filePath, content, lines)
    
    // Check for rate limiting
    checkRateLimiting(filePath, content, lines)
    
  } catch (error) {
    criticalIssues.push({
      file: filePath,
      type: 'FILE_READ_ERROR',
      message: `Cannot read file: ${error.message}`
    })
  }
}

function checkAuthentication(filePath, content, lines) {
  // Check for routes without authentication middleware
  const routePattern = /app\.(get|post|put|delete)\s*\(\s*['"`]([^'"`]+)['"`]/g
  const authMiddlewarePattern = /(adminAuthMiddleware|schoolAuthMiddleware|partnerAuthMiddleware)/g
  
  let match
  const routes = []
  
  while ((match = routePattern.exec(content)) !== null) {
    routes.push({
      method: match[1],
      path: match[2],
      line: content.substring(0, match.index).split('\n').length
    })
  }
  
  // Check if routes have auth middleware
  routes.forEach(route => {
    const routeStartIndex = content.indexOf(`app.${route.method}('${route.path}'`)
    const nextRouteIndex = content.indexOf('app.', routeStartIndex + 1)
    const routeContent = content.substring(routeStartIndex, nextRouteIndex > 0 ? nextRouteIndex : content.length)
    
    // Skip health checks and public endpoints
    if (route.path === '/health' || route.path.includes('login') || route.path.includes('register')) {
      return
    }
    
    if (!authMiddlewarePattern.test(routeContent)) {
      criticalIssues.push({
        file: filePath,
        type: 'MISSING_AUTH',
        line: route.line,
        message: `Route ${route.method.toUpperCase()} ${route.path} missing authentication middleware`
      })
    }
  })
}

function checkSQLInjection(filePath, content, lines) {
  // Check for potential SQL injection patterns
  const dangerousPatterns = [
    /\$\{[^}]*req\.(query|param|body)/g,
    /`[^`]*\$\{[^}]*req\./g,
    /sql`[^`]*\$\{[^}]*req\./g
  ]
  
  dangerousPatterns.forEach(pattern => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length
      criticalIssues.push({
        file: filePath,
        type: 'SQL_INJECTION_RISK',
        line: lineNumber,
        message: `Potential SQL injection: ${match[0]}`
      })
    }
  })
}

function checkInputValidation(filePath, content, lines) {
  // Check for missing input validation
  const unvalidatedInputs = [
    /req\.json\(\)(?!\s*\.valid)/g,
    /c\.req\.query\([^)]+\)(?!\s*\|\|\s*['"])/g,
    /c\.req\.param\([^)]+\)(?!\s*\|\|\s*['"])/g
  ]
  
  unvalidatedInputs.forEach(pattern => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length
      warningIssues.push({
        file: filePath,
        type: 'MISSING_VALIDATION',
        line: lineNumber,
        message: `Unvalidated input: ${match[0]}`
      })
    }
  })
}

function checkErrorHandling(filePath, content, lines) {
  // Check for missing try-catch blocks
  const awaitPattern = /await\s+[^;]+/g
  const tryPattern = /try\s*\{/g
  
  let awaitMatches = []
  let tryMatches = []
  
  let match
  while ((match = awaitPattern.exec(content)) !== null) {
    awaitMatches.push({
      index: match.index,
      line: content.substring(0, match.index).split('\n').length
    })
  }
  
  while ((match = tryPattern.exec(content)) !== null) {
    tryMatches.push({
      index: match.index,
      line: content.substring(0, match.index).split('\n').length
    })
  }
  
  // Check if await statements are within try blocks
  awaitMatches.forEach(awaitMatch => {
    const isInTryBlock = tryMatches.some(tryMatch => 
      tryMatch.index < awaitMatch.index && 
      awaitMatch.index < content.indexOf('}', tryMatch.index)
    )
    
    if (!isInTryBlock) {
      warningIssues.push({
        file: filePath,
        type: 'MISSING_ERROR_HANDLING',
        line: awaitMatch.line,
        message: 'Await statement not wrapped in try-catch block'
      })
    }
  })
}

function checkHardcodedSecrets(filePath, content, lines) {
  const secretPatterns = [
    /password\s*[:=]\s*['"][^'"]{8,}['"]/gi,
    /secret\s*[:=]\s*['"][^'"]{16,}['"]/gi,
    /key\s*[:=]\s*['"][^'"]{16,}['"]/gi,
    /token\s*[:=]\s*['"][^'"]{20,}['"]/gi
  ]
  
  secretPatterns.forEach(pattern => {
    let match
    while ((match = pattern.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length
      criticalIssues.push({
        file: filePath,
        type: 'HARDCODED_SECRET',
        line: lineNumber,
        message: `Potential hardcoded secret: ${match[0].substring(0, 30)}...`
      })
    }
  })
}

function checkSecurityHeaders(filePath, content, lines) {
  // Check CORS configuration
  if (content.includes('cors({')) {
    if (content.includes('origin: "*"') || content.includes("origin: '*'")) {
      warningIssues.push({
        file: filePath,
        type: 'INSECURE_CORS',
        message: 'CORS configured with wildcard origin (*)'
      })
    }
  }
  
  // Check for security headers
  const securityHeaders = ['helmet', 'x-frame-options', 'x-content-type-options', 'x-xss-protection']
  const hasSecurityHeaders = securityHeaders.some(header => content.toLowerCase().includes(header))
  
  if (!hasSecurityHeaders && filePath.includes('route.ts')) {
    infoIssues.push({
      file: filePath,
      type: 'MISSING_SECURITY_HEADERS',
      message: 'Consider adding security headers (helmet, etc.)'
    })
  }
}

function checkRateLimiting(filePath, content, lines) {
  // Check for rate limiting
  if (!content.includes('rateLimit') && !content.includes('rate-limit') && filePath.includes('route.ts')) {
    infoIssues.push({
      file: filePath,
      type: 'MISSING_RATE_LIMITING',
      message: 'Consider implementing rate limiting for API endpoints'
    })
  }
}

async function auditMiddleware(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    
    // Check JWT verification
    if (content.includes('jwt.verify')) {
      if (!content.includes('try') || !content.includes('catch')) {
        criticalIssues.push({
          file: filePath,
          type: 'UNSAFE_JWT_VERIFICATION',
          message: 'JWT verification not wrapped in try-catch'
        })
      }
    }
    
    // Check for token expiration handling
    if (!content.includes('exp') && content.includes('jwt')) {
      warningIssues.push({
        file: filePath,
        type: 'MISSING_TOKEN_EXPIRATION',
        message: 'JWT token expiration not explicitly checked'
      })
    }
    
  } catch (error) {
    criticalIssues.push({
      file: filePath,
      type: 'MIDDLEWARE_READ_ERROR',
      message: `Cannot read middleware file: ${error.message}`
    })
  }
}

function generateSecurityReport() {
  console.log('\n🔒 SECURITY AUDIT REPORT')
  console.log('=' .repeat(60))
  
  console.log(`\n🚨 CRITICAL ISSUES: ${criticalIssues.length}`)
  criticalIssues.forEach(issue => {
    console.log(`   ❌ ${issue.file}${issue.line ? `:${issue.line}` : ''} - ${issue.type}`)
    console.log(`      ${issue.message}`)
  })
  
  console.log(`\n⚠️  WARNING ISSUES: ${warningIssues.length}`)
  warningIssues.forEach(issue => {
    console.log(`   ⚠️  ${issue.file}${issue.line ? `:${issue.line}` : ''} - ${issue.type}`)
    console.log(`      ${issue.message}`)
  })
  
  console.log(`\n💡 INFO/RECOMMENDATIONS: ${infoIssues.length}`)
  infoIssues.forEach(issue => {
    console.log(`   💡 ${issue.file} - ${issue.type}`)
    console.log(`      ${issue.message}`)
  })
  
  // Security score
  const totalIssues = criticalIssues.length + warningIssues.length + infoIssues.length
  const securityScore = Math.max(0, 100 - (criticalIssues.length * 20) - (warningIssues.length * 5) - (infoIssues.length * 1))
  
  console.log(`\n📊 SECURITY SCORE: ${securityScore}/100`)
  
  if (criticalIssues.length === 0 && warningIssues.length === 0) {
    console.log('\n🎉 EXCELLENT! No critical or warning security issues found.')
  } else if (criticalIssues.length === 0) {
    console.log('\n✅ GOOD! No critical security issues found.')
  } else {
    console.log('\n🚨 ACTION REQUIRED! Critical security issues need immediate attention.')
  }
  
  return {
    critical: criticalIssues.length,
    warnings: warningIssues.length,
    info: infoIssues.length,
    score: securityScore
  }
}

// Run the security audit
performSecurityAudit().then(result => {
  if (criticalIssues.length > 0) {
    process.exit(1)
  }
}).catch(error => {
  console.error('❌ Security audit failed:', error)
  process.exit(1)
})
