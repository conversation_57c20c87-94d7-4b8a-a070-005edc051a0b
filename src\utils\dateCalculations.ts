/**
 * Date calculation utilities for billing cycles and due dates
 * Handles month-end edge cases, leap years, and varying month lengths
 */

/**
 * Calculate the due date by adding 30 days to the start date
 * Handles month-end edge cases properly
 * 
 * @param startDate - The subscription start date
 * @returns The calculated due date
 */
export function calculateDueDate(startDate: Date): Date {
  const dueDate = new Date(startDate)
  
  // Add 30 days
  dueDate.setDate(dueDate.getDate() + 30)
  
  // Handle month overflow edge cases
  // If we've overflowed into the next month beyond what we expected,
  // adjust to the last day of the intended month
  const expectedMonth = (startDate.getMonth() + 1) % 12
  const expectedYear = startDate.getFullYear() + Math.floor((startDate.getMonth() + 1) / 12)
  
  if (dueDate.getMonth() !== expectedMonth || dueDate.getFullYear() !== expectedYear) {
    // We've overflowed, so set to the last day of the expected month
    const lastDayOfExpectedMonth = new Date(expectedYear, expectedMonth + 1, 0)
    return lastDayOfExpectedMonth
  }
  
  return dueDate
}

/**
 * Calculate the next billing date based on billing cycle
 * 
 * @param currentDate - Current billing date
 * @param billingCycle - 'monthly' or 'yearly'
 * @returns The next billing date
 */
export function calculateNextBillingDate(currentDate: Date, billingCycle: 'monthly' | 'yearly'): Date {
  const nextDate = new Date(currentDate)
  
  if (billingCycle === 'yearly') {
    // Add one year
    nextDate.setFullYear(nextDate.getFullYear() + 1)
    
    // Handle leap year edge case for Feb 29
    if (currentDate.getMonth() === 1 && currentDate.getDate() === 29) {
      // If original date was Feb 29 and next year is not a leap year
      if (!isLeapYear(nextDate.getFullYear())) {
        nextDate.setDate(28) // Set to Feb 28
      }
    }
  } else {
    // Monthly billing - add one month
    const currentDay = currentDate.getDate()
    nextDate.setMonth(nextDate.getMonth() + 1)
    
    // Handle month-end edge cases
    const daysInNextMonth = getDaysInMonth(nextDate.getFullYear(), nextDate.getMonth())
    if (currentDay > daysInNextMonth) {
      // If the current day doesn't exist in the next month, use the last day
      nextDate.setDate(daysInNextMonth)
    } else {
      nextDate.setDate(currentDay)
    }
  }
  
  return nextDate
}

/**
 * Calculate billing period end date
 * 
 * @param startDate - Period start date
 * @param billingCycle - 'monthly' or 'yearly'
 * @returns The period end date (day before next billing date)
 */
export function calculatePeriodEndDate(startDate: Date, billingCycle: 'monthly' | 'yearly'): Date {
  const nextBillingDate = calculateNextBillingDate(startDate, billingCycle)
  const periodEndDate = new Date(nextBillingDate)
  periodEndDate.setDate(periodEndDate.getDate() - 1)
  return periodEndDate
}

/**
 * Check if a year is a leap year
 * 
 * @param year - The year to check
 * @returns True if the year is a leap year
 */
export function isLeapYear(year: number): boolean {
  return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
}

/**
 * Get the number of days in a specific month
 * 
 * @param year - The year
 * @param month - The month (0-11, where 0 is January)
 * @returns Number of days in the month
 */
export function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate()
}

/**
 * Calculate grace period end date
 * 
 * @param dueDate - The original due date
 * @param gracePeriodDays - Number of grace period days
 * @returns The grace period end date
 */
export function calculateGracePeriodEndDate(dueDate: Date, gracePeriodDays: number): Date {
  const gracePeriodEndDate = new Date(dueDate)
  gracePeriodEndDate.setDate(gracePeriodEndDate.getDate() + gracePeriodDays)
  return gracePeriodEndDate
}

/**
 * Calculate penalty start date (day after grace period ends)
 * 
 * @param dueDate - The original due date
 * @param gracePeriodDays - Number of grace period days
 * @returns The penalty start date
 */
export function calculatePenaltyStartDate(dueDate: Date, gracePeriodDays: number): Date {
  const penaltyStartDate = calculateGracePeriodEndDate(dueDate, gracePeriodDays)
  penaltyStartDate.setDate(penaltyStartDate.getDate() + 1)
  return penaltyStartDate
}

/**
 * Calculate the number of days between two dates
 * 
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Number of days between the dates
 */
export function daysBetween(startDate: Date, endDate: Date): number {
  const timeDifference = endDate.getTime() - startDate.getTime()
  return Math.ceil(timeDifference / (1000 * 3600 * 24))
}

/**
 * Check if a date is overdue based on due date and grace period
 * 
 * @param dueDate - The due date
 * @param gracePeriodDays - Number of grace period days
 * @param currentDate - Current date (defaults to now)
 * @returns True if the payment is overdue
 */
export function isOverdue(dueDate: Date, gracePeriodDays: number, currentDate: Date = new Date()): boolean {
  const gracePeriodEndDate = calculateGracePeriodEndDate(dueDate, gracePeriodDays)
  return currentDate > gracePeriodEndDate
}

/**
 * Format date for database storage (YYYY-MM-DD)
 * 
 * @param date - The date to format
 * @returns Formatted date string
 */
export function formatDateForDB(date: Date): string {
  return date.toISOString().split('T')[0]
}

/**
 * Parse date from database format
 * 
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Parsed Date object
 */
export function parseDateFromDB(dateString: string): Date {
  return new Date(dateString + 'T00:00:00.000Z')
}

/**
 * Calculate subscription renewal date with proper month-end handling
 * 
 * @param subscriptionStartDate - Original subscription start date
 * @param billingCycle - 'monthly' or 'yearly'
 * @param periodsElapsed - Number of billing periods that have elapsed
 * @returns The renewal date
 */
export function calculateRenewalDate(
  subscriptionStartDate: Date, 
  billingCycle: 'monthly' | 'yearly', 
  periodsElapsed: number
): Date {
  let renewalDate = new Date(subscriptionStartDate)
  
  for (let i = 0; i < periodsElapsed; i++) {
    renewalDate = calculateNextBillingDate(renewalDate, billingCycle)
  }
  
  return renewalDate
}

/**
 * Get billing cycle information for a subscription
 * 
 * @param startDate - Subscription start date
 * @param billingCycle - 'monthly' or 'yearly'
 * @returns Object with current period info
 */
export function getBillingCycleInfo(startDate: Date, billingCycle: 'monthly' | 'yearly') {
  const currentDate = new Date()
  const nextBillingDate = calculateNextBillingDate(startDate, billingCycle)
  const periodEndDate = calculatePeriodEndDate(startDate, billingCycle)
  const daysUntilNextBilling = daysBetween(currentDate, nextBillingDate)
  const daysInCurrentPeriod = daysBetween(startDate, nextBillingDate)
  const daysElapsedInPeriod = daysBetween(startDate, currentDate)
  
  return {
    currentPeriodStart: startDate,
    currentPeriodEnd: periodEndDate,
    nextBillingDate,
    daysUntilNextBilling: Math.max(0, daysUntilNextBilling),
    daysInCurrentPeriod,
    daysElapsedInPeriod: Math.min(daysElapsedInPeriod, daysInCurrentPeriod),
    progressPercentage: Math.min(100, (daysElapsedInPeriod / daysInCurrentPeriod) * 100)
  }
}
