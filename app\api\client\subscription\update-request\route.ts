import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/src/db'
import { 
  softwareRequests,
  clients
} from '@/src/db/schema'
import { eq } from 'drizzle-orm'
import jwt from 'jsonwebtoken'

// Helper function to verify client authentication
async function verifyClientAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { success: false, error: 'Authorization token required' }
  }

  const token = authHeader.substring(7)
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any
    return { success: true, clientId: decoded.clientId }
  } catch (error) {
    return { success: false, error: 'Invalid token' }
  }
}

// POST /api/client/subscription/update-request - Submit subscription update request
export async function POST(request: NextRequest) {
  try {
    // Verify client authentication
    const authResult = await verifyClientAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    console.log('📝 [Client] Submitting subscription update request')

    const data = await request.json()
    
    // Validate required fields
    const requiredFields = ['studentCount', 'facultyCount', 'averageMonthlyFee', 'completeAddress', 'contactNumber', 'primaryEmail']
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json({ error: `Missing required field: ${field}` }, { status: 400 })
      }
    }

    // Get client information
    const [client] = await db
      .select({
        id: clients.id,
        name: clients.name,
        email: clients.email
      })
      .from(clients)
      .where(eq(clients.id, authResult.clientId))
      .limit(1)

    if (!client) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 })
    }

    // Create software request for subscription update
    const [newRequest] = await db
      .insert(softwareRequests)
      .values({
        schoolName: client.name,
        email: client.email,
        requestType: 'production',
        studentCount: data.studentCount,
        facultyCount: data.facultyCount,
        completeAddress: data.completeAddress,
        contactNumber: data.contactNumber,
        primaryEmail: data.primaryEmail,
        averageMonthlyFee: data.averageMonthlyFee,
        status: 'pending',
        notes: `Subscription Update Request: ${data.notes || 'No additional notes'}`,
        clientId: authResult.clientId,
        // Legacy fields for backward compatibility
        class1Fee: data.averageMonthlyFee,
        class4Fee: data.averageMonthlyFee,
        class6Fee: data.averageMonthlyFee,
        class10Fee: data.averageMonthlyFee,
        class1112Fee: data.averageMonthlyFee,
        termsAccepted: true,
        termsVersion: '1.0'
      })
      .returning()

    console.log(`✅ [Client] Subscription update request created: ${newRequest.id}`)

    // Send notification email (optional - implement if needed)
    // await sendSubscriptionUpdateNotification(client, data)

    return NextResponse.json({ 
      success: true,
      requestId: newRequest.id,
      message: 'Subscription update request submitted successfully. Our team will review and process your request.'
    })

  } catch (error) {
    console.error('❌ [Client] Error submitting subscription update request:', error)
    return NextResponse.json(
      { error: 'Failed to submit subscription update request' }, 
      { status: 500 }
    )
  }
}

// GET /api/client/subscription/update-request - Get client's subscription update requests
export async function GET(request: NextRequest) {
  try {
    // Verify client authentication
    const authResult = await verifyClientAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    console.log('📊 [Client] Fetching subscription update requests')

    // Get client's software requests (which include subscription updates)
    const requests = await db
      .select({
        id: softwareRequests.id,
        requestType: softwareRequests.requestType,
        studentCount: softwareRequests.studentCount,
        facultyCount: softwareRequests.facultyCount,
        averageMonthlyFee: softwareRequests.averageMonthlyFee,
        completeAddress: softwareRequests.completeAddress,
        contactNumber: softwareRequests.contactNumber,
        primaryEmail: softwareRequests.primaryEmail,
        status: softwareRequests.status,
        notes: softwareRequests.notes,
        createdAt: softwareRequests.createdAt,
        updatedAt: softwareRequests.updatedAt
      })
      .from(softwareRequests)
      .where(eq(softwareRequests.clientId, authResult.clientId))
      .orderBy(softwareRequests.createdAt)

    console.log(`✅ [Client] Found ${requests.length} subscription requests`)

    return NextResponse.json({
      requests: requests.map(request => ({
        ...request,
        studentCount: request.studentCount || 0,
        facultyCount: request.facultyCount || 0,
        averageMonthlyFee: parseFloat(request.averageMonthlyFee || '0')
      }))
    })

  } catch (error) {
    console.error('❌ [Client] Error fetching subscription update requests:', error)
    return NextResponse.json(
      { error: 'Failed to fetch subscription update requests' }, 
      { status: 500 }
    )
  }
}
