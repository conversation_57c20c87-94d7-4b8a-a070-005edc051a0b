/**
 * Debug Partner Login Issue
 * This script helps debug why partner login is not working
 */

const jwt = require('jsonwebtoken')

// Test token generation and validation
function testTokenGeneration() {
  console.log('🔐 TESTING TOKEN GENERATION & VALIDATION')
  console.log('=' .repeat(50))

  const partnerId = 'test-partner-id'
  const email = '<EMAIL>'
  const secret = process.env.JWT_SECRET || 'fallback-secret-key'

  // Generate token (same as generatePartnerAuthToken)
  const token = jwt.sign(
    { 
      userId: partnerId, 
      email,
      type: 'partner'
    },
    secret,
    { 
      expiresIn: '7d',
      issuer: 'schopio-partner',
      audience: 'schopio-partner-portal'
    }
  )

  console.log('✅ Token generated successfully')
  console.log('Token length:', token.length)

  // Validate token (same as partnerAuthMiddleware)
  try {
    const decoded = jwt.verify(token, secret, {
      algorithms: ['HS256'],
      issuer: 'schopio-partner',
      audience: 'schopio-partner-portal'
    })

    console.log('✅ Token validation successful')
    console.log('Decoded payload:', {
      userId: decoded.userId,
      email: decoded.email,
      type: decoded.type,
      iss: decoded.iss,
      aud: decoded.aud
    })

    // Check if validation logic matches
    if (!decoded.userId || !decoded.email || decoded.type !== 'partner') {
      console.log('❌ Token validation would fail in middleware')
      console.log('Missing userId:', !decoded.userId)
      console.log('Missing email:', !decoded.email)
      console.log('Wrong type:', decoded.type !== 'partner')
    } else {
      console.log('✅ Token would pass middleware validation')
    }

  } catch (error) {
    console.log('❌ Token validation failed:', error.message)
  }
}

// Test actual login API
async function testLoginAPI() {
  console.log('\n🌐 TESTING LOGIN API')
  console.log('=' .repeat(50))

  try {
    // Test with dummy credentials first
    console.log('📝 Testing login API endpoint...')
    
    const response = await fetch('http://localhost:3000/api/partner/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    })

    console.log('Response status:', response.status)
    
    if (response.status === 401) {
      console.log('✅ API endpoint is working (correctly rejected invalid credentials)')
    } else if (response.status === 404) {
      console.log('❌ API endpoint not found - check route mounting')
    } else {
      console.log('⚠️ Unexpected response status')
    }

    const responseText = await response.text()
    console.log('Response body:', responseText)

  } catch (error) {
    console.log('❌ Failed to connect to API:', error.message)
    console.log('💡 Make sure the development server is running')
  }
}

// Test browser localStorage simulation
function testLocalStorageFlow() {
  console.log('\n💾 TESTING LOCALSTORAGE FLOW')
  console.log('=' .repeat(50))

  // Simulate what happens in the browser
  const mockToken = 'test-token-123'
  const mockPartner = { id: '1', name: 'Test Partner', email: '<EMAIL>' }

  console.log('📝 Simulating localStorage operations...')
  
  // This is what the login page does
  console.log('1. Setting partnerToken:', mockToken)
  console.log('2. Setting partner data:', JSON.stringify(mockPartner))
  
  // This is what the dashboard checks
  const retrievedToken = mockToken // localStorage.getItem('partnerToken')
  const retrievedPartner = JSON.stringify(mockPartner) // localStorage.getItem('partner')
  
  console.log('3. Retrieved token:', retrievedToken ? 'Found' : 'Not found')
  console.log('4. Retrieved partner:', retrievedPartner ? 'Found' : 'Not found')
  
  if (retrievedToken && retrievedPartner) {
    console.log('✅ localStorage flow would work correctly')
  } else {
    console.log('❌ localStorage flow has issues')
  }
}

// Check for common issues
function checkCommonIssues() {
  console.log('\n🔍 CHECKING COMMON ISSUES')
  console.log('=' .repeat(50))

  // Check environment variables
  console.log('📝 Environment variables:')
  console.log('- JWT_SECRET:', process.env.JWT_SECRET ? 'Set' : 'Not set (using fallback)')
  console.log('- NODE_ENV:', process.env.NODE_ENV || 'Not set')

  // Check for potential issues
  const issues = []
  
  if (!process.env.JWT_SECRET) {
    issues.push('JWT_SECRET not set in environment variables')
  }
  
  if (issues.length > 0) {
    console.log('\n⚠️ Potential issues found:')
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`)
    })
  } else {
    console.log('\n✅ No obvious configuration issues found')
  }
}

// Main execution
async function main() {
  console.log('🚀 PARTNER LOGIN DEBUG SUITE')
  console.log('=' .repeat(60))
  
  testTokenGeneration()
  await testLoginAPI()
  testLocalStorageFlow()
  checkCommonIssues()
  
  console.log('\n📋 DEBUGGING CHECKLIST:')
  console.log('1. Check browser console for JavaScript errors')
  console.log('2. Check Network tab for failed API calls')
  console.log('3. Verify partner exists in database with correct password')
  console.log('4. Check if localStorage is being set correctly')
  console.log('5. Verify redirect is not being blocked by browser')
  
  console.log('\n💡 NEXT STEPS:')
  console.log('1. Open browser console and try logging in')
  console.log('2. Check for any error messages in console')
  console.log('3. Verify the API calls are reaching the server')
  console.log('4. Check if token is being stored in localStorage')
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { testTokenGeneration, testLoginAPI, testLocalStorageFlow }
