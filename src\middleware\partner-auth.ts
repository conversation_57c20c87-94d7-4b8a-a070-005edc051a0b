import { Context } from 'hono'
import jwt from 'jsonwebtoken'
import { db } from '@/src/db'
import { partners } from '@/src/db/schema'
import { eq } from 'drizzle-orm'
import { auditLogger } from '@/src/services/auditLogger'

// Generate request ID compatible with both Node.js and Edge Runtime
function generateRequestId(): string {
  return 'req_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

export interface PartnerUser {
  id: string
  email: string
  name: string
  companyName: string
  partnerCode: string
  isActive: boolean
  permissions: string[]
}

// Get current partner from context
export const getCurrentPartner = (c: Context): PartnerUser | null => {
  return c.get('partner') || null
}

// Partner authentication middleware
export const partnerAuthMiddleware = async (c: Context, next: () => Promise<void>) => {
  const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
  const userAgent = c.req.header('user-agent') || 'unknown'
  const requestId = generateRequestId()

  try {
    const authHeader = c.req.header('Authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ 
        error: 'Authorization token required',
        requestId 
      }, 401)
    }

    const token = authHeader.substring(7)

    // Enhanced JWT verification
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key', {
      algorithms: ['HS256'],
      issuer: 'schopio-partner',
      audience: 'schopio-partner-portal'
    }) as any

    if (!decoded.userId || !decoded.email || decoded.type !== 'partner') {
      await auditLogger.logAuth('failed_login', {
        email: decoded.email || 'unknown',
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid partner token format'
      })

      return c.json({
        error: 'Invalid partner token',
        requestId
      }, 401)
    }

    // Fetch partner from database to ensure they still exist and are active
    const [partner] = await db
      .select()
      .from(partners)
      .where(eq(partners.id, decoded.userId))
      .limit(1)

    if (!partner) {
      await auditLogger.logAuth('failed_login', {
        email: decoded.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Partner not found'
      })

      return c.json({
        error: 'Partner not found',
        requestId
      }, 401)
    }

    if (!partner.isActive) {
      await auditLogger.logAuth('failed_login', {
        email: partner.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Partner account deactivated'
      })

      return c.json({
        error: 'Partner account is deactivated',
        requestId
      }, 403)
    }

    // Add partner to context
    c.set('partner', {
      id: partner.id,
      email: partner.email,
      name: partner.name,
      companyName: partner.companyName,
      partnerCode: partner.partnerCode,
      isActive: partner.isActive
    } as PartnerUser)

    await next()
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      await auditLogger.logAuth('failed_login', {
        email: 'unknown',
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid JWT token'
      })

      return c.json({
        error: 'Invalid token',
        requestId
      }, 401)
    }
    
    if (error instanceof jwt.TokenExpiredError) {
      await auditLogger.logAuth('failed_login', {
        email: 'unknown',
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'JWT token expired'
      })

      return c.json({
        error: 'Token expired',
        requestId
      }, 401)
    }
    
    console.error('Partner auth middleware error:', error)
    return c.json({
      error: 'Authentication failed',
      requestId
    }, 500)
  }
}

// Partner permission middleware
export const requirePartnerPermission = (permission: string) => {
  return async (c: Context, next: () => Promise<void>) => {
    const partner = getCurrentPartner(c)
    
    if (!partner) {
      return c.json({ error: 'Partner authentication required' }, 401)
    }

    if (!partner.permissions.includes(permission) && !partner.permissions.includes('all')) {
      return c.json({ 
        error: 'Insufficient permissions',
        required: permission,
        current: partner.permissions
      }, 403)
    }

    await next()
  }
}

// Helper to generate partner JWT token
export const generatePartnerAuthToken = (partnerId: string, email: string): string => {
  return jwt.sign(
    { 
      userId: partnerId, 
      email,
      type: 'partner'
    },
    process.env.JWT_SECRET || 'fallback-secret-key',
    { 
      expiresIn: '7d',
      issuer: 'schopio-partner',
      audience: 'schopio-partner-portal'
    }
  )
}

// Security headers middleware for partner portal
export const partnerSecurityHeaders = async (c: Context, next: () => Promise<void>) => {
  await next()
  
  // Add security headers
  c.header('X-Content-Type-Options', 'nosniff')
  c.header('X-Frame-Options', 'DENY')
  c.header('X-XSS-Protection', '1; mode=block')
  c.header('Referrer-Policy', 'strict-origin-when-cross-origin')
  c.header('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
}

// Rate limiting for partner API endpoints
export const partnerRateLimit = async (_c: Context, next: () => Promise<void>) => {
  // TODO: Implement rate limiting logic
  // For now, just pass through
  await next()
}

// Ensure partner data access (similar to school data access)
export const ensurePartnerDataAccess = async (c: Context, next: () => Promise<void>) => {
  const partner = getCurrentPartner(c)
  
  if (!partner) {
    return c.json({ error: 'Partner authentication required' }, 401)
  }

  // Add partner ID to context for data filtering
  c.set('partnerId', partner.id)
  
  await next()
}
