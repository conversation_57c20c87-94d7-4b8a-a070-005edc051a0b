/**
 * Payment Gateway Factory
 * Automatically switches between real Razorpay and mock service
 * based on environment configuration
 */

import { RazorpayService } from './razorpayService'
import { MockRazorpayService } from './mockRazorpayService'

interface PaymentGatewayConfig {
  keyId: string
  keySecret: string
  webhookSecret: string
}

class PaymentGatewayFactory {
  private static instance: RazorpayService | MockRazorpayService | null = null

  /**
   * Get payment service instance (singleton)
   */
  static getInstance(): RazorpayService | MockRazorpayService {
    if (!this.instance) {
      this.instance = this.createPaymentService()
    }
    return this.instance
  }

  /**
   * Create appropriate payment service based on environment
   */
  private static createPaymentService(): RazorpayService | MockRazorpayService {
    const config = this.getPaymentConfig()

    // Check if we have valid Razorpay credentials
    if (this.hasValidRazorpayCredentials(config)) {
      console.log('🔑 Using Real Razorpay Service with valid credentials')
      console.log(`   Key ID: ${config.keyId}`)
      console.log(`   Environment: ${config.keyId.includes('test') ? 'TEST' : 'LIVE'}`)
      console.log('   ✅ Ready for real payment processing')
      return new RazorpayService(config)
    } else {
      console.log('🎭 Using Mock Razorpay Service (no valid credentials found)')
      console.log('💡 To use real Razorpay:')
      console.log('   1. Get credentials from https://dashboard.razorpay.com/')
      console.log('   2. Update .env.local with valid keys')
      console.log('   3. Run: node scripts/setup-real-razorpay.js --test')
      return new MockRazorpayService(config.keySecret || 'mock_secret')
    }
  }

  /**
   * Get payment configuration from environment
   */
  private static getPaymentConfig(): PaymentGatewayConfig {
    return {
      keyId: process.env.RAZORPAY_KEY_ID || '',
      keySecret: process.env.RAZORPAY_KEY_SECRET || '',
      webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET || ''
    }
  }

  /**
   * Check if Razorpay credentials are valid (not placeholders)
   */
  private static hasValidRazorpayCredentials(config: PaymentGatewayConfig): boolean {
    const { keyId, keySecret } = config

    // Check for empty values
    if (!keyId || !keySecret) {
      return false
    }

    // Check for placeholder values
    const placeholderPatterns = [
      'rzp_test_1234567890',
      'your_razorpay_key_secret',
      'your_webhook_secret',
      'rzp_test_XXXXXXXXXXXXXX',
      'rzp_live_XXXXXXXXXXXXXX',
      'XXXXXXXXXXXXXXXXXXXXXXXX',
      'test_key',
      'placeholder',
      'dummy'
    ]

    const isPlaceholder = placeholderPatterns.some(pattern =>
      keyId.includes(pattern) || keySecret.includes(pattern)
    )

    if (isPlaceholder) {
      return false
    }

    // Check for proper Razorpay key format
    const validKeyIdPattern = /^rzp_(test|live)_[A-Za-z0-9]{14}$/
    const validKeySecretPattern = /^[A-Za-z0-9]{24}$/

    const isValidKeyId = validKeyIdPattern.test(keyId)
    const isValidKeySecret = validKeySecretPattern.test(keySecret)

    if (!isValidKeyId || !isValidKeySecret) {
      console.log('⚠️  Invalid Razorpay credential format:')
      console.log(`   Key ID format: ${isValidKeyId ? '✅' : '❌'} (should be rzp_test_XXXXXXXXXXXXXX)`)
      console.log(`   Secret format: ${isValidKeySecret ? '✅' : '❌'} (should be 24 characters)`)
      return false
    }

    return true
  }

  /**
   * Force refresh the service instance (useful after credential updates)
   */
  static refresh(): void {
    console.log('🔄 Refreshing payment service instance...')
    this.instance = null
    // Get new instance to trigger re-evaluation
    const newInstance = this.getInstance()
    console.log(`   New service type: ${newInstance instanceof MockRazorpayService ? 'Mock' : 'Real'}`)
  }

  /**
   * Check if currently using mock service
   */
  static isMockService(): boolean {
    const service = this.getInstance()
    return service instanceof MockRazorpayService
  }

  /**
   * Get service type for logging/debugging
   */
  static getServiceType(): 'real' | 'mock' {
    return this.isMockService() ? 'mock' : 'real'
  }

  /**
   * Validate current credentials without creating service
   */
  static validateCurrentCredentials(): {
    isValid: boolean
    keyId: string
    environment: 'test' | 'live' | 'invalid'
    issues: string[]
  } {
    const config = this.getPaymentConfig()
    const issues: string[] = []

    // Check key ID format
    const keyIdPattern = /^rzp_(test|live)_[A-Za-z0-9]{14}$/
    if (!keyIdPattern.test(config.keyId)) {
      issues.push('Invalid Key ID format')
    }

    // Check secret format
    const keySecretPattern = /^[A-Za-z0-9]{24}$/
    if (!keySecretPattern.test(config.keySecret)) {
      issues.push('Invalid Key Secret format')
    }

    // Determine environment
    let environment: 'test' | 'live' | 'invalid' = 'invalid'
    if (config.keyId.startsWith('rzp_test_')) {
      environment = 'test'
    } else if (config.keyId.startsWith('rzp_live_')) {
      environment = 'live'
    }

    return {
      isValid: issues.length === 0,
      keyId: config.keyId,
      environment,
      issues
    }
  }
}

// Export the factory and a convenience function
export { PaymentGatewayFactory }

/**
 * Convenience function to get payment service
 */
export const getPaymentService = () => PaymentGatewayFactory.getInstance()

/**
 * Check if using mock service
 */
export const isUsingMockPayments = () => PaymentGatewayFactory.isMockService()

/**
 * Check if using real Razorpay
 */
export const isUsingRealRazorpay = () => !PaymentGatewayFactory.isMockService()

/**
 * Get payment service type
 */
export const getPaymentServiceType = () => PaymentGatewayFactory.getServiceType()

/**
 * Validate current Razorpay credentials
 */
export const validateRazorpayCredentials = () => PaymentGatewayFactory.validateCurrentCredentials()

/**
 * Refresh payment service (useful after credential updates)
 */
export const refreshPaymentService = () => PaymentGatewayFactory.refresh()
