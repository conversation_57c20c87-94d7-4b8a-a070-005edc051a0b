import { Hono } from "hono"
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { db } from '@/src/db'
import {
  clients,
  subscriptions,
  billingSubscriptions,
  billingInvoices,
  billingPayments,
  supportTickets,
  ticketMessages,
  clientUsers,
  schoolReferrals,
  softwareRequests
} from '@/src/db/schema'
import { eq, and, desc, count, sum, gte, lte } from 'drizzle-orm'
import {
  schoolAuthMiddleware,
  requireSchoolRole,
  ensureSchoolDataAccess,
  schoolSecurityHeaders,
  getCurrentSchoolUser,
  type SchoolUser
} from '@/src/middleware/school-auth'
import { getPaymentService, isUsingMockPayments } from '@/src/services/paymentGatewayFactory'
import { pdfInvoiceService } from '@/src/services/pdfInvoiceService'
import { supportNotificationService } from '@/src/services/supportNotificationService'
import { SubscriptionBillingCalculator } from '@/src/services/subscriptionBillingCalculator'
import { commissionProcessor } from '@/src/services/commissionProcessor'

// Create Hono app for school portal routes
const app = new Hono()

// Apply security headers to all school routes
app.use('*', schoolSecurityHeaders)

// Health check for school routes (no auth required)
app.get("/health", (c) => {
  return c.json({
    status: "ok",
    service: "School Portal API",
    timestamp: new Date().toISOString()
  })
})

// Apply authentication middleware to all protected routes
app.use('*', schoolAuthMiddleware)
app.use('*', ensureSchoolDataAccess)

// School Dashboard - Main overview with subscription status, billing info, and key metrics
app.get("/dashboard", requireSchoolRole(['admin', 'billing', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId

    // Fetch school/client information
    const [client] = await db
      .select()
      .from(clients)
      .where(eq(clients.id, clientId))
      .limit(1)

    if (!client) {
      return c.json({
        success: false,
        error: 'School information not found'
      }, 404)
    }

    // Fetch current subscription from billingSubscriptions table
    const [subscription] = await db
      .select({
        id: billingSubscriptions.id,
        studentCount: billingSubscriptions.studentCount,
        pricePerStudent: billingSubscriptions.pricePerStudent,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        billingCycle: billingSubscriptions.billingCycle,
        status: billingSubscriptions.status,
        currentPeriodStart: billingSubscriptions.currentPeriodStart,
        nextBillingDate: billingSubscriptions.nextBillingDate,
        autoRenew: billingSubscriptions.autoRenew,
        gracePeriodDays: billingSubscriptions.gracePeriodDays
      })
      .from(billingSubscriptions)
      .where(and(
        eq(billingSubscriptions.clientId, clientId),
        eq(billingSubscriptions.status, 'active')
      ))
      .orderBy(desc(billingSubscriptions.createdAt))
      .limit(1)

    // Fetch recent billing cycles (last 3 months)
    const recentBillingCycles = subscription ? await db
      .select()
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscription.id))
      .orderBy(desc(billingSubscriptions.currentPeriodStart))
      .limit(3) : []

    // Fetch pending invoices
    const pendingInvoices = await db
      .select()
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.clientId, clientId),
        eq(billingInvoices.status, 'sent')
      ))
      .orderBy(desc(billingInvoices.issuedDate))

    // Fetch recent payments (last 5)
    const recentPayments = await db
      .select({
        payment: billingPayments,
        invoice: billingInvoices
      })
      .from(billingPayments)
      .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
      .where(eq(billingPayments.clientId, clientId))
      .orderBy(desc(billingPayments.createdAt))
      .limit(5)

    // Calculate dashboard metrics
    const totalPendingAmount = pendingInvoices.reduce((sum, invoice) =>
      sum + parseFloat(invoice.totalAmount.toString()), 0
    )

    const currentMonthStart = new Date()
    currentMonthStart.setDate(1)
    currentMonthStart.setHours(0, 0, 0, 0)

    const currentMonthEnd = new Date(currentMonthStart)
    currentMonthEnd.setMonth(currentMonthEnd.getMonth() + 1)
    currentMonthEnd.setDate(0)
    currentMonthEnd.setHours(23, 59, 59, 999)

    // Get current month's billing cycle
    const [currentBillingCycle] = recentBillingCycles.filter(cycle => {
      const cycleStart = new Date(cycle.currentPeriodStart)
      return cycleStart >= currentMonthStart && cycleStart <= currentMonthEnd
    })

    return c.json({
      success: true,
      data: {
        school: {
          id: client.id,
          name: client.schoolName,
          code: client.schoolCode,
          email: client.email,
          phone: client.phone,
          contactPerson: client.contactPerson,
          studentCount: client.actualStudentCount,
          estimatedStudentCount: client.estimatedStudentCount,
          averageMonthlyFee: client.averageMonthlyFee,
          status: client.status,
          createdAt: client.createdAt
        },
        subscription: subscription ? {
          id: subscription.id,
          planName: 'Basic Plan', // Default plan name for billingSubscriptions
          studentCount: subscription.studentCount,
          pricePerStudent: subscription.pricePerStudent,
          monthlyAmount: subscription.monthlyAmount,
          billingCycle: subscription.billingCycle,
          status: subscription.status,
          startDate: subscription.currentPeriodStart, // Use currentPeriodStart as startDate
          nextBillingDate: subscription.nextBillingDate,
          autoRenew: subscription.autoRenew,
          gracePeriodDays: subscription.gracePeriodDays
        } : null,
        billing: {
          currentCycle: currentBillingCycle ? {
            id: currentBillingCycle.id,
            currentPeriodStart: currentBillingCycle.currentPeriodStart,
            currentPeriodEnd: currentBillingCycle.currentPeriodEnd,
            studentCount: currentBillingCycle.studentCount,
            monthlyAmount: currentBillingCycle.monthlyAmount,
            status: currentBillingCycle.status,
            nextBillingDate: currentBillingCycle.nextBillingDate
          } : null,
          pendingInvoices: pendingInvoices.map(invoice => ({
            id: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            monthlyAmount: invoice.totalAmount,
            status: invoice.status,
            issuedDate: invoice.issuedDate,
            nextBillingDate: invoice.dueDate
          })),
          totalPendingAmount,
          recentPayments: recentPayments.map(({ payment, invoice }) => ({
            id: payment.id,
            monthlyAmount: payment.amount,
            status: payment.status,
            paymentMethod: payment.paymentMethod,
            createdAt: payment.createdAt,
            invoice: invoice ? {
              invoiceNumber: invoice.invoiceNumber,
              issuedDate: invoice.issuedDate
            } : null
          }))
        },
        metrics: {
          totalStudents: client.actualStudentCount,
          monthlySubscriptionAmount: subscription?.monthlyAmount || 0,
          pendingInvoicesCount: pendingInvoices.length,
          totalPendingAmount,
          lastPaymentDate: recentPayments[0]?.payment.createdAt || null,
          subscriptionStatus: subscription?.status || 'inactive',
          accountStatus: client.status
        }
      }
    })

  } catch (error) {
    console.error('School dashboard error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch dashboard data'
    }, 500)
  }
})

// Student Management - View and update student count
app.get("/students", requireSchoolRole(['admin', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId

    // Fetch client information with student data
    const [client] = await db
      .select({
        id: clients.id,
        schoolName: clients.schoolName,
        actualStudentCount: clients.actualStudentCount,
        estimatedStudentCount: clients.estimatedStudentCount,
        averageMonthlyFee: clients.averageMonthlyFee,
        updatedAt: clients.updatedAt
      })
      .from(clients)
      .where(eq(clients.id, clientId))
      .limit(1)

    if (!client) {
      return c.json({
        success: false,
        error: 'School information not found'
      }, 404)
    }

    // Fetch current subscription to show impact of student count changes from billingSubscriptions table
    const [subscription] = await db
      .select({
        id: billingSubscriptions.id,
        studentCount: billingSubscriptions.studentCount,
        pricePerStudent: billingSubscriptions.pricePerStudent,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        status: billingSubscriptions.status
      })
      .from(billingSubscriptions)
      .where(and(
        eq(billingSubscriptions.clientId, clientId),
        eq(billingSubscriptions.status, 'active')
      ))
      .limit(1)

    return c.json({
      success: true,
      data: {
        school: client,
        subscription: subscription || null,
        studentManagement: {
          canUpdateCount: schoolUser.role === 'admin',
          lastUpdated: client.updatedAt,
          subscriptionImpact: subscription ? {
            currentBilling: subscription.monthlyAmount,
            pricePerStudent: subscription.pricePerStudent,
            billedStudentCount: subscription.studentCount
          } : null
        }
      }
    })

  } catch (error) {
    console.error('Student management error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch student data'
    }, 500)
  }
})

// Update student count (admin only)
app.put("/students/count", requireSchoolRole(['admin']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId

    const { actualStudentCount } = await c.req.json()

    if (!actualStudentCount || actualStudentCount < 1) {
      return c.json({
        success: false,
        error: 'Valid student count is required (minimum 1)'
      }, 400)
    }

    // Update client student count
    await db
      .update(clients)
      .set({
        actualStudentCount,
        updatedAt: new Date()
      })
      .where(eq(clients.id, clientId))

    return c.json({
      success: true,
      message: 'Student count updated successfully',
      data: {
        actualStudentCount,
        updatedAt: new Date(),
        note: 'Subscription billing will be updated in the next billing cycle'
      }
    })

  } catch (error) {
    console.error('Update student count error:', error)
    return c.json({
      success: false,
      error: 'Failed to update student count'
    }, 500)
  }
})

// School Billing - View invoices, payments, and billing history
app.get("/billing", requireSchoolRole(['admin', 'billing', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId
    console.log(`🔍 [Billing API] Client ID: ${clientId}`)

    // Get query parameters for pagination and filtering
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const status = c.req.query('status') // 'sent', 'paid', 'overdue'
    const offset = (page - 1) * limit

    // Build where conditions
    const whereConditions = [eq(billingInvoices.clientId, clientId)]
    if (status) {
      whereConditions.push(eq(billingInvoices.status, status))
    }

    // Fetch invoices with pagination
    const invoicesList = await db
      .select({
        invoice: billingInvoices,
        billingCycle: billingSubscriptions
      })
      .from(billingInvoices)
      .leftJoin(billingSubscriptions, eq(billingInvoices.subscriptionId, billingSubscriptions.id))
      .where(and(...whereConditions))
      .orderBy(desc(billingInvoices.issuedDate))
      .limit(limit)
      .offset(offset)

    // Get total count for pagination
    const [{ count: totalInvoices }] = await db
      .select({ count: count() })
      .from(billingInvoices)
      .where(eq(billingInvoices.clientId, clientId))

    // Fetch recent payments
    const recentPayments = await db
      .select({
        payment: billingPayments,
        invoice: billingInvoices
      })
      .from(billingPayments)
      .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
      .where(eq(billingPayments.clientId, clientId))
      .orderBy(desc(billingPayments.createdAt))
      .limit(10)

    // Calculate subscription-based billing summary
    const subscriptionBillingSummary = await SubscriptionBillingCalculator.getBillingSummary(clientId)

    // Also fetch subscription details for the frontend
    const [subscriptionDetails] = await db
      .select({
        id: billingSubscriptions.id,
        studentCount: billingSubscriptions.studentCount,
        pricePerStudent: billingSubscriptions.pricePerStudent,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        billingCycle: billingSubscriptions.billingCycle,
        status: billingSubscriptions.status,
        startDate: billingSubscriptions.currentPeriodStart,
        nextBillingDate: billingSubscriptions.nextBillingDate,
        autoRenew: billingSubscriptions.autoRenew,
        gracePeriodDays: billingSubscriptions.gracePeriodDays,
        razorpaySubscriptionId: billingSubscriptions.razorpaySubscriptionId,
        razorpayCustomerId: billingSubscriptions.razorpayCustomerId,
        activatedAt: billingSubscriptions.activatedAt
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.clientId, clientId))
      .orderBy(desc(billingSubscriptions.createdAt))
      .limit(1)

    // Get legacy billing summary for backward compatibility (if needed)
    const [legacyBillingSummary] = await db
      .select({
        totalPaid: sum(billingPayments.amount),
        totalInvoiced: sum(billingInvoices.totalAmount)
      })
      .from(billingInvoices)
      .leftJoin(billingPayments, eq(billingInvoices.id, billingPayments.invoiceId))
      .where(eq(billingInvoices.clientId, clientId))

    return c.json({
      success: true,
      data: {
        invoices: invoicesList.map(({ invoice, billingCycle }) => ({
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          monthlyAmount: invoice.totalAmount,
          taxAmount: invoice.taxAmount,
          status: invoice.status,
          issuedDate: invoice.issuedDate,
          nextBillingDate: invoice.dueDate,
          paidDate: invoice.paidDate,
          billingPeriod: billingCycle ? {
            start: billingCycle.currentPeriodStart,
            end: billingCycle.currentPeriodEnd,
            studentCount: billingCycle.studentCount
          } : null
        })),
        payments: recentPayments.map(({ payment, invoice }) => ({
          id: payment.id,
          monthlyAmount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          paymentMethod: payment.paymentMethod,
          createdAt: payment.createdAt,
          invoice: invoice ? {
            invoiceNumber: invoice.invoiceNumber,
            issuedDate: invoice.issuedDate
          } : null
        })),
        summary: {
          // New subscription-based billing summary
          currentMonthAmount: subscriptionBillingSummary.currentMonthAmount,
          outstandingAmount: subscriptionBillingSummary.outstandingAmount,
          paymentStatus: subscriptionBillingSummary.paymentStatus,
          nextBillingDate: subscriptionBillingSummary.nextBillingDate,
          gracePeriodEnd: subscriptionBillingSummary.gracePeriodEnd,daysOverdue: subscriptionBillingSummary.daysOverdue || 0,
          isInGracePeriod: subscriptionBillingSummary.isInGracePeriod || false,

          // Legacy fields for backward compatibility
          totalInvoiced: legacyBillingSummary?.totalInvoiced || 0,
          totalPaid: legacyBillingSummary?.totalPaid || 0,
          pendingAmount: subscriptionBillingSummary.outstandingAmount // Use new calculation
        },
        // Include subscription details in billing response
        subscription: subscriptionDetails ? {
          ...subscriptionDetails,
          planName: 'Basic Plan', // Default plan name for billingSubscriptions
          isAutomaticBillingEnabled: !!(
            subscriptionDetails.razorpaySubscriptionId &&
            subscriptionDetails.razorpayCustomerId &&
            subscriptionDetails.status === 'active' &&
            subscriptionDetails.activatedAt
          )
        } : null,
        pagination: {
          page,
          limit,
          total: totalInvoices,
          totalPages: Math.ceil(totalInvoices / limit)
        }
      }
    })

  } catch (error) {
    console.error('School billing error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch billing data'
    }, 500)
  }
})

// Get current subscription details
app.get("/subscription", requireSchoolRole(['admin', 'billing', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId

    console.log(`🔍 [Subscription API] Looking for subscription for client: ${clientId}`)

    // Fetch current subscription with automatic billing status from billingSubscriptions table
    const [subscription] = await db
      .select({
        id: billingSubscriptions.id,
        studentCount: billingSubscriptions.studentCount,
        pricePerStudent: billingSubscriptions.pricePerStudent,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        billingCycle: billingSubscriptions.billingCycle,
        status: billingSubscriptions.status,
        startDate: billingSubscriptions.currentPeriodStart,
        nextBillingDate: billingSubscriptions.nextBillingDate,
        autoRenew: billingSubscriptions.autoRenew,
        gracePeriodDays: billingSubscriptions.gracePeriodDays,
        // Automatic billing fields
        razorpaySubscriptionId: billingSubscriptions.razorpaySubscriptionId,
        razorpayCustomerId: billingSubscriptions.razorpayCustomerId,
        activatedAt: billingSubscriptions.activatedAt
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.clientId, clientId))
      .orderBy(desc(billingSubscriptions.createdAt))
      .limit(1)

    console.log(`🔍 [Subscription API] Query result:`, subscription)

    if (!subscription) {
      console.log(`❌ [Subscription API] No subscription found for client: ${clientId}`)
      return c.json({
        success: false,
        error: 'No subscription found for this school'
      }, 404)
    }

    // Determine if automatic billing is enabled
    const isAutomaticBillingEnabled = !!(
      subscription.razorpaySubscriptionId &&
      subscription.razorpayCustomerId &&
      subscription.status === 'active' &&
      subscription.activatedAt
    )

    return c.json({
      success: true,
      data: {
        ...subscription,
        planName: 'Basic Plan', // Default plan name for billingSubscriptions
        isAutomaticBillingEnabled
      }
    })

  } catch (error) {
    console.error('School subscription error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch subscription data'
    }, 500)
  }
})

// Get specific invoice details
app.get("/billing/invoice/:invoiceId", requireSchoolRole(['admin', 'billing', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId
    const invoiceId = c.req.param('invoiceId')

    // Fetch invoice with billing cycle details
    const [invoiceData] = await db
      .select({
        invoice: billingInvoices,
        billingCycle: billingSubscriptions,
        client: {
          schoolName: clients.schoolName,
          email: clients.email,
          phone: clients.phone,
          address: clients.address,
          contactPerson: clients.contactPerson
        }
      })
      .from(billingInvoices)
      .leftJoin(billingSubscriptions, eq(billingInvoices.subscriptionId, billingSubscriptions.id))
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
      .where(and(
        eq(billingInvoices.id, invoiceId),
        eq(billingInvoices.clientId, clientId)
      ))
      .limit(1)

    if (!invoiceData) {
      return c.json({
        success: false,
        error: 'Invoice not found'
      }, 404)
    }

    // Fetch payment history for this invoice
    const paymentHistory = await db
      .select()
      .from(billingPayments)
      .where(eq(billingPayments.invoiceId, invoiceId))
      .orderBy(desc(billingPayments.createdAt))

    return c.json({
      success: true,
      data: {
        invoice: {
          id: invoiceData.invoice.id,
          invoiceNumber: invoiceData.invoice.invoiceNumber,
          monthlyAmount: invoiceData.invoice.totalAmount,
          taxAmount: invoiceData.invoice.taxAmount,
          status: invoiceData.invoice.status,
          issuedDate: invoiceData.invoice.issuedDate,
          nextBillingDate: invoiceData.invoice.dueDate,
          paidDate: invoiceData.invoice.paidDate,
          createdAt: invoiceData.invoice.createdAt
        },
        billingPeriod: invoiceData.billingCycle ? {
          id: invoiceData.billingCycle.id,
          currentPeriodStart: invoiceData.billingCycle.currentPeriodStart,
          currentPeriodEnd: invoiceData.billingCycle.currentPeriodEnd,
          studentCount: invoiceData.billingCycle.studentCount,
          monthlyAmount: invoiceData.billingCycle.monthlyAmount
        } : null,
        school: invoiceData.client,
        payments: paymentHistory.map(payment => ({
          id: payment.id,
          monthlyAmount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          paymentMethod: payment.paymentMethod,
          razorpayPaymentId: payment.razorpayPaymentId,
          razorpayOrderId: payment.razorpayOrderId,
          createdAt: payment.createdAt,
          failureReason: payment.failureReason
        })),
        canPay: invoiceData.invoice.status === 'sent' || invoiceData.invoice.status === 'overdue',
        downloadUrls: {
          pdf: `/api/school/billing/invoice/${invoiceId}/pdf`,
          preview: `/api/school/billing/invoice/${invoiceId}/preview`
        }
      }
    })

  } catch (error) {
    console.error('Invoice details error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch invoice details'
    }, 500)
  }
})

// Create payment order for invoice (admin and billing roles only)
app.post("/billing/invoice/:invoiceId/pay",
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    paymentMethod: z.enum(['card', 'netbanking', 'upi', 'wallet']).optional(),
    notes: z.string().optional()
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const invoiceId = c.req.param('invoiceId')
      const { paymentMethod, notes } = c.req.valid('json')

      // Verify invoice belongs to school and is payable
      const [invoiceData] = await db
        .select({
          id: billingInvoices.id,
          invoiceNumber: billingInvoices.invoiceNumber,
          monthlyAmount: billingInvoices.totalAmount,
          status: billingInvoices.status,
          clientId: billingInvoices.clientId,
          client: {
            schoolName: clients.schoolName,
            email: clients.email
          }
        })
        .from(billingInvoices)
        .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
        .where(and(
          eq(billingInvoices.id, invoiceId),
          eq(billingInvoices.clientId, clientId)
        ))
        .limit(1)

      if (!invoiceData) {
        return c.json({
          success: false,
          error: 'Invoice not found'
        }, 404)
      }

      // Check if invoice is payable
      if (invoiceData.status === 'paid') {
        return c.json({
          success: false,
          error: 'Invoice is already paid'
        }, 400)
      }

      if (invoiceData.status !== 'sent' && invoiceData.status !== 'overdue') {
        return c.json({
          success: false,
          error: 'Invoice is not available for payment'
        }, 400)
      }

      // Create payment order
      const amount = Math.round(parseFloat(invoiceData.monthlyAmount.toString()) * 100) // Convert to paise
      const paymentService = getPaymentService()
      const orderResult = await paymentService.createOrder({
        amount,
        currency: 'INR',
        receipt: `PAY-${invoiceData.invoiceNumber}`,
        notes: {
          invoice_id: invoiceId,
          client_id: clientId,
          school_name: invoiceData.client?.schoolName || '',
          invoice_number: invoiceData.invoiceNumber,
          payment_method: paymentMethod || 'not_specified',
          initiated_by: schoolUser.email,
          user_notes: notes || ''
        }
      })

      if (!orderResult.success) {
        return c.json({
          success: false,
          error: 'Failed to create payment order'
        }, 500)
      }

      return c.json({
        success: true,
        data: {
          order: orderResult.order,
          invoice: {
            id: invoiceData.id,
            invoiceNumber: invoiceData.invoiceNumber,
            monthlyAmount: invoiceData.monthlyAmount,
            schoolName: invoiceData.client?.schoolName
          },
          razorpayConfig: {
            keyId: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
            currency: 'INR',
            name: 'Schopio',
            description: `Payment for Invoice ${invoiceData.invoiceNumber}`,
            prefill: {
              email: invoiceData.client?.email,
              name: invoiceData.client?.schoolName
            }
          }
        }
      })

    } catch (error) {
      console.error('Payment initiation error:', error)
      return c.json({
        success: false,
        error: 'Failed to initiate payment'
      }, 500)
    }
  }
)

// Verify payment for invoice
app.post("/billing/invoice/:invoiceId/verify",
  requireSchoolRole(['admin', 'billing']),
  zValidator('json', z.object({
    razorpayOrderId: z.string(),
    razorpayPaymentId: z.string(),
    razorpaySignature: z.string()
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const invoiceId = c.req.param('invoiceId')
      const { razorpayOrderId, razorpayPaymentId, razorpaySignature } = c.req.valid('json')

      // Verify invoice belongs to school
      const [invoiceData] = await db
        .select({
          id: billingInvoices.id,
          invoiceNumber: billingInvoices.invoiceNumber,
          monthlyAmount: billingInvoices.totalAmount,
          status: billingInvoices.status,
          clientId: billingInvoices.clientId,
          client: {
            schoolName: clients.schoolName,
            email: clients.email
          }
        })
        .from(billingInvoices)
        .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
        .where(and(
          eq(billingInvoices.id, invoiceId),
          eq(billingInvoices.clientId, clientId)
        ))
        .limit(1)

      if (!invoiceData) {
        return c.json({
          success: false,
          error: 'Invoice not found'
        }, 404)
      }

      // Check if already paid
      if (invoiceData.status === 'paid') {
        return c.json({
          success: false,
          error: 'Invoice is already paid'
        }, 400)
      }

      // Verify payment signature
      const paymentService = getPaymentService()
      const isValid = paymentService.verifyPaymentSignature({
        razorpayOrderId,
        razorpayPaymentId,
        razorpaySignature
      })

      if (!isValid) {
        return c.json({
          success: false,
          error: 'Invalid payment signature'
        }, 400)
      }

      // Start database transaction
      const result = await db.transaction(async (tx) => {
        // Create payment record
        const [payment] = await tx.insert(billingPayments).values({
          invoiceId,
          clientId,
          razorpayPaymentId,
          razorpayOrderId,
          amount: invoiceData.monthlyAmount,
          currency: 'INR',
          status: 'success',
          paymentMethod: 'razorpay'
        }).returning()

        // Update invoice status
        await tx.update(billingInvoices)
          .set({
            status: 'paid',
            paidDate: new Date().toISOString().split('T')[0]
          })
          .where(eq(billingInvoices.id, invoiceId))

        // Update billing cycle status if applicable
        const [billingCycle] = await tx.select()
          .from(billingSubscriptions)
          .where(eq(billingSubscriptions.id, invoiceData.id))
          .limit(1)

        if (billingCycle) {
          await tx.update(billingSubscriptions)
            .set({ status: 'paid' })
            .where(eq(billingSubscriptions.id, billingCycle.id))
        }

        return payment
      })

      // Process partner commission after successful payment
      try {
        // Check if this school has a partner referral
        const [partnerReferral] = await db
          .select({
            partnerId: schoolReferrals.partnerId,
            isActive: schoolReferrals.isActive
          })
          .from(schoolReferrals)
          .where(and(
            eq(schoolReferrals.clientId, clientId),
            eq(schoolReferrals.isActive, true)
          ))
          .limit(1)

        if (partnerReferral && partnerReferral.partnerId) {
          console.log(`Processing commission for partner ${partnerReferral.partnerId} on payment ${result.id}`)

          const grossAmount = parseFloat(result.amount)
          await commissionProcessor.processCommissionForPayment(
            result.id,
            partnerReferral.partnerId,
            clientId,
            grossAmount
          )

          console.log(`Commission processing completed for payment ${result.id}`)
        }
      } catch (commissionError) {
        console.error('Commission processing error:', commissionError)
        // Don't fail the payment if commission processing fails
        // This ensures the school payment is still recorded successfully
      }

      return c.json({
        success: true,
        message: 'Payment verified and processed successfully',
        data: {
          payment: {
            id: result.id,
            monthlyAmount: result.amount,
            currency: result.currency,
            status: result.status,
            razorpayPaymentId: result.razorpayPaymentId,
            createdAt: result.createdAt
          },
          invoice: {
            id: invoiceData.id,
            invoiceNumber: invoiceData.invoiceNumber,
            status: 'paid',
            paidDate: new Date().toISOString().split('T')[0]
          }
        }
      })

    } catch (error) {
      console.error('Payment verification error:', error)
      return c.json({
        success: false,
        error: 'Failed to verify payment'
      }, 500)
    }
  }
)

// Download invoice PDF
app.get("/billing/invoice/:invoiceId/pdf", requireSchoolRole(['admin', 'billing', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId
    const invoiceId = c.req.param('invoiceId')

    // Verify invoice belongs to school
    const [invoice] = await db
      .select()
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.id, invoiceId),
        eq(billingInvoices.clientId, clientId)
      ))
      .limit(1)

    if (!invoice) {
      return c.json({
        success: false,
        error: 'Invoice not found'
      }, 404)
    }

    // Generate PDF
    const pdfResult = await pdfInvoiceService.generateInvoicePDF(invoiceId)

    if (!pdfResult.success) {
      return c.json({
        success: false,
        error: pdfResult.error || 'Failed to generate PDF'
      }, 500)
    }

    // Return PDF as response
    c.header('Content-Type', 'application/pdf')
    c.header('Content-Disposition', `attachment; filename="${pdfResult.fileName}"`)
    c.header('Content-Length', pdfResult.pdfBuffer!.length.toString())

    return c.body(pdfResult.pdfBuffer!)

  } catch (error) {
    console.error('PDF download error:', error)
    return c.json({
      success: false,
      error: 'Failed to download invoice PDF'
    }, 500)
  }
})

// Get payment status for invoice
app.get("/billing/invoice/:invoiceId/payment-status", requireSchoolRole(['admin', 'billing', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId
    const invoiceId = c.req.param('invoiceId')

    // Verify invoice belongs to school
    const [invoiceData] = await db
      .select({
        id: billingInvoices.id,
        invoiceNumber: billingInvoices.invoiceNumber,
        monthlyAmount: billingInvoices.totalAmount,
        status: billingInvoices.status,
        issuedDate: billingInvoices.issuedDate,
        nextBillingDate: billingInvoices.dueDate,
        paidDate: billingInvoices.paidDate
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.id, invoiceId),
        eq(billingInvoices.clientId, clientId)
      ))
      .limit(1)

    if (!invoiceData) {
      return c.json({
        success: false,
        error: 'Invoice not found'
      }, 404)
    }

    // Get payment history
    const paymentHistory = await db
      .select({
        id: billingPayments.id,
        monthlyAmount: billingPayments.amount,
        status: billingPayments.status,
        paymentMethod: billingPayments.paymentMethod,
        razorpayPaymentId: billingPayments.razorpayPaymentId,
        createdAt: billingPayments.createdAt,
        failureReason: billingPayments.failureReason
      })
      .from(billingPayments)
      .where(eq(billingPayments.invoiceId, invoiceId))
      .orderBy(desc(billingPayments.createdAt))

    // Calculate payment status
    const totalPaid = paymentHistory
      .filter(p => p.status === 'success')
      .reduce((sum, p) => sum + parseFloat(p.monthlyAmount.toString()), 0)

    const isPaid = invoiceData.status === 'paid'
    const isOverdue = !isPaid && new Date(invoiceData.nextBillingDate) < new Date()
    const daysOverdue = isOverdue ?
      Math.floor((new Date().getTime() - new Date(invoiceData.nextBillingDate).getTime()) / (1000 * 60 * 60 * 24)) : 0

    return c.json({
      success: true,
      data: {
        invoice: {
          id: invoiceData.id,
          invoiceNumber: invoiceData.invoiceNumber,
          monthlyAmount: invoiceData.monthlyAmount,
          status: invoiceData.status,
          issuedDate: invoiceData.issuedDate,
          nextBillingDate: invoiceData.nextBillingDate,
          paidDate: invoiceData.paidDate
        },
        paymentStatus: {
          isPaid,
          isOverdue,
          daysOverdue,
          totalPaid,
          remainingAmount: parseFloat(invoiceData.monthlyAmount.toString()) - totalPaid,
          canPay: !isPaid && (invoiceData.status === 'sent' || invoiceData.status === 'overdue')
        },
        payments: paymentHistory,
        summary: {
          totalAttempts: paymentHistory.length,
          successfulPayments: paymentHistory.filter(p => p.status === 'success').length,
          failedPayments: paymentHistory.filter(p => p.status === 'failed').length,
          lastPaymentAttempt: paymentHistory[0]?.createdAt || null,
          lastSuccessfulPayment: paymentHistory.find(p => p.status === 'success')?.createdAt || null
        }
      }
    })

  } catch (error) {
    console.error('Payment status error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch payment status'
    }, 500)
  }
})

// ===== SUPPORT TICKET SYSTEM =====

// Get all support tickets for the school
app.get("/support/tickets", requireSchoolRole(['admin', 'support', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId

    // Get query parameters for pagination and filtering
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const status = c.req.query('status') // 'open', 'in_progress', 'resolved', 'closed'
    const priority = c.req.query('priority') // 'low', 'medium', 'high', 'urgent'
    const category = c.req.query('category') // 'billing', 'technical', 'feature_request', 'bug'
    const search = c.req.query('search') || ''
    const offset = (page - 1) * limit

    // Build where conditions
    const whereConditions = [eq(supportTickets.clientId, clientId)]

    if (status) {
      whereConditions.push(eq(supportTickets.status, status))
    }
    if (priority) {
      whereConditions.push(eq(supportTickets.priority, priority))
    }
    if (category) {
      whereConditions.push(eq(supportTickets.category, category))
    }

    // Fetch tickets with pagination
    const tickets = await db
      .select({
        id: supportTickets.id,
        title: supportTickets.title,
        description: supportTickets.description,
        priority: supportTickets.priority,
        status: supportTickets.status,
        category: supportTickets.category,
        assignedTo: supportTickets.assignedTo,
        resolvedAt: supportTickets.resolvedAt,
        createdAt: supportTickets.createdAt,
        updatedAt: supportTickets.updatedAt,
        createdBy: {
          id: clientUsers.id,
          name: clientUsers.name,
          email: clientUsers.email
        }
      })
      .from(supportTickets)
      .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
      .where(and(...whereConditions))
      .orderBy(desc(supportTickets.createdAt))
      .limit(limit)
      .offset(offset)

    // Get total count for pagination
    const [{ count: totalTickets }] = await db
      .select({ count: count() })
      .from(supportTickets)
      .where(and(...whereConditions))

    // Get message counts for each ticket
    const ticketsWithMessageCounts = await Promise.all(
      tickets.map(async (ticket) => {
        const [{ messageCount }] = await db
          .select({ messageCount: count() })
          .from(ticketMessages)
          .where(eq(ticketMessages.ticketId, ticket.id))

        // Get last message info
        const [lastMessage] = await db
          .select({
            message: ticketMessages.message,
            senderType: ticketMessages.senderType,
            createdAt: ticketMessages.createdAt
          })
          .from(ticketMessages)
          .where(eq(ticketMessages.ticketId, ticket.id))
          .orderBy(desc(ticketMessages.createdAt))
          .limit(1)

        return {
          ...ticket,
          messageCount,
          lastMessage: lastMessage || null,
          isUnread: false // TODO: Implement read status tracking
        }
      })
    )

    return c.json({
      success: true,
      data: {
        tickets: ticketsWithMessageCounts,
        pagination: {
          page,
          limit,
          total: totalTickets,
          totalPages: Math.ceil(totalTickets / limit)
        },
        summary: {
          total: totalTickets,
          byStatus: {
            open: tickets.filter(t => t.status === 'open').length,
            inProgress: tickets.filter(t => t.status === 'in_progress').length,
            resolved: tickets.filter(t => t.status === 'resolved').length,
            closed: tickets.filter(t => t.status === 'closed').length
          },
          byPriority: {
            urgent: tickets.filter(t => t.priority === 'urgent').length,
            high: tickets.filter(t => t.priority === 'high').length,
            medium: tickets.filter(t => t.priority === 'medium').length,
            low: tickets.filter(t => t.priority === 'low').length
          }
        }
      }
    })

  } catch (error) {
    console.error('Support tickets error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch support tickets'
    }, 500)
  }
})

// Create new support ticket
app.post("/support/tickets",
  requireSchoolRole(['admin', 'support']),
  zValidator('json', z.object({
    title: z.string().min(5, 'Title must be at least 5 characters').max(255, 'Title too long'),
    description: z.string().min(10, 'Description must be at least 10 characters'),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
    category: z.enum(['billing', 'technical', 'feature_request', 'bug', 'general']).optional(),
    attachments: z.array(z.object({
      fileName: z.string(),
      fileUrl: z.string().url(),
      fileSize: z.number(),
      fileType: z.string()
    })).optional()
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const { title, description, priority, category, attachments } = c.req.valid('json')

      // Check if school has a referral partner for ticket assignment
      let assignedTo = null
      const [referralData] = await db
        .select({
          partnerId: schoolReferrals.partnerId
        })
        .from(schoolReferrals)
        .where(and(
          eq(schoolReferrals.clientId, clientId),
          eq(schoolReferrals.isActive, true)
        ))
        .limit(1)

      // If school has a referral partner, assign ticket to them
      if (referralData) {
        assignedTo = referralData.partnerId
      }

      // Create the support ticket
      const [newTicket] = await db.insert(supportTickets).values({
        clientId,
        title,
        description,
        priority,
        category: category || 'general',
        status: 'open',
        assignedTo, // Assign to partner if exists, otherwise null (admin will handle)
        createdBy: schoolUser.id,
        createdAt: new Date(),
        updatedAt: new Date()
      }).returning()

      // If there are attachments, create an initial message with them
      if (attachments && attachments.length > 0) {
        await db.insert(ticketMessages).values({
          ticketId: newTicket.id,
          senderType: 'client',
          senderId: schoolUser.id,
          message: 'Initial ticket submission with attachments.',
          attachments: attachments,
          createdAt: new Date()
        })
      }

      // Send notifications about new ticket creation
      await supportNotificationService.notifyTicketCreated(newTicket.id)

      return c.json({
        success: true,
        message: 'Support ticket created successfully',
        data: {
          ticket: {
            id: newTicket.id,
            title: newTicket.title,
            description: newTicket.description,
            priority: newTicket.priority,
            category: newTicket.category,
            status: newTicket.status,
            createdAt: newTicket.createdAt,
            createdBy: {
              id: schoolUser.id,
              name: schoolUser.name,
              email: schoolUser.email
            }
          },
          nextSteps: [
            'Your ticket has been submitted and assigned a unique ID',
            'Our support team will review your request within 24 hours',
            'You will receive updates via email and can track progress in your portal',
            'You can add additional messages or attachments to this ticket'
          ]
        }
      })

    } catch (error) {
      console.error('Create support ticket error:', error)
      return c.json({
        success: false,
        error: 'Failed to create support ticket'
      }, 500)
    }
  }
)

// Get specific support ticket details with messages
app.get("/support/tickets/:ticketId", requireSchoolRole(['admin', 'support', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId
    const ticketId = c.req.param('ticketId')

    // Get ticket details
    const [ticket] = await db
      .select({
        id: supportTickets.id,
        title: supportTickets.title,
        description: supportTickets.description,
        priority: supportTickets.priority,
        status: supportTickets.status,
        category: supportTickets.category,
        assignedTo: supportTickets.assignedTo,
        resolvedAt: supportTickets.resolvedAt,
        createdAt: supportTickets.createdAt,
        updatedAt: supportTickets.updatedAt,
        createdBy: {
          id: clientUsers.id,
          name: clientUsers.name,
          email: clientUsers.email
        }
      })
      .from(supportTickets)
      .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
      .where(and(
        eq(supportTickets.id, ticketId),
        eq(supportTickets.clientId, clientId)
      ))
      .limit(1)

    if (!ticket) {
      return c.json({
        success: false,
        error: 'Support ticket not found'
      }, 404)
    }

    // Get all messages for this ticket
    const messages = await db
      .select({
        id: ticketMessages.id,
        message: ticketMessages.message,
        senderType: ticketMessages.senderType,
        senderId: ticketMessages.senderId,
        attachments: ticketMessages.attachments,
        createdAt: ticketMessages.createdAt
      })
      .from(ticketMessages)
      .where(eq(ticketMessages.ticketId, ticketId))
      .orderBy(ticketMessages.createdAt)

    // Get sender details for each message
    const messagesWithSenders = await Promise.all(
      messages.map(async (message) => {
        let senderName = 'Unknown'
        let senderEmail = ''

        if (message.senderType === 'client') {
          const [clientUser] = await db
            .select({ name: clientUsers.name, email: clientUsers.email })
            .from(clientUsers)
            .where(eq(clientUsers.id, message.senderId))
            .limit(1)

          if (clientUser) {
            senderName = clientUser.name
            senderEmail = clientUser.email
          }
        } else if (message.senderType === 'admin') {
          // For admin messages, we'll show "Support Team" for privacy
          senderName = 'Support Team'
          senderEmail = '<EMAIL>'
        }

        return {
          ...message,
          sender: {
            name: senderName,
            email: senderEmail,
            type: message.senderType
          }
        }
      })
    )

    // Get assigned admin name if any
    let assignedAdminName = null
    if (ticket.assignedTo) {
      // We'll just show "Support Team" for privacy
      assignedAdminName = 'Support Team'
    }

    return c.json({
      success: true,
      data: {
        ticket: {
          ...ticket,
          assignedAdminName,
          canAddMessage: ticket.status !== 'closed',
          canClose: ticket.status === 'resolved' && ['admin', 'support'].includes(schoolUser.role)
        },
        messages: messagesWithSenders,
        messageCount: messages.length,
        statusHistory: [
          {
            status: 'open',
            timestamp: ticket.createdAt,
            note: 'Ticket created'
          }
          // TODO: Add status change tracking
        ]
      }
    })

  } catch (error) {
    console.error('Get support ticket error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch support ticket details'
    }, 500)
  }
})

// Add message to support ticket
app.post("/support/tickets/:ticketId/messages",
  requireSchoolRole(['admin', 'support']),
  zValidator('json', z.object({
    message: z.string().min(1, 'Message cannot be empty').max(5000, 'Message too long'),
    attachments: z.array(z.object({
      fileName: z.string(),
      fileUrl: z.string().url(),
      fileSize: z.number(),
      fileType: z.string()
    })).optional()
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const ticketId = c.req.param('ticketId')
      const { message, attachments } = c.req.valid('json')

      // Verify ticket belongs to school and is not closed
      const [ticket] = await db
        .select({
          id: supportTickets.id,
          status: supportTickets.status,
          title: supportTickets.title
        })
        .from(supportTickets)
        .where(and(
          eq(supportTickets.id, ticketId),
          eq(supportTickets.clientId, clientId)
        ))
        .limit(1)

      if (!ticket) {
        return c.json({
          success: false,
          error: 'Support ticket not found'
        }, 404)
      }

      if (ticket.status === 'closed') {
        return c.json({
          success: false,
          error: 'Cannot add messages to closed tickets'
        }, 400)
      }

      // Add the message
      const [newMessage] = await db.insert(ticketMessages).values({
        ticketId,
        senderType: 'client',
        senderId: schoolUser.id,
        message,
        attachments: attachments || null,
        createdAt: new Date()
      }).returning()

      // Update ticket's updatedAt timestamp and potentially status
      const updateData: any = {
        updatedAt: new Date()
      }

      // If ticket was resolved, move it back to in_progress
      if (ticket.status === 'resolved') {
        updateData.status = 'in_progress'
      }

      await db.update(supportTickets)
        .set(updateData)
        .where(eq(supportTickets.id, ticketId))

      // Send notification about new client message
      await supportNotificationService.notifyNewMessage(ticketId, newMessage.id, 'client')

      return c.json({
        success: true,
        message: 'Message added successfully',
        data: {
          messageId: newMessage.id,
          ticketId,
          message: newMessage.message,
          attachments: newMessage.attachments,
          sentAt: newMessage.createdAt,
          sender: {
            name: schoolUser.name,
            email: schoolUser.email,
            type: 'client'
          },
          ticketStatus: updateData.status || ticket.status,
          statusChanged: !!updateData.status
        }
      })

    } catch (error) {
      console.error('Add ticket message error:', error)
      return c.json({
        success: false,
        error: 'Failed to add message to ticket'
      }, 500)
    }
  }
)

// Update support ticket status (limited actions for schools)
app.put("/support/tickets/:ticketId/status",
  requireSchoolRole(['admin', 'support']),
  zValidator('json', z.object({
    status: z.enum(['closed']), // Schools can only close resolved tickets
    reason: z.string().optional()
  })),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)!
      const clientId = schoolUser.clientId
      const ticketId = c.req.param('ticketId')
      const { status, reason } = c.req.valid('json')

      // Verify ticket belongs to school
      const [ticket] = await db
        .select({
          id: supportTickets.id,
          status: supportTickets.status,
          title: supportTickets.title
        })
        .from(supportTickets)
        .where(and(
          eq(supportTickets.id, ticketId),
          eq(supportTickets.clientId, clientId)
        ))
        .limit(1)

      if (!ticket) {
        return c.json({
          success: false,
          error: 'Support ticket not found'
        }, 404)
      }

      // Schools can only close tickets that are resolved
      if (status === 'closed' && ticket.status !== 'resolved') {
        return c.json({
          success: false,
          error: 'Can only close tickets that have been resolved by support team'
        }, 400)
      }

      if (ticket.status === 'closed') {
        return c.json({
          success: false,
          error: 'Ticket is already closed'
        }, 400)
      }

      // Update ticket status
      await db.update(supportTickets)
        .set({
          status,
          updatedAt: new Date()
        })
        .where(eq(supportTickets.id, ticketId))

      // Add a system message about the status change
      if (reason) {
        await db.insert(ticketMessages).values({
          ticketId,
          senderType: 'client',
          senderId: schoolUser.id,
          message: `Ticket closed by school. Reason: ${reason}`,
          createdAt: new Date()
        })
      }

      return c.json({
        success: true,
        message: `Ticket ${status} successfully`,
        data: {
          ticketId,
          oldStatus: ticket.status,
          newStatus: status,
          updatedBy: schoolUser.name,
          updatedAt: new Date().toISOString(),
          reason: reason || null
        }
      })

    } catch (error) {
      console.error('Update ticket status error:', error)
      return c.json({
        success: false,
        error: 'Failed to update ticket status'
      }, 500)
    }
  }
)

// Get support ticket statistics for school dashboard
app.get("/support/stats", requireSchoolRole(['admin', 'support', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)!
    const clientId = schoolUser.clientId

    // Get ticket counts by status
    const statusCounts = await db
      .select({
        status: supportTickets.status,
        count: count()
      })
      .from(supportTickets)
      .where(eq(supportTickets.clientId, clientId))
      .groupBy(supportTickets.status)

    // Get ticket counts by priority
    const priorityCounts = await db
      .select({
        priority: supportTickets.priority,
        count: count()
      })
      .from(supportTickets)
      .where(eq(supportTickets.clientId, clientId))
      .groupBy(supportTickets.priority)

    // Get recent tickets (last 5)
    const recentTickets = await db
      .select({
        id: supportTickets.id,
        title: supportTickets.title,
        status: supportTickets.status,
        priority: supportTickets.priority,
        createdAt: supportTickets.createdAt
      })
      .from(supportTickets)
      .where(eq(supportTickets.clientId, clientId))
      .orderBy(desc(supportTickets.createdAt))
      .limit(5)

    // Calculate response time (average time to first admin response)
    // This is a simplified calculation - in production you'd want more sophisticated metrics
    const totalTickets = statusCounts.reduce((sum, item) => sum + item.count, 0)

    return c.json({
      success: true,
      data: {
        summary: {
          total: totalTickets,
          open: statusCounts.find(s => s.status === 'open')?.count || 0,
          inProgress: statusCounts.find(s => s.status === 'in_progress')?.count || 0,
          resolved: statusCounts.find(s => s.status === 'resolved')?.count || 0,
          closed: statusCounts.find(s => s.status === 'closed')?.count || 0
        },
        byPriority: {
          urgent: priorityCounts.find(p => p.priority === 'urgent')?.count || 0,
          high: priorityCounts.find(p => p.priority === 'high')?.count || 0,
          medium: priorityCounts.find(p => p.priority === 'medium')?.count || 0,
          low: priorityCounts.find(p => p.priority === 'low')?.count || 0
        },
        recentTickets,
        quickActions: [
          {
            label: 'Create New Ticket',
            action: 'create_ticket',
            available: true
          },
          {
            label: 'View All Tickets',
            action: 'view_tickets',
            available: true
          },
          {
            label: 'Contact Support',
            action: 'contact_support',
            available: true
          }
        ]
      }
    })

  } catch (error) {
    console.error('Support stats error:', error)
    return c.json({
      success: false,
      error: 'Failed to fetch support statistics'
    }, 500)
  }
})

// ===== SUBSCRIPTION MANAGEMENT =====

// Validation schema for subscription update request
const subscriptionUpdateRequestSchema = z.object({
  studentCount: z.number().min(1, 'Student count must be at least 1'),
  facultyCount: z.number().min(1, 'Faculty count must be at least 1'),
  averageMonthlyFee: z.number().min(0, 'Average monthly fee must be a valid amount'),
  completeAddress: z.string().min(1, 'Complete address is required'),
  contactNumber: z.string().min(1, 'Contact number is required'),
  primaryEmail: z.string().email('Please enter a valid email address'),
  notes: z.string().optional()
})

// POST /school/subscription/update-request - Submit subscription update request
app.post('/subscription/update-request',
  requireSchoolRole(['admin']),
  zValidator('json', subscriptionUpdateRequestSchema),
  async (c) => {
    try {
      const schoolUser = getCurrentSchoolUser(c)
      if (!schoolUser) {
        return c.json({ error: 'Authentication required' }, 401)
      }

      const data = c.req.valid('json')

      console.log(`📝 [School] Submitting subscription update request for client: ${schoolUser.clientId}`)

      // Get client information
      const [client] = await db
        .select({
          id: clients.id,
          schoolName: clients.schoolName,
          email: clients.email
        })
        .from(clients)
        .where(eq(clients.id, schoolUser.clientId))
        .limit(1)

      if (!client) {
        return c.json({ error: 'School not found' }, 404)
      }

      // Create software request for subscription update
      const [newRequest] = await db
        .insert(softwareRequests)
        .values({
          clientId: schoolUser.clientId,
          requestType: 'production',
          studentCount: data.studentCount,
          facultyCount: data.facultyCount,
          completeAddress: data.completeAddress,
          contactNumber: data.contactNumber,
          primaryEmail: data.primaryEmail,
          averageMonthlyFee: data.averageMonthlyFee.toString(),
          status: 'pending',
          // Legacy fields for backward compatibility
          class1Fee: data.averageMonthlyFee.toString(),
          class4Fee: data.averageMonthlyFee.toString(),
          class6Fee: data.averageMonthlyFee.toString(),
          class10Fee: data.averageMonthlyFee.toString(),
          class1112Fee: data.averageMonthlyFee.toString(),
          termsAccepted: true,
          termsVersion: '1.0'
        })
        .returning()

      console.log(`✅ [School] Subscription update request created: ${newRequest.id}`)

      return c.json({
        success: true,
        requestId: newRequest.id,
        message: 'Subscription update request submitted successfully. Our team will review and process your request.'
      })

    } catch (error) {
      console.error('❌ [School] Error submitting subscription update request:', error)
      return c.json({ error: 'Failed to submit subscription update request' }, 500)
    }
  }
)

// GET /school/subscription/update-requests - Get school's subscription update requests
app.get('/subscription/update-requests', requireSchoolRole(['admin', 'viewer']), async (c) => {
  try {
    const schoolUser = getCurrentSchoolUser(c)
    if (!schoolUser) {
      return c.json({ error: 'Authentication required' }, 401)
    }

    console.log(`📊 [School] Fetching subscription update requests for client: ${schoolUser.clientId}`)

    // Get school's software requests (which include subscription updates)
    const requests = await db
      .select({
        id: softwareRequests.id,
        requestType: softwareRequests.requestType,
        studentCount: softwareRequests.studentCount,
        facultyCount: softwareRequests.facultyCount,
        averageMonthlyFee: softwareRequests.averageMonthlyFee,
        completeAddress: softwareRequests.completeAddress,
        contactNumber: softwareRequests.contactNumber,
        primaryEmail: softwareRequests.primaryEmail,
        status: softwareRequests.status,
        createdAt: softwareRequests.createdAt,
        updatedAt: softwareRequests.updatedAt
      })
      .from(softwareRequests)
      .where(eq(softwareRequests.clientId, schoolUser.clientId))
      .orderBy(desc(softwareRequests.createdAt))

    console.log(`✅ [School] Found ${requests.length} subscription requests`)

    return c.json({
      success: true,
      requests: requests.map(request => ({
        ...request,
        studentCount: request.studentCount || 0,
        facultyCount: request.facultyCount || 0,
        averageMonthlyFee: parseFloat(request.averageMonthlyFee || '0')
      }))
    })

  } catch (error) {
    console.error('❌ [School] Error fetching subscription update requests:', error)
    return c.json({ error: 'Failed to fetch subscription update requests' }, 500)
  }
})

export default app
