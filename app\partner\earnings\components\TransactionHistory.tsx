'use client'

import { useState } from 'react'
import { 
  CreditCard, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  Download,
  Calendar,
  DollarSign,
  Filter,
  Search
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

// Simple Table Components
const Table = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`overflow-hidden rounded-lg border border-slate-200 ${className}`}>
    <table className="w-full border-collapse">{children}</table>
  </div>
)

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead className="bg-slate-50">{children}</thead>
)

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody className="divide-y divide-slate-200">{children}</tbody>
)

const TableRow = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <tr className={`hover:bg-slate-50 ${className}`}>{children}</tr>
)

const TableHead = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <th className={`px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider ${className}`}>
    {children}
  </th>
)

const TableCell = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <td className={`px-6 py-4 whitespace-nowrap text-sm text-slate-900 ${className}`}>
    {children}
  </td>
)

interface Transaction {
  id: string
  schoolName: string
  paymentMonth: string
  grossAmount: number
  tdsAmount: number
  netAmount: number
  payoutStatus: 'pending' | 'processing' | 'completed' | 'failed'
  payoutDate?: string
  razorpayPayoutId?: string
}

interface TransactionHistoryProps {
  transactions: Transaction[]
}

export default function TransactionHistory({ transactions }: TransactionHistoryProps) {
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<string>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  const getPayoutStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        )
      case 'processing':
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
            <Clock className="w-3 h-3 mr-1" />
            Processing
          </Badge>
        )
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
      case 'failed':
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Failed
          </Badge>
        )
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  const formatMonth = (monthString: string) => {
    const [year, month] = monthString.split('-')
    const date = new Date(parseInt(year), parseInt(month) - 1)
    return date.toLocaleDateString('en-IN', {
      month: 'short',
      year: 'numeric'
    })
  }

  // Apply filters and sorting
  const filteredTransactions = transactions.filter(transaction => {
    // Status filter
    if (statusFilter !== 'all' && transaction.payoutStatus !== statusFilter) {
      return false
    }

    // Search filter
    if (searchTerm && !transaction.schoolName.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false
    }

    return true
  }).sort((a, b) => {
    // Sorting
    if (sortBy === 'date') {
      const dateA = a.payoutDate ? new Date(a.payoutDate).getTime() : 0
      const dateB = b.payoutDate ? new Date(b.payoutDate).getTime() : 0
      return sortOrder === 'asc' ? dateA - dateB : dateB - dateA
    }
    
    if (sortBy === 'amount') {
      return sortOrder === 'asc' ? a.netAmount - b.netAmount : b.netAmount - a.netAmount
    }
    
    if (sortBy === 'school') {
      return sortOrder === 'asc' 
        ? a.schoolName.localeCompare(b.schoolName) 
        : b.schoolName.localeCompare(a.schoolName)
    }
    
    return 0
  })

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="w-5 h-5" />
            <span>Transaction History</span>
          </CardTitle>
          
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="flex items-center space-x-2">
              <Search className="w-4 h-4 text-slate-400" />
              <Input
                placeholder="Search schools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <CreditCard className="w-12 h-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">No transactions found</h3>
            <p className="text-slate-600">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your filters to see more results' 
                : 'Your transaction history will appear here once commissions are processed'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>School</TableHead>
                  <TableHead>Month</TableHead>
                  <TableHead>Gross Amount</TableHead>
                  <TableHead>TDS Deducted</TableHead>
                  <TableHead>Net Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payout Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-medium">
                      {transaction.schoolName}
                    </TableCell>
                    <TableCell>
                      {formatMonth(transaction.paymentMonth)}
                    </TableCell>
                    <TableCell className="font-semibold">
                      {formatCurrency(transaction.grossAmount)}
                    </TableCell>
                    <TableCell className="text-red-600">
                      -{formatCurrency(transaction.tdsAmount)}
                    </TableCell>
                    <TableCell className="font-semibold text-green-600">
                      {formatCurrency(transaction.netAmount)}
                    </TableCell>
                    <TableCell>
                      {getPayoutStatusBadge(transaction.payoutStatus)}
                    </TableCell>
                    <TableCell>
                      {formatDate(transaction.payoutDate)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {transaction.razorpayPayoutId && transaction.payoutStatus === 'completed' && (
                          <Button variant="outline" size="sm">
                            View Receipt
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
