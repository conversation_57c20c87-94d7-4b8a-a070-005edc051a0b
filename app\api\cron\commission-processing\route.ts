import { NextRequest, NextResponse } from 'next/server'
import { commissionScheduler } from '@/src/services/commissionScheduler'

// This endpoint should be called by a cron job service (like Vercel Cron or external cron)
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from authorized source
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.CRON_SECRET_TOKEN

    if (!expectedToken || authHeader !== `Bearer ${expectedToken}`) {
      console.error('❌ [Cron] Unauthorized cron job request')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('🕐 [Cron] Starting automated commission processing...')

    // Run all scheduled commission tasks
    await commissionScheduler.runScheduledTasks()

    console.log('✅ [Cron] Automated commission processing completed')

    return NextResponse.json({
      success: true,
      message: 'Commission processing completed',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ [Cron] Error in automated commission processing:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Handle GET requests for health check
export async function GET() {
  return NextResponse.json({
    message: 'Commission processing cron endpoint is active',
    timestamp: new Date().toISOString()
  })
}
