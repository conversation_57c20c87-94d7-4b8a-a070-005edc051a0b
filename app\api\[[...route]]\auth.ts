import { <PERSON>o } from 'hono'
import { z<PERSON>alidator } from '@hono/zod-validator'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { Resend } from 'resend'
import { db } from '@/src/db'
import { clients, clientUsers, partners, referralCodes, schoolReferrals, softwareRequests, requestStatusHistory, termsConditions } from '@/src/db/schema'
import { eq, and, desc } from 'drizzle-orm'
import { generatePartnerAuthToken } from '@/src/middleware/partner-auth'
import { generateSchoolToken } from '@/src/middleware/school-auth'

const app = new Hono()

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY)

// Validation schemas
const registerSchema = z.object({
  schoolName: z.string().min(2, 'School name must be at least 2 characters'),
  contactPerson: z.string().min(2, 'Contact person name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  phone: z.string().min(10, 'Phone number must be at least 10 digits'),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  estimatedStudents: z.number().min(1, 'Must have at least 1 student'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
  referralCode: z.string().optional()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required')
})

const verifyOtpSchema = z.object({
  email: z.string().email('Invalid email address'),
  otp: z.string().length(6, 'OTP must be 6 digits')
})

const resendOtpSchema = z.object({
  email: z.string().email('Invalid email address')
})

// Helper function to generate OTP
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Helper function to send OTP email
async function sendOTPEmail(email: string, otp: string, name: string) {
  try {
    await resend.emails.send({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: [email],
      subject: 'Verify Your Schopio Account - OTP Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">Schopio</h1>
            <p style="color: #64748b; margin: 5px 0;">School Management Platform</p>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #1e293b; margin-top: 0;">Verify Your Account</h2>
            <p style="color: #475569; margin-bottom: 20px;">Hi ${name},</p>
            <p style="color: #475569; margin-bottom: 20px;">
              Thank you for registering with Schopio! Please use the following OTP code to verify your email address:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <div style="background: #2563eb; color: white; font-size: 32px; font-weight: bold; padding: 20px; border-radius: 8px; letter-spacing: 8px; display: inline-block;">
                ${otp}
              </div>
            </div>
            
            <p style="color: #475569; margin-bottom: 10px;">
              This OTP will expire in <strong>10 minutes</strong>.
            </p>
            <p style="color: #475569; margin-bottom: 0;">
              If you didn't request this verification, please ignore this email.
            </p>
          </div>
          
          <div style="text-align: center; color: #94a3b8; font-size: 14px;">
            <p>© 2024 Schopio. All rights reserved.</p>
            <p>This is an automated email. Please do not reply.</p>
          </div>
        </div>
      `
    })
    return true
  } catch (error) {
    console.error('Error sending OTP email:', error)
    return false
  }
}

// Helper function to generate JWT token
function generateToken(userId: string, email: string): string {
  return jwt.sign(
    { userId, email },
    process.env.JWT_SECRET || 'fallback-secret-key',
    { expiresIn: '7d' }
  )
}

// Register endpoint
app.post('/register', zValidator('json', registerSchema, (result, c) => {
  if (!result.success) {
    console.log('Validation errors:', result.error.issues)
    return c.json({
      error: 'Validation failed',
      details: result.error.issues.map(issue => ({
        field: issue.path.join('.'),
        message: issue.message
      }))
    }, 400)
  }
}), async (c) => {
  try {
    const data = c.req.valid('json')
    console.log('Registration data received:', data)

    // Check if email already exists
    const existingUser = await db.select().from(clientUsers).where(eq(clientUsers.email, data.email)).limit(1)
    if (existingUser.length > 0) {
      return c.json({ error: 'Email already registered' }, 400)
    }

    // Hash password
    const passwordHash = await bcrypt.hash(data.password, 12)
    
    // Generate OTP
    const otp = generateOTP()
    const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Validate referral code if provided
    let referralCodeData = null
    if (data.referralCode && data.referralCode.trim()) {
      const [referralCode] = await db
        .select({
          id: referralCodes.id,
          partnerId: referralCodes.partnerId,
          code: referralCodes.code,
          isActive: referralCodes.isActive,
          usageCount: referralCodes.usageCount,
          maxUsage: referralCodes.maxUsage
        })
        .from(referralCodes)
        .innerJoin(partners, eq(referralCodes.partnerId, partners.id))
        .where(and(
          eq(referralCodes.code, data.referralCode.trim().toUpperCase()),
          eq(referralCodes.isActive, true),
          eq(partners.isActive, true)
        ))
        .limit(1)

      if (!referralCode) {
        return c.json({ error: 'Invalid or inactive referral code' }, 400)
      }

      // Check usage limit
      if (referralCode.maxUsage && (referralCode.usageCount || 0) >= referralCode.maxUsage) {
        return c.json({ error: 'Referral code has reached maximum usage limit' }, 400)
      }

      referralCodeData = referralCode
    }

    // Get client IP and user agent for referral tracking
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Create records sequentially (Neon HTTP driver doesn't support transactions)
    // Create client record first
    const [newClient] = await db.insert(clients).values({
      schoolName: data.schoolName,
      schoolCode: `SCH${Date.now()}`, // Generate unique school code
      email: data.email,
      phone: data.phone,
      address: data.address,
      contactPerson: data.contactPerson,
      actualStudentCount: data.estimatedStudents,
      estimatedStudentCount: data.estimatedStudents,
      onboardingStatus: 'pending',
      status: 'active'
    }).returning()

    // Create user record
    const [newUser] = await db.insert(clientUsers).values({
      clientId: newClient.id,
      email: data.email,
      passwordHash,
      name: data.contactPerson,
      role: 'admin',
      isActive: true,
      emailVerified: false,
      otpCode: otp,
      otpExpiresAt
    }).returning()

    // Apply referral code if provided
    if (referralCodeData) {
      // Create school referral record
      await db.insert(schoolReferrals).values({
        clientId: newClient.id,
        partnerId: referralCodeData.partnerId,
        referralCodeId: referralCodeData.id,
        referralSource: 'registration',
        ipAddress: ipAddress,
        userAgent: userAgent,
        appliedBy: newUser.id
      })

      // Update referral code usage count
      await db
        .update(referralCodes)
        .set({
          usageCount: (referralCodeData.usageCount || 0) + 1
        })
        .where(eq(referralCodes.id, referralCodeData.id))
    }

    // Variables are already available as newClient and newUser

    // Send OTP email
    const emailSent = await sendOTPEmail(data.email, otp, data.contactPerson)
    
    if (!emailSent) {
      // Rollback - delete created records
      await db.delete(clientUsers).where(eq(clientUsers.id, newUser.id))
      await db.delete(clients).where(eq(clients.id, newClient.id))
      return c.json({ error: 'Failed to send verification email. Please try again.' }, 500)
    }

    return c.json({
      message: 'Registration successful! Please check your email for the OTP code.',
      email: data.email,
      requiresVerification: true
    })

  } catch (error) {
    console.error('Registration error:', error)
    return c.json({ error: 'Registration failed. Please try again.' }, 500)
  }
})

// Login endpoint
app.post('/login', zValidator('json', loginSchema), async (c) => {
  try {
    const { email, password } = c.req.valid('json')

    console.log(`🔐 [Auth] Login attempt for email: ${email}`)

    // Find user
    const [user] = await db.select().from(clientUsers).where(eq(clientUsers.email, email)).limit(1)

    if (!user) {
      console.log(`❌ [Auth] User not found: ${email}`)
      return c.json({ error: 'Invalid email or password' }, 401)
    }

    console.log(`👤 [Auth] Found user: ${user.name} (${user.email})`)
    console.log(`🔍 [Auth] User status: verified=${user.emailVerified}, active=${user.isActive}`)

    // Check password
    console.log(`🔐 [Auth] Comparing password for user: ${email}`)
    console.log(`🔐 [Auth] Password length: ${password.length}`)
    console.log(`🔐 [Auth] Hash length: ${user.passwordHash.length}`)
    console.log(`🔐 [Auth] Hash starts with: ${user.passwordHash.substring(0, 10)}`)

    const isValidPassword = await bcrypt.compare(password, user.passwordHash)
    if (!isValidPassword) {
      console.log(`❌ [Auth] Invalid password for user: ${email}`)
      console.log(`❌ [Auth] Password received: "${password}"`)
      return c.json({ error: 'Invalid email or password' }, 401)
    }

    console.log(`✅ [Auth] Password valid for user: ${email}`)

    // Check if email is verified
    if (!user.emailVerified) {
      // Generate new OTP and send
      const otp = generateOTP()
      const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000)
      
      await db.update(clientUsers)
        .set({ otpCode: otp, otpExpiresAt })
        .where(eq(clientUsers.id, user.id))
      
      await sendOTPEmail(email, otp, user.name)
      
      return c.json({
        error: 'Email not verified. A new OTP has been sent to your email.',
        requiresVerification: true,
        email
      }, 403)
    }

    // Check if account is active
    if (!user.isActive) {
      return c.json({ error: 'Account is deactivated. Please contact support.' }, 403)
    }

    // Update last login
    await db.update(clientUsers)
      .set({ lastLogin: new Date() })
      .where(eq(clientUsers.id, user.id))

    // Get client information for school token
    const [client] = await db.select()
      .from(clients)
      .where(eq(clients.id, user.clientId!))
      .limit(1)

    if (!client) {
      return c.json({ error: 'School information not found' }, 404)
    }

    // Generate school-specific token
    const token = generateSchoolToken(
      user.id,
      user.email,
      user.role || 'admin',
      user.clientId!,
      client.schoolName,
      [] // permissions - can be expanded later
    )

    return c.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        clientId: user.clientId,
        schoolName: client.schoolName
      }
    })

  } catch (error) {
    console.error('Login error:', error)
    return c.json({ error: 'Login failed. Please try again.' }, 500)
  }
})

// Verify OTP endpoint
app.post('/verify-otp', zValidator('json', verifyOtpSchema), async (c) => {
  try {
    const { email, otp } = c.req.valid('json')
    
    // Find user
    const [user] = await db.select().from(clientUsers).where(eq(clientUsers.email, email)).limit(1)
    
    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    // Check OTP
    if (!user.otpCode || user.otpCode !== otp) {
      return c.json({ error: 'Invalid OTP code' }, 400)
    }

    // Check OTP expiry
    if (!user.otpExpiresAt || new Date() > user.otpExpiresAt) {
      return c.json({ error: 'OTP has expired. Please request a new one.' }, 400)
    }

    // Verify email and clear OTP
    await db.update(clientUsers)
      .set({
        emailVerified: true,
        otpCode: null,
        otpExpiresAt: null,
        lastLogin: new Date()
      })
      .where(eq(clientUsers.id, user.id))

    // Get client information for school token
    const [client] = await db.select()
      .from(clients)
      .where(eq(clients.id, user.clientId!))
      .limit(1)

    if (!client) {
      return c.json({ error: 'School information not found' }, 404)
    }

    // Generate school-specific token
    const token = generateSchoolToken(
      user.id,
      user.email,
      user.role || 'admin',
      user.clientId!,
      client.schoolName,
      [] // permissions - can be expanded later
    )

    return c.json({
      message: 'Email verified successfully',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        clientId: user.clientId,
        schoolName: client.schoolName
      }
    })

  } catch (error) {
    console.error('OTP verification error:', error)
    return c.json({ error: 'Verification failed. Please try again.' }, 500)
  }
})

// Resend OTP endpoint
app.post('/resend-otp', zValidator('json', resendOtpSchema), async (c) => {
  try {
    const { email } = c.req.valid('json')

    // Find user
    const [user] = await db.select().from(clientUsers).where(eq(clientUsers.email, email)).limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    if (user.emailVerified) {
      return c.json({ error: 'Email is already verified' }, 400)
    }

    // Generate new OTP
    const otp = generateOTP()
    const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000)

    await db.update(clientUsers)
      .set({ otpCode: otp, otpExpiresAt })
      .where(eq(clientUsers.id, user.id))

    // Send OTP email
    const emailSent = await sendOTPEmail(email, otp, user.name)

    if (!emailSent) {
      return c.json({ error: 'Failed to send OTP email. Please try again.' }, 500)
    }

    return c.json({
      message: 'New OTP sent to your email'
    })

  } catch (error) {
    console.error('Resend OTP error:', error)
    return c.json({ error: 'Failed to resend OTP. Please try again.' }, 500)
  }
})

// Get user profile endpoint (protected)
app.get('/profile', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user with client data
    const [user] = await db
      .select({
        id: clientUsers.id,
        email: clientUsers.email,
        name: clientUsers.name,
        role: clientUsers.role,
        isActive: clientUsers.isActive,
        emailVerified: clientUsers.emailVerified,
        lastLogin: clientUsers.lastLogin,
        createdAt: clientUsers.createdAt,
        clientId: clientUsers.clientId,
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        phone: clients.phone,
        address: clients.address,
        contactPerson: clients.contactPerson,
        actualStudentCount: clients.actualStudentCount,
        classFee: clients.classFee,
        onboardingStatus: clients.onboardingStatus,
        status: clients.status,
        clientCreatedAt: clients.createdAt
      })
      .from(clientUsers)
      .leftJoin(clients, eq(clientUsers.clientId, clients.id))
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    if (!user.isActive) {
      return c.json({ error: 'Account is deactivated' }, 403)
    }

    return c.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        emailVerified: user.emailVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt
      },
      client: {
        id: user.clientId,
        schoolName: user.schoolName,
        schoolCode: user.schoolCode,
        phone: user.phone,
        address: user.address,
        contactPerson: user.contactPerson,
        actualStudentCount: user.actualStudentCount,
        classFee: user.classFee,
        onboardingStatus: user.onboardingStatus,
        status: user.status,
        createdAt: user.clientCreatedAt
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }

    console.error('Profile fetch error:', error)
    return c.json({ error: 'Failed to fetch profile' }, 500)
  }
})

// Update user profile endpoint (protected)
const updateUserProfileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  phone: z.string().min(10, 'Phone number must be at least 10 digits').optional(),
})

app.put('/profile/user', zValidator('json', updateUserProfileSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    const updates = c.req.valid('json')

    // Update user profile
    const [updatedUser] = await db
      .update(clientUsers)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(clientUsers.id, decoded.userId))
      .returning()

    if (!updatedUser) {
      return c.json({ error: 'User not found' }, 404)
    }

    return c.json({
      success: true,
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        role: updatedUser.role,
        emailVerified: updatedUser.emailVerified,
        lastLogin: updatedUser.lastLogin,
        createdAt: updatedUser.createdAt
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error updating user profile:', error)
    return c.json({ error: 'Failed to update profile' }, 500)
  }
})

// Update school information endpoint (protected)
const updateSchoolInfoSchema = z.object({
  schoolName: z.string().min(2, 'School name must be at least 2 characters').optional(),
  address: z.string().min(10, 'Address must be at least 10 characters').optional(),
  contactPerson: z.string().min(2, 'Contact person name must be at least 2 characters').optional(),
  actualStudentCount: z.number().min(1, 'Must have at least 1 student').optional(),
  classFee: z.number().min(0, 'Class fee must be a positive number').optional(),
})

app.put('/profile/school', zValidator('json', updateSchoolInfoSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user's client ID
    const [user] = await db
      .select({ clientId: clientUsers.clientId })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User or school not found' }, 404)
    }

    const updates = c.req.valid('json')

    // Convert classFee to string for decimal column if provided
    const processedUpdates = {
      ...updates,
      classFee: updates.classFee !== undefined ? updates.classFee?.toString() : undefined
    }

    // Update school information
    const [updatedClient] = await db
      .update(clients)
      .set({
        ...processedUpdates,
        updatedAt: new Date()
      })
      .where(eq(clients.id, user.clientId))
      .returning()

    if (!updatedClient) {
      return c.json({ error: 'School not found' }, 404)
    }

    return c.json({
      success: true,
      message: 'School information updated successfully',
      client: {
        id: updatedClient.id,
        schoolName: updatedClient.schoolName,
        schoolCode: updatedClient.schoolCode,
        phone: updatedClient.phone,
        address: updatedClient.address,
        contactPerson: updatedClient.contactPerson,
        actualStudentCount: updatedClient.actualStudentCount,
        classFee: updatedClient.classFee,
        onboardingStatus: updatedClient.onboardingStatus,
        status: updatedClient.status,
        createdAt: updatedClient.createdAt
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error updating school information:', error)
    return c.json({ error: 'Failed to update school information' }, 500)
  }
})

// Change password endpoint (protected)
const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "New passwords don't match",
  path: ["confirmPassword"],
})

app.put('/profile/password', zValidator('json', changePasswordSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    const { currentPassword, newPassword } = c.req.valid('json')

    // Get current user
    const [user] = await db
      .select({ passwordHash: clientUsers.passwordHash })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash)
    if (!isCurrentPasswordValid) {
      return c.json({ error: 'Current password is incorrect' }, 400)
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, 12)

    // Update password
    await db
      .update(clientUsers)
      .set({
        passwordHash: newPasswordHash,
        updatedAt: new Date()
      })
      .where(eq(clientUsers.id, decoded.userId))

    return c.json({
      success: true,
      message: 'Password changed successfully'
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error changing password:', error)
    return c.json({ error: 'Failed to change password' }, 500)
  }
})

// Resend email verification endpoint (protected)
app.post('/profile/resend-verification', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user details
    const [user] = await db
      .select({ email: clientUsers.email, name: clientUsers.name, emailVerified: clientUsers.emailVerified })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user) {
      return c.json({ error: 'User not found' }, 404)
    }

    if (user.emailVerified) {
      return c.json({ error: 'Email is already verified' }, 400)
    }

    // Generate new OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString()
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

    // Update user with new OTP
    await db
      .update(clientUsers)
      .set({
        otpCode: otp,
        otpExpiresAt: otpExpiry,
        updatedAt: new Date()
      })
      .where(eq(clientUsers.id, decoded.userId))

    // Send verification email
    const resend = new Resend(process.env.RESEND_API_KEY)

    await resend.emails.send({
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: user.email,
      subject: 'Verify Your Email - Schopio',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Email Verification</h2>
          <p>Hello ${user.name},</p>
          <p>Please use the following OTP to verify your email address:</p>
          <div style="background: #f3f4f6; padding: 20px; text-align: center; margin: 20px 0;">
            <h1 style="color: #1f2937; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
          </div>
          <p>This OTP will expire in 10 minutes.</p>
          <p>If you didn't request this verification, please ignore this email.</p>
          <hr style="margin: 30px 0;">
          <p style="color: #6b7280; font-size: 14px;">
            Best regards,<br>
            The Schopio Team
          </p>
        </div>
      `
    })

    return c.json({
      success: true,
      message: 'Verification email sent successfully'
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error sending verification email:', error)
    return c.json({ error: 'Failed to send verification email' }, 500)
  }
})

// Validate referral code endpoint (public)
app.post('/referral/validate', async (c) => {
  try {
    const { code } = await c.req.json()

    if (!code || typeof code !== 'string') {
      return c.json({ error: 'Referral code is required' }, 400)
    }

    // Check if referral code exists and is active
    const [referralCode] = await db
      .select({
        id: referralCodes.id,
        code: referralCodes.code,
        partnerId: referralCodes.partnerId,
        isActive: referralCodes.isActive,
        usageCount: referralCodes.usageCount,
        maxUsage: referralCodes.maxUsage,
        partnerName: partners.name,
        partnerCompany: partners.companyName
      })
      .from(referralCodes)
      .innerJoin(partners, eq(referralCodes.partnerId, partners.id))
      .where(and(
        eq(referralCodes.code, code.toUpperCase()),
        eq(referralCodes.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (!referralCode) {
      return c.json({
        valid: false,
        error: 'Invalid or inactive referral code'
      }, 400)
    }

    // Check if referral code has reached max usage
    if (referralCode.maxUsage && (referralCode.usageCount || 0) >= referralCode.maxUsage) {
      return c.json({
        valid: false,
        error: 'Referral code has reached maximum usage limit'
      }, 400)
    }

    return c.json({
      valid: true,
      referralCode: {
        id: referralCode.id,
        code: referralCode.code,
        partnerName: referralCode.partnerName,
        partnerCompany: referralCode.partnerCompany,
        usageCount: referralCode.usageCount,
        maxUsage: referralCode.maxUsage
      }
    })

  } catch (error) {
    console.error('Error validating referral code:', error)
    return c.json({ error: 'Failed to validate referral code' }, 500)
  }
})

// Apply referral code to school (protected)
app.post('/referral/apply', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    const { code } = await c.req.json()

    if (!code || typeof code !== 'string') {
      return c.json({ error: 'Referral code is required' }, 400)
    }

    // Get user and client information
    const [user] = await db
      .select({
        id: clientUsers.id,
        clientId: clientUsers.clientId,
        email: clientUsers.email
      })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User or school not found' }, 404)
    }

    // Check if school already has a referral
    const [existingReferral] = await db
      .select({ id: schoolReferrals.id })
      .from(schoolReferrals)
      .where(and(
        eq(schoolReferrals.clientId, user.clientId),
        eq(schoolReferrals.isActive, true)
      ))
      .limit(1)

    if (existingReferral) {
      return c.json({ error: 'School already has an active referral code applied' }, 400)
    }

    // Validate referral code
    const [referralCode] = await db
      .select({
        id: referralCodes.id,
        partnerId: referralCodes.partnerId,
        code: referralCodes.code,
        isActive: referralCodes.isActive,
        usageCount: referralCodes.usageCount,
        maxUsage: referralCodes.maxUsage,
        partnerName: partners.name,
        partnerCompany: partners.companyName
      })
      .from(referralCodes)
      .innerJoin(partners, eq(referralCodes.partnerId, partners.id))
      .where(and(
        eq(referralCodes.code, code.toUpperCase()),
        eq(referralCodes.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (!referralCode) {
      return c.json({ error: 'Invalid or inactive referral code' }, 400)
    }

    // Check usage limit
    if (referralCode.maxUsage && (referralCode.usageCount || 0) >= referralCode.maxUsage) {
      return c.json({ error: 'Referral code has reached maximum usage limit' }, 400)
    }

    // Get client IP and user agent
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Apply referral code (Neon HTTP driver doesn't support transactions)
    // Create school referral record
    await db.insert(schoolReferrals).values({
      clientId: user.clientId!,
      partnerId: referralCode.partnerId,
      referralCodeId: referralCode.id,
      referralSource: 'profile_update',
      ipAddress: ipAddress,
      userAgent: userAgent,
      appliedBy: user.id
    })

    // Update referral code usage count
    await db
      .update(referralCodes)
      .set({
        usageCount: (referralCode.usageCount || 0) + 1
      })
      .where(eq(referralCodes.id, referralCode.id))

    return c.json({
      success: true,
      message: 'Referral code applied successfully',
      referral: {
        code: referralCode.code,
        partnerName: referralCode.partnerName,
        partnerCompany: referralCode.partnerCompany,
        appliedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error applying referral code:', error)
    return c.json({ error: 'Failed to apply referral code' }, 500)
  }
})

// Get school referral status (protected)
app.get('/referral/status', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId) {
      return c.json({ error: 'Invalid token' }, 401)
    }

    // Get user and client information
    const [user] = await db
      .select({
        id: clientUsers.id,
        clientId: clientUsers.clientId
      })
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User or school not found' }, 404)
    }

    // Check if school has a referral (including rejected ones)
    const [referral] = await db
      .select({
        id: schoolReferrals.id,
        referralCode: referralCodes.code,
        partnerName: partners.name,
        partnerCompany: partners.companyName,
        referredAt: schoolReferrals.referredAt,
        referralSource: schoolReferrals.referralSource,
        isActive: schoolReferrals.isActive,
        verifiedAt: schoolReferrals.verifiedAt,
        verifiedBy: schoolReferrals.verifiedBy,
        rejectionReason: schoolReferrals.rejectionReason
      })
      .from(schoolReferrals)
      .innerJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .innerJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(eq(schoolReferrals.clientId, user.clientId))
      .orderBy(schoolReferrals.referredAt)
      .limit(1)

    if (!referral) {
      return c.json({
        hasReferral: false,
        message: 'No referral code applied'
      })
    }

    // Determine referral status
    const isVerified = !!referral.verifiedAt
    const isRejected = !referral.isActive && !!referral.verifiedBy
    const isPending = referral.isActive && !referral.verifiedAt

    return c.json({
      hasReferral: true,
      referral: {
        code: referral.referralCode,
        partnerName: referral.partnerName,
        partnerCompany: referral.partnerCompany,
        appliedAt: referral.referredAt,
        source: referral.referralSource,
        isVerified: isVerified,
        verifiedAt: referral.verifiedAt,
        isRejected: isRejected,
        isPending: isPending,
        rejectionReason: referral.rejectionReason,
        status: isVerified ? 'verified' : isRejected ? 'rejected' : 'pending'
      }
    })

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error getting referral status:', error)
    return c.json({ error: 'Failed to get referral status' }, 500)
  }
})

// ===== SOFTWARE REQUEST ENDPOINTS =====

// Software request validation schemas
const softwareRequestSchema = z.object({
  requestType: z.enum(['demo', 'production'], { required_error: 'Request type is required' }),
  studentCount: z.number().min(1, 'Student count must be at least 1'),
  facultyCount: z.number().min(1, 'Faculty count must be at least 1'),
  completeAddress: z.string().min(10, 'Complete address must be at least 10 characters'),
  contactNumber: z.string().min(10, 'Contact number must be at least 10 digits'),
  primaryEmail: z.string().email('Invalid email address'),

  // Simplified fee structure (required for production requests)
  averageMonthlyFee: z.number().min(0, 'Average monthly fee must be a positive number').optional(),

  // Legacy fee fields (for backward compatibility)
  class1Fee: z.number().optional(),
  class4Fee: z.number().optional(),
  class6Fee: z.number().optional(),
  class10Fee: z.number().optional(),
  class11Fee: z.number().optional(),
  class12Fee: z.number().optional(),
  class1112Fee: z.number().optional(), // Legacy field for backward compatibility

  // Terms acceptance (required for production requests)
  termsAccepted: z.boolean().optional(),
  termsVersion: z.string().optional(),
}).refine((data) => {
  // For production requests, require average fee and terms acceptance
  if (data.requestType === 'production') {
    return data.averageMonthlyFee && data.averageMonthlyFee > 0 && data.termsAccepted === true && data.termsVersion
  }
  return true
}, {
  message: "Production requests require average monthly fee and terms acceptance",
  path: ["requestType"],
})

// Helper function to calculate average monthly fee from individual class fees (for backward compatibility)
const calculateAverageFeeFromClasses = (fees: { [key: string]: number | undefined }) => {
  const validFees = Object.values(fees).filter(fee => fee && fee > 0) as number[]
  if (validFees.length === 0) return null
  return validFees.reduce((sum, fee) => sum + fee, 0) / validFees.length
}

// Helper function to get the average fee (prioritize direct input, fallback to calculation)
const getAverageMonthlyFee = (data: any) => {
  // If averageMonthlyFee is provided directly, use it
  if (data.averageMonthlyFee && data.averageMonthlyFee > 0) {
    return data.averageMonthlyFee
  }

  // Fallback: calculate from individual class fees (for backward compatibility)
  return calculateAverageFeeFromClasses({
    class1Fee: data.class1Fee,
    class4Fee: data.class4Fee,
    class6Fee: data.class6Fee,
    class10Fee: data.class10Fee,
    class11Fee: data.class11Fee,
    class12Fee: data.class12Fee,
  })
}

// Create software request endpoint
app.post('/software-request', zValidator('json', softwareRequestSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any

    // Get user details
    const [user] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User not found or not associated with a school' }, 404)
    }

    const data = c.req.valid('json')

    // Check if user already has a pending or active request
    const existingRequest = await db
      .select()
      .from(softwareRequests)
      .where(and(
        eq(softwareRequests.clientId, user.clientId),
        eq(softwareRequests.status, 'pending')
      ))
      .limit(1)

    if (existingRequest.length > 0) {
      return c.json({ error: 'You already have a pending software request' }, 400)
    }

    // Get average monthly fee for production requests
    let averageMonthlyFee = null
    if (data.requestType === 'production') {
      averageMonthlyFee = getAverageMonthlyFee(data)
    }

    // Get client IP and user agent
    const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Create software request
    const [newRequest] = await db.insert(softwareRequests).values({
      clientId: user.clientId,
      requestType: data.requestType,
      studentCount: data.studentCount,
      facultyCount: data.facultyCount,
      completeAddress: data.completeAddress,
      contactNumber: data.contactNumber,
      primaryEmail: data.primaryEmail,
      averageMonthlyFee: averageMonthlyFee ? averageMonthlyFee.toString() : null,
      // Legacy fields for backward compatibility
      class1Fee: data.class1Fee ? data.class1Fee.toString() : null,
      class4Fee: data.class4Fee ? data.class4Fee.toString() : null,
      class6Fee: data.class6Fee ? data.class6Fee.toString() : null,
      class10Fee: data.class10Fee ? data.class10Fee.toString() : null,
      class11Fee: data.class11Fee ? data.class11Fee.toString() : null,
      class12Fee: data.class12Fee ? data.class12Fee.toString() : null,
      class1112Fee: data.class1112Fee ? data.class1112Fee.toString() : null, // Legacy field
      calculatedAverageFee: averageMonthlyFee ? averageMonthlyFee.toString() : null, // Legacy field
      termsAccepted: data.termsAccepted || false,
      termsAcceptedAt: data.termsAccepted ? new Date() : null,
      termsVersion: data.termsVersion || null,
      ipAddress: ipAddress,
      userAgent: userAgent,
      status: 'pending'
    }).returning()

    // Create status history entry
    await db.insert(requestStatusHistory).values({
      requestId: newRequest.id,
      fromStatus: null,
      toStatus: 'pending',
      changedBy: user.id,
      changeReason: 'Initial request submission',
      metadata: { requestType: data.requestType }
    })

    return c.json({
      message: 'Software request submitted successfully',
      requestId: newRequest.id,
      requestType: data.requestType,
      status: 'pending'
    })

  } catch (error: any) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error creating software request:', error)
    return c.json({ error: 'Failed to create software request' }, 500)
  }
})

// Get software request status endpoint
app.get('/software-request/status', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any

    // Get user details
    const [user] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User not found or not associated with a school' }, 404)
    }

    // Get all software requests for this client to properly handle demo/production workflow
    const allRequests = await db
      .select()
      .from(softwareRequests)
      .where(eq(softwareRequests.clientId, user.clientId))
      .orderBy(desc(softwareRequests.createdAt))

    if (allRequests.length === 0) {
      return c.json({ hasRequest: false })
    }

    // Find the most relevant request to display
    // Priority: 1. Active production request, 2. Latest production request, 3. Latest demo request
    let primaryRequest = allRequests[0] // Default to latest

    const productionRequests = allRequests.filter(r => r.requestType === 'production')
    const demoRequests = allRequests.filter(r => r.requestType === 'demo')

    if (productionRequests.length > 0) {
      // If there are production requests, prioritize the most recent one
      const activeProduction = productionRequests.find(r =>
        r.status && ['approved', 'activated'].includes(r.status)
      )
      primaryRequest = activeProduction || productionRequests[0]
    } else if (demoRequests.length > 0) {
      // If only demo requests, use the latest one
      primaryRequest = demoRequests[0]
    }

    // Get status history for the primary request
    const statusHistory = await db
      .select()
      .from(requestStatusHistory)
      .where(eq(requestStatusHistory.requestId, primaryRequest.id))
      .orderBy(requestStatusHistory.createdAt)

    // Determine if user can upgrade from demo to production
    // Can upgrade if: 1. Has an activated/approved demo, 2. No production request exists
    const hasActivatedDemo = demoRequests.some(r =>
      r.status && ['activated', 'approved'].includes(r.status)
    )
    const hasProductionRequest = productionRequests.length > 0
    const canUpgrade = hasActivatedDemo && !hasProductionRequest

    return c.json({
      hasRequest: true,
      request: {
        id: primaryRequest.id,
        requestType: primaryRequest.requestType,
        status: primaryRequest.status,
        studentCount: primaryRequest.studentCount,
        facultyCount: primaryRequest.facultyCount,
        completeAddress: primaryRequest.completeAddress,
        contactNumber: primaryRequest.contactNumber,
        primaryEmail: primaryRequest.primaryEmail,
        calculatedAverageFee: primaryRequest.calculatedAverageFee,
        termsAccepted: primaryRequest.termsAccepted,
        termsVersion: primaryRequest.termsVersion,
        createdAt: primaryRequest.createdAt,
        approvedAt: primaryRequest.approvedAt,
        activatedAt: primaryRequest.activatedAt,
        reviewNotes: primaryRequest.reviewNotes,
        rejectionReason: primaryRequest.rejectionReason
      },
      statusHistory: statusHistory.map(history => ({
        fromStatus: history.fromStatus,
        toStatus: history.toStatus,
        changeReason: history.changeReason,
        createdAt: history.createdAt
      })),
      canUpgrade,
      // Additional context for better UI decisions
      requestSummary: {
        totalRequests: allRequests.length,
        demoRequests: demoRequests.length,
        productionRequests: productionRequests.length,
        hasActivatedDemo,
        hasProductionRequest
      }
    })

  } catch (error: any) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error getting software request status:', error)
    return c.json({ error: 'Failed to get software request status' }, 500)
  }
})

// Upgrade demo to production endpoint
const upgradeToProductionSchema = z.object({
  // Simplified fee structure (required for production requests)
  averageMonthlyFee: z.number().min(0, 'Average monthly fee must be a positive number in ₹'),

  // Legacy fee fields (for backward compatibility)
  class1Fee: z.number().min(0, 'Class 1 monthly fee must be a positive number in ₹').optional(),
  class4Fee: z.number().min(0, 'Class 4 monthly fee must be a positive number in ₹').optional(),
  class6Fee: z.number().min(0, 'Class 6 monthly fee must be a positive number in ₹').optional(),
  class10Fee: z.number().min(0, 'Class 10 monthly fee must be a positive number in ₹').optional(),
  class11Fee: z.number().min(0, 'Class 11 monthly fee must be a positive number in ₹').optional(),
  class12Fee: z.number().min(0, 'Class 12 monthly fee must be a positive number in ₹').optional(),

  termsAccepted: z.boolean().refine(val => val === true, 'Terms and conditions must be accepted'),
  termsVersion: z.string().min(1, 'Terms version is required')
})

app.post('/software-request/upgrade-to-production', zValidator('json', upgradeToProductionSchema), async (c) => {
  try {
    const authHeader = c.req.header('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
    const data = c.req.valid('json')

    // Get user details
    const [user] = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.id, decoded.userId))
      .limit(1)

    if (!user || !user.clientId) {
      return c.json({ error: 'User not found or not associated with a school' }, 404)
    }

    // Check for existing demo request
    const [existingRequest] = await db
      .select()
      .from(softwareRequests)
      .where(
        and(
          eq(softwareRequests.clientId, user.clientId),
          eq(softwareRequests.requestType, 'demo')
        )
      )
      .orderBy(desc(softwareRequests.createdAt))
      .limit(1)

    if (!existingRequest) {
      return c.json({ error: 'No demo request found to upgrade' }, 404)
    }

    // Check if there's already a production request
    const [existingProductionRequest] = await db
      .select()
      .from(softwareRequests)
      .where(
        and(
          eq(softwareRequests.clientId, user.clientId),
          eq(softwareRequests.requestType, 'production')
        )
      )
      .limit(1)

    if (existingProductionRequest) {
      return c.json({
        error: 'You already have a production request. Cannot upgrade demo request.'
      }, 400)
    }

    if (existingRequest.status !== 'activated' && existingRequest.status !== 'approved') {
      return c.json({
        error: 'Demo request must be approved or activated before upgrading to production'
      }, 400)
    }

    // Get average monthly fee (use direct input or calculate from legacy fields)
    const averageMonthlyFee = getAverageMonthlyFee(data)

    // Get client IP and user agent for audit trail
    const clientIP = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
    const userAgent = c.req.header('user-agent') || 'unknown'

    // Check if client has partner attribution for conversion tracking
    const [partnerAttribution] = await db
      .select({
        partnerId: schoolReferrals.partnerId,
        referralCodeId: schoolReferrals.referralCodeId,
        partnerName: partners.name,
        partnerCode: partners.partnerCode
      })
      .from(schoolReferrals)
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(and(
        eq(schoolReferrals.clientId, user.clientId!),
        eq(schoolReferrals.isActive, true)
      ))
      .limit(1)

    // Update existing request to production (without transaction due to Neon HTTP driver limitation)
    await db
      .update(softwareRequests)
      .set({
        requestType: 'production',
        averageMonthlyFee: averageMonthlyFee ? averageMonthlyFee.toString() : null,
        // Legacy fields for backward compatibility
        class1Fee: data.class1Fee ? data.class1Fee.toString() : null,
        class4Fee: data.class4Fee ? data.class4Fee.toString() : null,
        class6Fee: data.class6Fee ? data.class6Fee.toString() : null,
        class10Fee: data.class10Fee ? data.class10Fee.toString() : null,
        class11Fee: data.class11Fee ? data.class11Fee.toString() : null,
        class12Fee: data.class12Fee ? data.class12Fee.toString() : null,
        calculatedAverageFee: averageMonthlyFee ? averageMonthlyFee.toString() : null, // Legacy field
        termsAccepted: data.termsAccepted,
        termsAcceptedAt: new Date(),
        termsVersion: data.termsVersion,
        ipAddress: clientIP,
        userAgent: userAgent,
        status: 'pending', // Reset to pending for admin review
        updatedAt: new Date()
      })
      .where(eq(softwareRequests.id, existingRequest.id))

    // Log partner conversion if applicable
    if (partnerAttribution) {
      console.log(`Demo to Production conversion tracked for Partner: ${partnerAttribution.partnerName} (${partnerAttribution.partnerCode}) - Client: ${user.clientId}`)
    }

    // Add status history entry
    await db.insert(requestStatusHistory).values({
      requestId: existingRequest.id,
      fromStatus: existingRequest.status,
      toStatus: 'pending',
      changedBy: user.id,
      changeReason: 'Upgraded from demo to production request',
      metadata: {
        upgradeType: 'demo_to_production',
        previousRequestType: 'demo',
        newRequestType: 'production',
        averageFee: averageMonthlyFee,
        clientIP,
        userAgent
      }
    })

    return c.json({
      success: true,
      message: 'Successfully upgraded to production request. Your request is now pending admin review.',
      requestId: existingRequest.id,
      newStatus: 'pending'
    })

  } catch (error: any) {
    if (error instanceof jwt.JsonWebTokenError) {
      return c.json({ error: 'Invalid token' }, 401)
    }
    if (error instanceof jwt.TokenExpiredError) {
      return c.json({ error: 'Token expired' }, 401)
    }
    console.error('Error upgrading to production:', error)
    return c.json({ error: 'Failed to upgrade to production' }, 500)
  }
})

// Get active terms & conditions endpoint
app.get('/terms-conditions', async (c) => {
  try {
    const [activeTerms] = await db
      .select()
      .from(termsConditions)
      .where(eq(termsConditions.isActive, true))
      .orderBy(termsConditions.effectiveDate)
      .limit(1)

    if (!activeTerms) {
      return c.json({ error: 'No active terms and conditions found' }, 404)
    }

    return c.json({
      version: activeTerms.version,
      title: activeTerms.title,
      content: activeTerms.content,
      effectiveDate: activeTerms.effectiveDate
    })

  } catch (error: any) {
    console.error('Error getting terms and conditions:', error)
    return c.json({ error: 'Failed to get terms and conditions' }, 500)
  }
})

// Partner login schema
const partnerLoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required')
})

// Partner login endpoint
app.post('/partner/login', zValidator('json', partnerLoginSchema), async (c) => {
  try {
    const { email, password } = c.req.valid('json')

    // Find partner
    const [partner] = await db
      .select({
        id: partners.id,
        email: partners.email,
        passwordHash: partners.passwordHash,
        name: partners.name,
        partnerCode: partners.partnerCode,
        isActive: partners.isActive,
        phone: partners.phone,
        profitSharePercentage: partners.profitSharePercentage
      })
      .from(partners)
      .where(eq(partners.email, email))
      .limit(1)

    if (!partner) {
      return c.json({ error: 'Invalid email or password' }, 401)
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, partner.passwordHash)
    if (!isValidPassword) {
      return c.json({ error: 'Invalid email or password' }, 401)
    }

    // Check if partner is active
    if (!partner.isActive) {
      return c.json({ error: 'Partner account is deactivated' }, 403)
    }

    // Generate JWT token
    const token = generatePartnerAuthToken(partner.id, partner.email)

    return c.json({
      success: true,
      token,
      partner: {
        id: partner.id,
        name: partner.name,
        email: partner.email,
        phone: partner.phone,
        partnerCode: partner.partnerCode
      }
    })

  } catch (error: any) {
    console.error('Partner login error:', error)
    return c.json({ error: 'Login failed' }, 500)
  }
})

// Partner profile endpoint
app.get('/partner/profile', async (c) => {
  try {
    const authHeader = c.req.header('Authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ error: 'Authorization token required' }, 401)
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret-key') as any

    if (!decoded.userId || decoded.type !== 'partner') {
      return c.json({ error: 'Invalid token' }, 401)
    }

    const [partner] = await db.select().from(partners).where(eq(partners.id, decoded.userId)).limit(1)

    if (!partner || !partner.isActive) {
      return c.json({ error: 'Partner not found or inactive' }, 404)
    }

    return c.json({
      success: true,
      partner: {
        id: partner.id,
        name: partner.name,
        email: partner.email,
        companyName: partner.companyName,
        partnerCode: partner.partnerCode,
        phone: partner.phone,
        address: partner.address,
        createdAt: partner.createdAt
      }
    })

  } catch (error: any) {
    console.error('Partner profile error:', error)
    return c.json({ error: 'Failed to fetch profile' }, 500)
  }
})

export default app
