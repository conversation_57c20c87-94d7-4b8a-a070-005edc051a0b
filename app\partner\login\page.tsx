'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/src/components/ui/Button'
import { Input } from '@/src/components/ui/input'
import { Label } from '@/src/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/src/components/ui/Card'
import { Alert, AlertDescription } from '@/src/components/ui/alert'
import { Eye, EyeOff, Mail, Lock, ArrowLeft, Handshake } from 'lucide-react'
import Link from 'next/link'

export default function PartnerLoginPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  // Check if already authenticated and redirect
  useEffect(() => {
    const token = localStorage.getItem('partnerToken')
    if (token) {
      console.log('Partner already logged in, redirecting to dashboard...')
      // Use window.location for immediate redirect
      window.location.href = '/partner/dashboard'
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (loading) {
      console.log('Already processing login, ignoring duplicate submission')
      return
    }

    setLoading(true)
    setError('')

    console.log('Starting login process...')
    console.log('Form data:', { email: formData.email, password: '[HIDDEN]' })

    try {
      const response = await fetch('/api/auth/partner/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Login failed')
      }

      // Store partner token and data
      console.log('Login successful, storing token and redirecting...')
      console.log('Token received:', data.token ? 'Yes' : 'No')
      console.log('Partner data received:', data.partner ? 'Yes' : 'No')

      // Store token and partner data
      localStorage.setItem('partnerToken', data.token)
      localStorage.setItem('partner', JSON.stringify(data.partner))

      // Verify storage
      const storedToken = localStorage.getItem('partnerToken')
      const storedPartner = localStorage.getItem('partner')
      console.log('Token stored successfully:', storedToken ? 'Yes' : 'No')
      console.log('Partner data stored successfully:', storedPartner ? 'Yes' : 'No')

      // Redirect with longer delay to ensure storage is complete
      setTimeout(() => {
        console.log('Redirecting to dashboard...')
        window.location.replace('/partner/dashboard')
      }, 500)
    } catch (err: any) {
      console.error('Login error:', err)
      console.error('Error details:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      })
      setError(err.message || 'Login failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center text-emerald-600 hover:text-emerald-700 mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
          <div className="flex items-center justify-center mb-4">
            <Handshake className="w-8 h-8 text-emerald-600 mr-2" />
            <h1 className="text-3xl font-bold text-gray-900">
              <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">
                Schopio
              </span>
            </h1>
          </div>
          <p className="text-gray-600">Partner Portal</p>
          <p className="text-sm text-gray-500 mt-1">Access your referral dashboard and earnings</p>
        </div>

        {/* Login Card */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className="text-2xl font-bold text-center text-gray-900">
              Partner Login
            </CardTitle>
            <CardDescription className="text-center text-gray-600">
              Sign in to your partner account to manage referrals and track earnings
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert className="mb-6 border-red-200 bg-red-50">
                <AlertDescription className="text-red-700">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-700">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="pl-10 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-gray-700">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    className="pl-10 pr-10 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500"
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white font-medium py-2.5"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </div>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Need help accessing your account?{' '}
                <Link href="/contact" className="text-emerald-600 hover:text-emerald-700 font-medium">
                  Contact Support
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500">
            © 2025 Schopio. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  )
}
