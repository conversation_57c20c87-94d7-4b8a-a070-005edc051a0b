'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  CreditCard, 
  Download, 
  Calendar, 
  IndianRupee,
  Clock,
  CheckCircle,
  AlertCircle,
  Receipt
} from 'lucide-react'

interface Invoice {
  id: string
  invoiceNumber: string
  amount: string
  taxAmount: string
  totalAmount: string
  status: string
  issuedDate: string
  dueDate: string
  paidDate?: string
  billingCycle?: {
    cycleStart: string
    cycleEnd: string
    studentCount: number
  }
}

interface Payment {
  id: string
  amount: string
  currency: string
  status: string
  paymentMethod: string
  processedAt: string
  razorpayPaymentId?: string
  subscriptionId?: string
  paymentPeriod?: string
  description?: string
  invoice: {
    invoiceNumber: string
    issuedDate: string
  } | null
}

interface SubscriptionInfo {
  id: string
  planName: string
  studentCount: number
  pricePerStudent: string
  monthlyAmount: string
  billingCycle: string
  status: string
  startDate: string
  nextBillingDate: string
  autoRenew: boolean
  dueDate: number
  gracePeriodDays: number
  // Automatic billing fields
  razorpaySubscriptionId?: string
  razorpayCustomerId?: string
  razorpayPlanId?: string
  activatedAt?: string
  isAutomaticBillingEnabled: boolean
}

interface BillingSummary {
  // New subscription-based fields
  currentMonthAmount: number
  outstandingAmount: number
  paymentStatus: 'current' | 'due' | 'grace_period' | 'overdue_with_penalty' | 'no_subscription'
  nextBillingDate: string | null
  gracePeriodEnd: string | null
  penaltyAmount: number
  daysOverdue: number
  isInGracePeriod: boolean
  canMakePayment: boolean

  // Legacy fields for backward compatibility
  totalInvoiced: number
  totalPaid: number
  pendingAmount: number
}

export default function SchoolBillingPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null)
  const [billingSummary, setBillingSummary] = useState<BillingSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [paymentLoading, setPaymentLoading] = useState<string | null>(null)
  const [manualPaymentLoading, setManualPaymentLoading] = useState(false)

  useEffect(() => {
    fetchBillingData()
  }, [])

  // Helper function to check if subscription is newly created (within last 7 days)
  const isNewSubscription = (subscription: SubscriptionInfo): boolean => {
    if (!subscription.startDate) return false

    const subscriptionStart = new Date(subscription.startDate)
    const now = new Date()
    const daysDifference = Math.floor((now.getTime() - subscriptionStart.getTime()) / (1000 * 60 * 60 * 24))

    // Consider subscription "new" if created within last 7 days and no payments made yet
    return daysDifference <= 7
  }

  const fetchBillingData = async () => {
    try {
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch('/api/school/billing', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch billing data')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch billing data')
      }

      const data = result.data

      // Debug: Log the complete API response structure
      console.log('🔍 [Frontend Debug] Complete API Response:', {
        success: result.success,
        data: data,
        subscription: data.subscription,
        summary: data.summary
      })

      setInvoices(data.invoices || [])
      setPayments(data.payments || [])
      setBillingSummary(data.summary || null)

      // Set subscription data from billing response (no need for separate API call)
      if (data.subscription) {
        console.log('✅ [Frontend Debug] Setting subscription data:', data.subscription)
        setSubscription(data.subscription)
      } else {
        console.log('❌ [Frontend Debug] No subscription data found in API response')
        setSubscription(null)
      }

    } catch (error) {
      console.error('Error fetching billing data:', error)
    } finally {
      setLoading(false)
    }
  }



  const downloadInvoice = async (invoiceId: string) => {
    try {
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/school/billing/invoice/${invoiceId}/pdf`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to download invoice')
      }

      // Get the PDF blob
      const blob = await response.blob()

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Get filename from response headers or use default
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `invoice-${invoiceId}.pdf`
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }

      link.download = filename
      document.body.appendChild(link)
      link.click()

      // Cleanup
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

    } catch (error) {
      console.error('Error downloading invoice:', error)
      alert('Failed to download invoice. Please try again.')
    }
  }

  const downloadStatement = async () => {
    try {
      // For now, we'll generate a CSV statement with billing summary
      // In the future, this could be a PDF statement
      const csvContent = generateBillingStatement()

      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `billing-statement-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(link)
      link.click()

      // Cleanup
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

    } catch (error) {
      console.error('Error downloading statement:', error)
      alert('Failed to download statement. Please try again.')
    }
  }

  const generateBillingStatement = (): string => {
    const headers = ['Invoice Number', 'Date', 'Due Date', 'Amount', 'Tax', 'Total', 'Status']
    const rows = invoices.map(invoice => [
      invoice.invoiceNumber,
      formatDate(invoice.issuedDate),
      formatDate(invoice.dueDate),
      invoice.amount,
      invoice.taxAmount,
      invoice.totalAmount,
      invoice.status
    ])

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n')

    return csvContent
  }

  const makeManualPayment = async () => {
    if (!subscription || !billingSummary) return

    setManualPaymentLoading(true)
    try {
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      // Step 1: Create manual payment order
      const orderResponse = await fetch('/api/subscriptions/create-manual-payment-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subscriptionId: subscription.id,
          amount: Number(billingSummary.outstandingAmount) || 0
        })
      })

      const orderData = await orderResponse.json()

      if (!orderData.success) {
        throw new Error(orderData.error || 'Failed to create payment order')
      }

      // Step 2: Initialize Razorpay manual payment
      const options = {
        key: orderData.data.keyId,
        amount: orderData.data.amount * 100, // Convert to paise
        currency: orderData.data.currency,
        name: 'Schopio',
        description: orderData.data.description,
        order_id: orderData.data.orderId,
        image: '/logo.png',
        handler: async function (response: any) {
          try {
            console.log('Manual payment response:', response)

            // Verify manual payment
            const verifyResponse = await fetch('/api/subscriptions/verify-manual-payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_signature: response.razorpay_signature,
                subscriptionId: subscription.id
              })
            })

            const verifyData = await verifyResponse.json()

            if (verifyData.success) {
              alert('Payment successful! Your subscription is now current.')
              fetchBillingData() // Refresh data
            } else {
              throw new Error(verifyData.error || 'Payment verification failed')
            }
          } catch (error) {
            console.error('Payment verification error:', error)
            alert(error instanceof Error ? error.message : 'Payment verification failed')
          } finally {
            setManualPaymentLoading(false)
          }
        },
        prefill: {
          name: orderData.data.schoolName || '',
          email: '', // Will be filled from user data
          contact: ''
        },
        theme: {
          color: '#3399cc'
        },
        modal: {
          ondismiss: function() {
            console.log('Manual payment cancelled')
            setManualPaymentLoading(false)
          }
        }
      }

      // Load Razorpay script if not already loaded
      if (!(window as any).Razorpay) {
        const script = document.createElement('script')
        script.src = 'https://checkout.razorpay.com/v1/checkout.js'
        script.onload = () => {
          const rzp = new (window as any).Razorpay(options)
          rzp.open()
        }
        document.body.appendChild(script)
      } else {
        const rzp = new (window as any).Razorpay(options)
        rzp.open()
      }

    } catch (error) {
      console.error('Manual payment error:', error)
      alert(error instanceof Error ? error.message : 'Failed to process payment')
    } finally {
      setManualPaymentLoading(false)
    }
  }

  const handlePayment = async (invoice: Invoice) => {
    setPaymentLoading(invoice.id)

    try {
      // Create Razorpay order
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const orderResponse = await fetch('/api/client-payments/create-order', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          invoiceId: invoice.id,
          paymentMethod: 'card'
        })
      })

      if (!orderResponse.ok) {
        const errorData = await orderResponse.json()
        throw new Error(errorData.error || 'Failed to create payment order')
      }

      const orderData = await orderResponse.json()
      const { order, invoice: invoiceData, razorpayConfig } = orderData.data

      // Check if we're using mock payments (invalid Razorpay key)
      const isUsingMockPayments = !razorpayConfig.keyId ||
        razorpayConfig.keyId.includes('1234567890') ||
        razorpayConfig.keyId === 'rzp_test_1234567890' ||
        razorpayConfig.keyId.includes('XXXXXXXXXXXXXX') ||
        !/^rzp_(test|live)_[A-Za-z0-9]{14}$/.test(razorpayConfig.keyId)

      if (isUsingMockPayments) {
        // Handle mock payment flow for invoice
        const mockAlert = `🎭 Mock Payment Mode - Invoice Payment

This is a test payment simulation because:
• No valid Razorpay credentials are configured
• Using placeholder/demo credentials

In production with real Razorpay credentials:
• Real payment gateway would open
• Actual payment processing would occur
• Bank/card verification required

Amount: ₹${invoice.totalAmount}
Invoice: ${invoice.invoiceNumber}

Click OK to simulate successful payment for testing.`

        if (!confirm(mockAlert)) {
          setPaymentLoading(null)
          return
        }

        // Simulate successful payment for testing
        const mockPaymentResponse = {
          razorpay_order_id: order.id,
          razorpay_payment_id: `pay_${Math.random().toString(36).substr(2, 9)}`,
          razorpay_signature: `mock_signature_${Math.random().toString(36).substr(2, 9)}`
        }

        // Proceed with payment verification
        try {
          const verifyResponse = await fetch('/api/client-payments/verify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              razorpayOrderId: mockPaymentResponse.razorpay_order_id,
              razorpayPaymentId: mockPaymentResponse.razorpay_payment_id,
              razorpaySignature: mockPaymentResponse.razorpay_signature,
              invoiceId: invoice.id
            })
          })

          if (verifyResponse.ok) {
            const verifyData = await verifyResponse.json()
            if (verifyData.success) {
              alert('✅ Mock Payment Successful!\n\nInvoice has been marked as paid.\nIn production, this would be a real payment transaction.')
              await fetchBillingData()
            } else {
              throw new Error(verifyData.error || 'Payment verification failed')
            }
          } else {
            throw new Error('Payment verification failed')
          }
        } catch (verifyError) {
          console.error('Mock payment verification error:', verifyError)
          alert('❌ Mock payment simulation failed.\n\nThis is a test environment issue.\nIn production with real Razorpay, this would work correctly.')
        }

        setPaymentLoading(null)
        return
      }

      // Initialize Razorpay payment with enhanced configuration (real payment mode)
      const options = {
        key: razorpayConfig.keyId,
        amount: order.amount,
        currency: order.currency,
        name: razorpayConfig.name,
        description: razorpayConfig.description,
        image: razorpayConfig.image,
        order_id: order.id,
        handler: async (response: any) => {
          try {
            // Verify payment using new client payment service
            const verifyResponse = await fetch('/api/client-payments/verify', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                razorpayOrderId: response.razorpay_order_id,
                razorpayPaymentId: response.razorpay_payment_id,
                razorpaySignature: response.razorpay_signature,
                invoiceId: invoice.id
              })
            })

            if (verifyResponse.ok) {
              const verifyData = await verifyResponse.json()
              if (verifyData.success) {
                alert('Payment successful! Your invoice has been paid.')
                await fetchBillingData()
              } else {
                throw new Error(verifyData.error || 'Payment verification failed')
              }
            } else {
              throw new Error('Payment verification failed')
            }
          } catch (verifyError) {
            console.error('Payment verification error:', verifyError)
            alert('Payment completed but verification failed. Please contact support.')
          }
        },
        prefill: razorpayConfig.prefill,
        theme: razorpayConfig.theme,
        modal: {
          ondismiss: () => {
            console.log('Payment modal dismissed')
            setPaymentLoading(null)
          }
        }
      }

      const razorpay = new (window as any).Razorpay(options)
      razorpay.open()

    } catch (error) {
      console.error('Payment error:', error)
      alert(error instanceof Error ? error.message : 'Payment failed. Please try again.')
      setPaymentLoading(null)
    }
  }

  const handleManualPayment = async () => {
    if (!subscription || !billingSummary || paymentLoading) return

    setPaymentLoading('subscription')

    try {
      const token = localStorage.getItem('schoolToken')
      if (!token) {
        throw new Error('No authentication token found')
      }

      // Create manual payment order
      const orderResponse = await fetch('/api/subscriptions/create-manual-payment-order', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscriptionId: subscription.id,
          amount: Number(billingSummary.outstandingAmount) || 0
        })
      })

      if (!orderResponse.ok) {
        let errorMessage = 'Failed to create payment order'
        try {
          const errorData = await orderResponse.json()
          errorMessage = errorData.error || errorData.message || errorMessage
        } catch (parseError) {
          console.error('Error parsing error response:', parseError)
          errorMessage = `HTTP ${orderResponse.status}: ${orderResponse.statusText}`
        }
        throw new Error(errorMessage)
      }

      const orderData = await orderResponse.json()

      // Validate order data
      if (!orderData.success || !orderData.data.orderId) {
        throw new Error('Invalid payment order response. Please contact support.')
      }

      // Load Razorpay script if not already loaded
      const loadRazorpayScript = () => {
        return new Promise((resolve, reject) => {
          if ((window as any).Razorpay) {
            resolve(true)
            return
          }

          const script = document.createElement('script')
          script.src = 'https://checkout.razorpay.com/v1/checkout.js'
          script.onload = () => resolve(true)
          script.onerror = () => reject(new Error('Failed to load Razorpay script'))
          document.body.appendChild(script)
        })
      }

      // Initialize Razorpay manual payment
      await loadRazorpayScript()

      const options = {
        key: orderData.data.keyId,
        amount: orderData.data.amount * 100, // Convert to paise
        currency: orderData.data.currency,
        name: 'Schopio',
        description: orderData.data.description,
        order_id: orderData.data.orderId,
        image: '/logo.png',
        handler: async (response: any) => {
          try {
            console.log('🔄 Processing manual payment response:', {
              order_id: response.razorpay_order_id,
              payment_id: response.razorpay_payment_id
            })

            // Verify manual payment
            const verifyResponse = await fetch('/api/subscriptions/verify-manual-payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_signature: response.razorpay_signature,
                subscriptionId: subscription.id
              })
            })

            if (verifyResponse.ok) {
              const verifyData = await verifyResponse.json()
              if (verifyData.success) {
                alert('✅ Payment Successful!\n\nYour subscription payment has been processed.\nYour account is now current.')
                await fetchBillingData()
              } else {
                throw new Error(verifyData.error || 'Payment verification failed')
              }
            } else {
              const errorData = await verifyResponse.json().catch(() => ({}))
              throw new Error(errorData.error || 'Payment verification failed')
            }
          } catch (verifyError) {
            console.error('❌ Payment verification error:', verifyError)
            const errorMessage = verifyError instanceof Error ? verifyError.message :
                                typeof verifyError === 'string' ? verifyError :
                                'Unknown error'
            alert(`❌ Payment verification failed: ${errorMessage}\n\nPlease contact support if this issue persists.`)
          } finally {
            setPaymentLoading(null)
          }
        },
        prefill: {
          name: orderData.data.schoolName || '',
          email: '', // Will be filled from user data
          contact: ''
        },
        theme: {
          color: '#3399cc'
        },
        modal: {
          ondismiss: () => {
            console.log('🚪 Payment modal dismissed by user')
            setPaymentLoading(null)
          }
        }
      }

      console.log('🚀 Opening Razorpay manual payment modal with options:', {
        key: orderData.data.keyId,
        order_id: orderData.data.orderId,
        amount: orderData.data.amount,
        description: orderData.data.description
      })

      const razorpay = new (window as any).Razorpay(options)
      razorpay.open()

    } catch (error) {
      console.error('Manual payment error:', error)
      const errorMessage = error instanceof Error ? error.message :
                          typeof error === 'string' ? error :
                          'Payment failed. Please try again.'
      alert(errorMessage)
      setPaymentLoading(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>
      case 'sent':
        return <Badge className="bg-blue-100 text-blue-800">Sent</Badge>
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatAmount = (amount: string | number) => {
    // Handle different input types safely
    let numericAmount: number

    if (typeof amount === 'number') {
      numericAmount = amount
    } else if (typeof amount === 'string') {
      numericAmount = parseFloat(amount)
    } else {
      console.warn('Invalid amount format:', amount)
      numericAmount = 0
    }

    // Check if the parsed amount is valid
    if (isNaN(numericAmount)) {
      console.warn('Amount is NaN:', amount)
      numericAmount = 0
    }

    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(numericAmount)
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Billing & Payments</h1>
        <Button variant="outline" onClick={downloadStatement}>
          <Download className="h-4 w-4 mr-2" />
          Download Statement
        </Button>
      </div>

      {/* Subscription Information */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Your Subscription
            </CardTitle>
            <CardDescription>
              Manual monthly payment system - Pay in advance to maintain software access
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Essential Subscription Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <p className="text-sm font-medium text-gray-600">Plan</p>
                <p className="text-lg font-semibold">{subscription.planName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Amount</p>
                <p className="text-lg font-semibold text-blue-600">{formatAmount(subscription.monthlyAmount || '0')}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Next Payment Due</p>
                <p className="text-sm font-medium">{formatDate(subscription.nextBillingDate)}</p>
              </div>
            </div>

            {/* Pay-and-Use Model Information */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <CreditCard className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">Manual Monthly Payment System</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Your monthly subscription of <strong>{formatAmount(subscription.monthlyAmount || '0')}</strong> requires manual payment by <strong>{formatDate(subscription.nextBillingDate)}</strong>.
                  </p>
                  <p className="text-xs text-blue-600 mt-2">
                    • Manual payment required • Pay through billing dashboard • Email receipts for all transactions
                  </p>
                </div>
              </div>
            </div>



            {/* Payment Method Information */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center gap-2 mb-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <h4 className="text-lg font-medium text-gray-900">Payment Method</h4>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <CreditCard className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-orange-900">Manual Payment Required</p>
                    <p className="text-sm text-orange-700 mt-1">
                      Monthly charges must be paid manually through your billing dashboard before the due date.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Simplified Billing Information */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IndianRupee className="h-5 w-5" />
              Current Month Billing
            </CardTitle>
            <CardDescription>
              Manual monthly payment system - Pay in advance to maintain software access
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Monthly Amount Display */}
              <div className="text-center p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {formatAmount(subscription.monthlyAmount || '0')}
                </div>
                <p className="text-sm text-blue-700 font-medium">
                  Monthly Subscription Amount
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  {subscription.studentCount} students × {formatAmount(subscription.pricePerStudent || '0')} per student
                </p>
                <div className="mt-3 p-3 bg-white/50 rounded-lg border border-blue-100">
                  <p className="text-xs text-blue-800 font-medium">
                    💡 Advance Payment Model: Pay monthly in advance to maintain software access - No automatic deduction
                  </p>
                </div>
              </div>

              {/* Manual Payment Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <p className="font-medium text-blue-900">Next Payment Due</p>
                  </div>
                  <p className="text-lg font-semibold text-blue-700">
                    {formatDate(subscription.nextBillingDate)}
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Manual payment required
                  </p>
                </div>

                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CreditCard className="h-4 w-4 text-orange-600" />
                    <p className="font-medium text-orange-900">Payment Period</p>
                  </div>
                  <p className="text-sm text-orange-700">
                    {billingSummary?.nextBillingDate ? (() => {
                      const nextDate = new Date(billingSummary.nextBillingDate)
                      const currentDate = new Date(nextDate)
                      currentDate.setMonth(currentDate.getMonth() - 1)
                      return `${currentDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })} - ${nextDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}`
                    })() : 'Not set'}
                  </p>
                  <p className="text-xs text-orange-600 mt-1">
                    Software access period
                  </p>
                </div>
              </div>

              {/* Outstanding Amount (if any) */}
              {billingSummary && billingSummary.outstandingAmount > 0 && (
                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-orange-600" />
                      <p className="font-medium text-orange-900">Outstanding Amount</p>
                    </div>
                    <div className="text-lg font-bold text-orange-700">
                      {formatAmount(billingSummary.outstandingAmount || 0)}
                    </div>
                  </div>
                  <p className="text-sm text-orange-700">
                    {billingSummary.paymentStatus === 'due' && 'Payment due - please ensure sufficient balance'}
                    {billingSummary.paymentStatus === 'grace_period' && `Grace period active (${billingSummary.daysOverdue} days overdue)`}
                    {billingSummary.paymentStatus === 'overdue_with_penalty' && `Overdue with penalty (${billingSummary.daysOverdue} days)`}
                  </p>
                  {billingSummary.penaltyAmount > 0 && (
                    <p className="text-xs text-red-600 mt-1">
                      Penalty: {formatAmount(billingSummary.penaltyAmount || 0)} (2% daily after grace period)
                    </p>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fallback for schools without active subscription */}
      {!subscription && (
        <Card>
          <CardContent className="text-center py-8">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500">No active subscription found</p>
            <p className="text-sm text-gray-400 mt-2">Contact support to set up your subscription</p>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Manual Payment Section */}
      {subscription && billingSummary && (
        <Card className={`border-2 ${
          billingSummary.outstandingAmount > 0
            ? 'border-red-300 bg-red-50'
            : 'border-blue-300 bg-blue-50'
        }`}>
          <CardContent className="p-6">
            <div className="space-y-4">
              {/* Payment Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {billingSummary.outstandingAmount > 0 ? (
                    <AlertCircle className="h-6 w-6 text-red-600" />
                  ) : (
                    <CreditCard className="h-6 w-6 text-blue-600" />
                  )}
                  <div>
                    <p className={`font-semibold text-lg ${
                      billingSummary.outstandingAmount > 0 ? 'text-red-900' : 'text-blue-900'
                    }`}>
                      {billingSummary.outstandingAmount > 0 ? 'Payment Overdue' : 'Make Monthly Payment'}
                    </p>
                    <p className={`text-sm ${
                      billingSummary.outstandingAmount > 0 ? 'text-red-700' : 'text-blue-700'
                    }`}>
                      {billingSummary.outstandingAmount > 0
                        ? 'Immediate payment required to maintain software access'
                        : 'Pay in advance for next month\'s software access'
                      }
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-2xl font-bold ${
                    billingSummary.outstandingAmount > 0 ? 'text-red-700' : 'text-blue-700'
                  }`}>
                    {formatAmount(
                      billingSummary.outstandingAmount > 0
                        ? billingSummary.outstandingAmount
                        : billingSummary.currentMonthAmount || 0
                    )}
                  </p>
                  <p className={`text-xs ${
                    billingSummary.outstandingAmount > 0 ? 'text-red-600' : 'text-blue-600'
                  }`}>
                    {billingSummary.outstandingAmount > 0 ? 'Including penalties' : 'Monthly amount'}
                  </p>
                </div>
              </div>

              {/* Payment Period Info */}
              <div className={`p-3 rounded-lg border ${
                billingSummary.outstandingAmount > 0
                  ? 'bg-red-100 border-red-200'
                  : 'bg-blue-100 border-blue-200'
              }`}>
                <p className={`text-sm font-medium ${
                  billingSummary.outstandingAmount > 0 ? 'text-red-800' : 'text-blue-800'
                }`}>
                  Payment Period: {billingSummary.nextBillingDate ? (() => {
                    const nextDate = new Date(billingSummary.nextBillingDate)
                    const currentDate = new Date(nextDate)
                    currentDate.setMonth(currentDate.getMonth() - 1)
                    return `${currentDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short' })} - ${nextDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' })}`
                  })() : 'Not set'}
                </p>
                <p className={`text-xs mt-1 ${
                  billingSummary.outstandingAmount > 0 ? 'text-red-600' : 'text-blue-600'
                }`}>
                  Your payment covers software access for this entire period
                </p>
              </div>

              {/* Payment Button */}
              <div className="flex justify-center">
                <Button
                  size="lg"
                  className={`px-8 py-3 text-white font-semibold ${
                    billingSummary.outstandingAmount > 0
                      ? 'bg-red-600 hover:bg-red-700'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                  onClick={() => handleManualPayment()}
                  disabled={paymentLoading === 'subscription' || (!billingSummary.canMakePayment && billingSummary.outstandingAmount <= 0)}
                >
                  {paymentLoading === 'subscription' ? (
                    <>
                      <Clock className="h-5 w-5 mr-2 animate-spin" />
                      Processing Payment...
                    </>
                  ) : (
                    <>
                      <CreditCard className="h-5 w-5 mr-2" />
                      {billingSummary.outstandingAmount > 0 ? 'Pay Outstanding Amount' : 'Pay for Next Month'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Simplified Billing Policies */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Policies</CardTitle>
          <CardDescription>
            Important information about your subscription billing
          </CardDescription>
        </CardHeader>
        <CardContent>
          {subscription ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-center">
                <CreditCard className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                <h5 className="font-medium text-blue-900 mb-1">Manual Payment</h5>
                <p className="text-sm text-blue-700">
                  Pay monthly in advance through your billing dashboard
                </p>
              </div>
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
                <Clock className="h-6 w-6 mx-auto mb-2 text-yellow-600" />
                <h5 className="font-medium text-yellow-800 mb-1">Grace Period</h5>
                <p className="text-sm text-yellow-700">
                  {subscription.gracePeriodDays} days for insufficient balance
                </p>
              </div>
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
                <CheckCircle className="h-6 w-6 mx-auto mb-2 text-green-600" />
                <h5 className="font-medium text-green-800 mb-1">Email Notifications</h5>
                <p className="text-sm text-green-700">
                  Alerts for all billing activities
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Receipt className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No active subscription found</p>
              <p className="text-sm text-gray-400 mt-2">Contact support to activate your subscription</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Payment History Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Payment History & Invoices
              </CardTitle>
              <CardDescription>
                Complete record of all subscription payments, discounts applied, and advance payments
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {invoices && invoices.length > 0 ? (
            <div className="space-y-4">
              {invoices.map((invoice) => (
                <div key={invoice.id} className="border rounded-lg hover:bg-gray-50 transition-colors">
                  {/* Invoice Header */}
                  <div className="p-4 border-b">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          invoice.status === 'paid' ? 'bg-green-500' :
                        invoice.status === 'sent' ? 'bg-blue-500' :
                        invoice.status === 'overdue' ? 'bg-red-500' :
                        'bg-gray-400'
                      }`} />
                      <div>
                        <p className="font-medium">
                          Invoice #{invoice.invoiceNumber}
                        </p>
                        <p className="text-sm text-gray-600">
                          {invoice.issuedDate ? new Date(invoice.issuedDate).toLocaleDateString('en-IN', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Date not available'}
                        </p>
                        {invoice.dueDate && (
                          <p className="text-xs text-gray-500">
                            Due: {new Date(invoice.dueDate).toLocaleDateString('en-IN', {
                              day: 'numeric',
                              month: 'short',
                              year: 'numeric'
                            })}
                          </p>
                        )}
                      </div>
                    </div>
                      <div className="text-right">
                        <p className="font-semibold text-lg">
                          {formatAmount(invoice.totalAmount)}
                        </p>
                        <Badge
                          variant={
                            invoice.status === 'paid' ? 'default' :
                            invoice.status === 'sent' ? 'secondary' :
                            invoice.status === 'overdue' ? 'destructive' :
                            'outline'
                          }
                        >
                          {invoice.status === 'paid' ? 'Paid' :
                           invoice.status === 'sent' ? 'Pending' :
                           invoice.status === 'overdue' ? 'Overdue' :
                           invoice.status}
                        </Badge>
                        {invoice.status === 'paid' && invoice.paidDate && (
                          <p className="text-xs text-green-600 mt-1">
                            Paid on {new Date(invoice.paidDate).toLocaleDateString('en-IN')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>


                  {/* Invoice Details */}
                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Base Amount:</span>
                        <span className="ml-2 font-medium">{formatAmount(invoice.amount)}</span>
                      </div>
                      {parseFloat(invoice.amount) !== parseFloat(invoice.totalAmount) && (
                        <div>
                          <span className="text-gray-500">Discount Applied:</span>
                          <span className="ml-2 font-medium text-green-600">
                            -₹{(parseFloat(invoice.amount) - parseFloat(invoice.totalAmount)).toLocaleString()}
                          </span>
                        </div>
                      )}
                      <div>
                        <span className="text-gray-500">Final Amount:</span>
                        <span className="ml-2 font-medium">{formatAmount(invoice.totalAmount)}</span>
                      </div>
                    </div>

                    {/* Payment Actions */}
                    <div className="mt-4 flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadInvoice(invoice.id)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                      {invoice.status !== 'paid' && (
                        <Button
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700"
                          onClick={() => handlePayment(invoice)}
                          disabled={paymentLoading === invoice.id}
                        >
                          {paymentLoading === invoice.id ? (
                            <>
                              <Clock className="h-4 w-4 mr-1 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <CreditCard className="h-4 w-4 mr-1" />
                              Pay Now
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-gray-500">No payment history available</p>
              <p className="text-sm text-gray-400 mt-2">
                Payment records will appear here after your first transaction
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
