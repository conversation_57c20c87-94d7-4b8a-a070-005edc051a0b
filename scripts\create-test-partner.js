/**
 * Create Test Partner Account
 * This script creates a test partner account for debugging login issues
 */

const bcrypt = require('bcryptjs')
const { drizzle } = require('drizzle-orm/neon-http')
const { neon } = require('@neondatabase/serverless')
const { eq } = require('drizzle-orm')
const { partners } = require('../src/db/schema')

// Database connection
const sql = neon(process.env.DATABASE_URL)
const db = drizzle(sql)

async function createTestPartner() {
  console.log('👤 CREATING TEST PARTNER ACCOUNT')
  console.log('=' .repeat(50))

  try {
    // Test partner data
    const testPartner = {
      name: 'Test Partner',
      email: '<EMAIL>',
      password: 'password123',
      phone: '+91 **********',
      partnerCode: 'TEST001',
      profitSharePercentage: '30.00',
      isActive: true
    }

    console.log('📝 Partner details:')
    console.log('- Name:', testPartner.name)
    console.log('- Email:', testPartner.email)
    console.log('- Password:', testPartner.password)
    console.log('- Partner Code:', testPartner.partnerCode)

    // Hash password
    console.log('\n🔐 Hashing password...')
    const passwordHash = await bcrypt.hash(testPartner.password, 12)
    console.log('✅ Password hashed successfully')

    // Check if partner already exists
    console.log('\n🔍 Checking if partner already exists...')
    const existingPartner = await db
      .select()
      .from(partners)
      .where(eq(partners.email, testPartner.email))
      .limit(1)

    if (existingPartner.length > 0) {
      console.log('⚠️ Partner already exists with this email')
      console.log('Existing partner ID:', existingPartner[0].id)
      console.log('Existing partner name:', existingPartner[0].name)
      console.log('Is active:', existingPartner[0].isActive)
      
      // Update password for testing
      console.log('\n🔄 Updating password for existing partner...')
      await db
        .update(partners)
        .set({ 
          passwordHash,
          isActive: true 
        })
        .where(eq(partners.email, testPartner.email))
      
      console.log('✅ Password updated successfully')
      return existingPartner[0]
    }

    // Create new partner
    console.log('\n➕ Creating new partner...')
    const [newPartner] = await db
      .insert(partners)
      .values({
        name: testPartner.name,
        email: testPartner.email,
        passwordHash,
        phone: testPartner.phone,
        partnerCode: testPartner.partnerCode,
        profitSharePercentage: testPartner.profitSharePercentage,
        isActive: testPartner.isActive
      })
      .returning()

    console.log('✅ Partner created successfully!')
    console.log('Partner ID:', newPartner.id)
    
    return newPartner

  } catch (error) {
    console.error('❌ Error creating test partner:', error)
    throw error
  }
}

async function testPartnerLogin(partner) {
  console.log('\n🧪 TESTING PARTNER LOGIN')
  console.log('=' .repeat(50))

  try {
    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    }

    console.log('📝 Attempting login with:')
    console.log('- Email:', loginData.email)
    console.log('- Password:', loginData.password)

    const response = await fetch('http://localhost:3000/api/partner/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(loginData)
    })

    console.log('\n📊 Login response:')
    console.log('- Status:', response.status)
    console.log('- Status Text:', response.statusText)

    const responseData = await response.json()
    console.log('- Response Data:', JSON.stringify(responseData, null, 2))

    if (response.ok && responseData.success) {
      console.log('\n✅ LOGIN SUCCESSFUL!')
      console.log('- Token received:', responseData.token ? 'Yes' : 'No')
      console.log('- Partner data received:', responseData.partner ? 'Yes' : 'No')
      
      if (responseData.token) {
        console.log('- Token length:', responseData.token.length)
        console.log('- Token starts with:', responseData.token.substring(0, 20) + '...')
      }
      
      return responseData
    } else {
      console.log('\n❌ LOGIN FAILED!')
      console.log('Error:', responseData.error || 'Unknown error')
      return null
    }

  } catch (error) {
    console.error('❌ Login test failed:', error.message)
    return null
  }
}

async function testDashboardAccess(loginData) {
  if (!loginData || !loginData.token) {
    console.log('\n⏭️ Skipping dashboard test (no token)')
    return
  }

  console.log('\n🏠 TESTING DASHBOARD ACCESS')
  console.log('=' .repeat(50))

  try {
    const response = await fetch('http://localhost:3000/api/partner/dashboard', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    })

    console.log('📊 Dashboard response:')
    console.log('- Status:', response.status)
    console.log('- Status Text:', response.statusText)

    if (response.ok) {
      const dashboardData = await response.json()
      console.log('\n✅ DASHBOARD ACCESS SUCCESSFUL!')
      console.log('- Success:', dashboardData.success)
      console.log('- Has partner data:', !!dashboardData.data?.partner)
      console.log('- Has performance metrics:', !!dashboardData.data?.performanceMetrics)
      console.log('- Has earnings:', !!dashboardData.data?.earnings)
    } else {
      const errorData = await response.text()
      console.log('\n❌ DASHBOARD ACCESS FAILED!')
      console.log('Error:', errorData)
    }

  } catch (error) {
    console.error('❌ Dashboard test failed:', error.message)
  }
}

// Main execution
async function main() {
  console.log('🚀 PARTNER ACCOUNT SETUP & LOGIN TEST')
  console.log('=' .repeat(60))

  try {
    // Check database connection
    console.log('🔌 Checking database connection...')
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable not set')
    }
    console.log('✅ Database URL configured')

    // Create/update test partner
    const partner = await createTestPartner()
    
    // Test login
    const loginData = await testPartnerLogin(partner)
    
    // Test dashboard access
    await testDashboardAccess(loginData)

    console.log('\n🎉 SETUP COMPLETE!')
    console.log('=' .repeat(60))
    console.log('You can now test partner login with:')
    console.log('- Email: <EMAIL>')
    console.log('- Password: password123')
    console.log('')
    console.log('Go to: http://localhost:3000/partner/login')

  } catch (error) {
    console.error('❌ Setup failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { createTestPartner, testPartnerLogin }
