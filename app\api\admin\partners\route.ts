import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/src/db'
import {
  partners,
  schoolReferrals,
  partnerBilling
} from '@/src/db/schema'
import { eq, and, desc, count, isNotNull } from 'drizzle-orm'

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { success: false, error: 'Authorization token required' }
  }

  const token = authHeader.substring(7)

  try {
    // Here you would verify the JWT token
    // For now, we'll assume the token is valid
    return { success: true, adminId: 'admin-123' }
  } catch (error) {
    return { success: false, error: 'Invalid token' }
  }
}

// GET /api/admin/partners - Get all partners
export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    console.log('📊 [Admin] Fetching partners list')

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'

    // Calculate pagination
    const offset = (page - 1) * limit

    // Build query conditions
    let whereConditions = []

    if (status !== 'all') {
      whereConditions.push(eq(partners.isActive, status === 'active'))
    }

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(partners)
      .where(and(...whereConditions))

    // Get partners with pagination
    const partnersList = await db
      .select({
        id: partners.id,
        name: partners.name,
        email: partners.email,
        phone: partners.phone,
        partnerCode: partners.partnerCode,
        profitSharePercentage: partners.profitSharePercentage,
        isActive: partners.isActive,
        createdAt: partners.createdAt
      })
      .from(partners)
      .where(and(...whereConditions))
      .orderBy(desc(partners.createdAt))
      .limit(limit)
      .offset(offset)

    // Get school counts for each partner
    const schoolCounts = await db
      .select({
        partnerId: schoolReferrals.partnerId,
        count: count()
      })
      .from(schoolReferrals)
      .groupBy(schoolReferrals.partnerId)

    // Get billing information for each partner
    const billingInfo = await db
      .select({
        partnerId: partnerBilling.partnerId,
        totalEarned: partnerBilling.totalEarned,
        paidAmount: partnerBilling.paidAmount,
        pendingAmount: partnerBilling.pendingAmount
      })
      .from(partnerBilling)

    // Combine data
    const partnersWithDetails = partnersList.map(partner => {
      const schoolCount = schoolCounts.find(sc => sc.partnerId === partner.id)?.count || 0
      const billing = billingInfo.find(bi => bi.partnerId === partner.id) || {
        totalEarned: '0',
        paidAmount: '0',
        pendingAmount: '0'
      }

      return {
        ...partner,
        schoolCount,
        profitSharePercentage: parseFloat(partner.profitSharePercentage || '0'),
        totalEarned: parseFloat(billing.totalEarned || '0'),
        paidAmount: parseFloat(billing.paidAmount || '0'),
        pendingAmount: parseFloat(billing.pendingAmount || '0')
      }
    })

    console.log(`✅ [Admin] Partners list fetched: ${partnersWithDetails.length} partners`)

    return NextResponse.json({
      partners: partnersWithDetails,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil((totalCount?.count || 0) / limit),
        totalRecords: totalCount?.count || 0,
        recordsPerPage: limit
      }
    })

  } catch (error) {
    console.error('❌ [Admin] Error fetching partners list:', error)
    return NextResponse.json(
      { error: 'Failed to fetch partners list' },
      { status: 500 }
    )
  }
}

// POST /api/admin/partners - Create new partner
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request)
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 })
    }

    console.log('🆕 [Admin] Creating new partner')

    const data = await request.json()

    // Validate required fields
    const requiredFields = ['name', 'email', 'phone', 'profitSharePercentage']
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json({ error: `Missing required field: ${field}` }, { status: 400 })
      }
    }

    // Check if email already exists
    const [existingPartner] = await db
      .select({ id: partners.id })
      .from(partners)
      .where(eq(partners.email, data.email))
      .limit(1)

    if (existingPartner) {
      return NextResponse.json({ error: 'Partner with this email already exists' }, { status: 400 })
    }

    // Generate unique partner code
    const partnerCode = generatePartnerCode()

    // Create partner
    const [newPartner] = await db
      .insert(partners)
      .values({
        name: data.name,
        email: data.email,
        phone: data.phone,
        partnerCode,
        passwordHash: 'temp_password_hash', // This should be properly hashed
        address: data.address || 'Address not provided',
        profitSharePercentage: data.profitSharePercentage.toString(),
        isActive: true,
        createdBy: authResult.adminId || 'admin-default'
      })
      .returning()

    console.log(`✅ [Admin] New partner created: ${newPartner.name}`)

    return NextResponse.json({
      success: true,
      partner: newPartner
    })

  } catch (error) {
    console.error('❌ [Admin] Error creating partner:', error)
    return NextResponse.json(
      { error: 'Failed to create partner' },
      { status: 500 }
    )
  }
}

// Helper function to generate unique partner code
function generatePartnerCode(): string {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
  let code = ''
  for (let i = 0; i < 6; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return code
}
