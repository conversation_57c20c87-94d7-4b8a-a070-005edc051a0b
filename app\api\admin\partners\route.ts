import { Hono } from 'hono'
import { db } from '@/src/db'
import { 
  partners, 
  partnerBilling, 
  partnerTdsRecords,
  partnerHoldPeriods,
  schoolReferrals,
  clients,
  billingSubscriptions,
  partnerCommissions
} from '@/src/db/schema'
import { eq, and, desc, count, sum } from 'drizzle-orm'
import { 
  adminAuthMiddleware, 
  requirePermission 
} from '@/src/middleware/authMiddleware'

const app = new Hono()

// ===== PARTNER DASHBOARD API =====

// Get comprehensive partner dashboard data
app.get('/:partnerId/dashboard', adminAuthMiddleware, requirePermission('partners:read'), async (c) => {
  try {
    const partnerId = c.req.param('partnerId')

    console.log(`📊 [Admin] Fetching partner dashboard for: ${partnerId}`)

    // Get partner basic information
    const [partner] = await db
      .select({
        id: partners.id,
        name: partners.name,
        email: partners.email,
        phone: partners.phone,
        partnerCode: partners.partnerCode,
        profitSharePercentage: partners.profitSharePercentage,
        panCard: partners.panCard,
        city: partners.city,
        age: partners.age,
        bankAccountHolderName: partners.bankAccountHolderName,
        status: partners.isActive,
        createdAt: partners.createdAt
      })
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!partner) {
      return c.json({ error: 'Partner not found' }, 404)
    }

    // Get partner billing information
    let [billing] = await db
      .select()
      .from(partnerBilling)
      .where(eq(partnerBilling.partnerId, partnerId))
      .limit(1)

    // If no billing record exists, create one with default values
    if (!billing) {
      [billing] = await db
        .insert(partnerBilling)
        .values({
          partnerId: partnerId,
          pendingAmount: 0,
          holdAmount: 0,
          paidAmount: 0,
          totalEarned: 0,
          totalTdsDeducted: 0,
          netPayoutAmount: 0,
          totalTransactions: 0,
          successfulPayouts: 0,
          failedPayouts: 0
        })
        .returning()
    }

    // Get total schools referred by this partner
    const [schoolCount] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(eq(schoolReferrals.partnerId, partnerId))

    // Get recent commission activity
    const recentCommissions = await db
      .select({
        id: partnerCommissions.id,
        schoolName: partnerCommissions.schoolName,
        commissionAmount: partnerCommissions.commissionAmount,
        paymentDate: partnerCommissions.paymentDate,
        holdStatus: partnerCommissions.holdStatus,
        payoutStatus: partnerCommissions.payoutStatus
      })
      .from(partnerCommissions)
      .where(eq(partnerCommissions.partnerId, partnerId))
      .orderBy(desc(partnerCommissions.createdAt))
      .limit(10)

    // Get monthly earnings for the last 6 months
    const monthlyEarnings = await db
      .select({
        month: partnerCommissions.paymentMonth,
        totalAmount: sum(partnerCommissions.commissionAmount),
        transactionCount: count()
      })
      .from(partnerCommissions)
      .where(eq(partnerCommissions.partnerId, partnerId))
      .groupBy(partnerCommissions.paymentMonth)
      .orderBy(desc(partnerCommissions.paymentMonth))
      .limit(6)

    // Prepare response data
    const partnerData = {
      ...partner,
      totalSchools: schoolCount?.count || 0,
      profitSharePercentage: parseFloat(partner.profitSharePercentage || '0')
    }

    const billingData = {
      pendingAmount: parseFloat(billing.pendingAmount || '0'),
      holdAmount: parseFloat(billing.holdAmount || '0'),
      paidAmount: parseFloat(billing.paidAmount || '0'),
      totalEarned: parseFloat(billing.totalEarned || '0'),
      totalTdsDeducted: parseFloat(billing.totalTdsDeducted || '0'),
      netPayoutAmount: parseFloat(billing.netPayoutAmount || '0'),
      totalTransactions: billing.totalTransactions || 0,
      successfulPayouts: billing.successfulPayouts || 0,
      failedPayouts: billing.failedPayouts || 0,
      lastPayoutDate: billing.lastPayoutDate
    }

    console.log(`✅ [Admin] Partner dashboard data fetched for: ${partner.name}`)

    return c.json({
      partner: partnerData,
      billing: billingData,
      recentActivity: recentCommissions.map(commission => ({
        ...commission,
        commissionAmount: parseFloat(commission.commissionAmount || '0')
      })),
      monthlyEarnings: monthlyEarnings.map(earning => ({
        month: earning.month,
        totalAmount: parseFloat(earning.totalAmount || '0'),
        transactionCount: earning.transactionCount
      }))
    })

  } catch (error) {
    console.error('❌ [Admin] Error fetching partner dashboard:', error)
    return c.json({ error: 'Failed to fetch partner dashboard data' }, 500)
  }
})

// Get partner billing details
app.get('/:partnerId/billing', adminAuthMiddleware, requirePermission('partners:read'), async (c) => {
  try {
    const partnerId = c.req.param('partnerId')

    console.log(`💰 [Admin] Fetching partner billing details for: ${partnerId}`)

    // Get detailed billing information
    const [billing] = await db
      .select()
      .from(partnerBilling)
      .where(eq(partnerBilling.partnerId, partnerId))
      .limit(1)

    if (!billing) {
      return c.json({ error: 'Partner billing information not found' }, 404)
    }

    // Get TDS records
    const tdsRecords = await db
      .select()
      .from(partnerTdsRecords)
      .where(eq(partnerTdsRecords.partnerId, partnerId))
      .orderBy(desc(partnerTdsRecords.createdAt))
      .limit(20)

    // Get hold periods
    const holdPeriods = await db
      .select()
      .from(partnerHoldPeriods)
      .where(eq(partnerHoldPeriods.partnerId, partnerId))
      .orderBy(desc(partnerHoldPeriods.createdAt))
      .limit(20)

    console.log(`✅ [Admin] Partner billing details fetched`)

    return c.json({
      billing: {
        ...billing,
        pendingAmount: parseFloat(billing.pendingAmount || '0'),
        holdAmount: parseFloat(billing.holdAmount || '0'),
        paidAmount: parseFloat(billing.paidAmount || '0'),
        totalEarned: parseFloat(billing.totalEarned || '0'),
        totalTdsDeducted: parseFloat(billing.totalTdsDeducted || '0'),
        netPayoutAmount: parseFloat(billing.netPayoutAmount || '0')
      },
      tdsRecords: tdsRecords.map(record => ({
        ...record,
        grossAmount: parseFloat(record.grossAmount || '0'),
        tdsAmount: parseFloat(record.tdsAmount || '0'),
        netAmount: parseFloat(record.netAmount || '0'),
        tdsPercentage: parseFloat(record.tdsPercentage || '0')
      })),
      holdPeriods: holdPeriods.map(period => ({
        ...period,
        holdAmount: parseFloat(period.holdAmount || '0')
      }))
    })

  } catch (error) {
    console.error('❌ [Admin] Error fetching partner billing details:', error)
    return c.json({ error: 'Failed to fetch partner billing details' }, 500)
  }
})

// Update partner billing status (for admin actions)
app.patch('/:partnerId/billing', adminAuthMiddleware, requirePermission('partners:write'), async (c) => {
  try {
    const partnerId = c.req.param('partnerId')
    const { action, amount, reason } = await c.req.json()

    console.log(`🔄 [Admin] Updating partner billing: ${action} for partner: ${partnerId}`)

    // Validate action
    if (!['release_hold', 'manual_payout', 'adjust_amount'].includes(action)) {
      return c.json({ error: 'Invalid action' }, 400)
    }

    // Get current billing state
    const [currentBilling] = await db
      .select()
      .from(partnerBilling)
      .where(eq(partnerBilling.partnerId, partnerId))
      .limit(1)

    if (!currentBilling) {
      return c.json({ error: 'Partner billing record not found' }, 404)
    }

    let updateData: any = {
      updatedAt: new Date()
    }

    switch (action) {
      case 'release_hold':
        // Move hold amount to pending
        updateData.holdAmount = 0
        updateData.pendingAmount = parseFloat(currentBilling.pendingAmount || '0') + parseFloat(currentBilling.holdAmount || '0')
        break

      case 'manual_payout':
        // Process manual payout
        if (!amount || amount <= 0) {
          return c.json({ error: 'Valid amount required for manual payout' }, 400)
        }
        updateData.pendingAmount = Math.max(0, parseFloat(currentBilling.pendingAmount || '0') - amount)
        updateData.paidAmount = parseFloat(currentBilling.paidAmount || '0') + amount
        updateData.successfulPayouts = (currentBilling.successfulPayouts || 0) + 1
        updateData.lastPayoutDate = new Date()
        break

      case 'adjust_amount':
        // Manual amount adjustment
        if (!amount) {
          return c.json({ error: 'Amount required for adjustment' }, 400)
        }
        updateData.totalEarned = Math.max(0, parseFloat(currentBilling.totalEarned || '0') + amount)
        if (amount > 0) {
          updateData.pendingAmount = parseFloat(currentBilling.pendingAmount || '0') + amount
        }
        break
    }

    // Update billing record
    await db
      .update(partnerBilling)
      .set(updateData)
      .where(eq(partnerBilling.partnerId, partnerId))

    console.log(`✅ [Admin] Partner billing updated: ${action}`)

    return c.json({ 
      success: true, 
      message: `Partner billing ${action} completed successfully`,
      action,
      amount
    })

  } catch (error) {
    console.error('❌ [Admin] Error updating partner billing:', error)
    return c.json({ error: 'Failed to update partner billing' }, 500)
  }
})

export default app
