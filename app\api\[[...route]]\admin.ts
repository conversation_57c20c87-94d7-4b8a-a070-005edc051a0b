import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import crypto from 'crypto'
import { db } from '@/src/db'
import {
  adminUsers,
  clients,
  clientUsers,
  leads,
  softwareRequests,
  partners,
  referralCodes,
  schoolReferrals,
  operationalExpenses,
  partnerEarnings,
  withdrawalRequests,
  partnerTransactions,
  subscriptions,
  subscriptionPlans,
  billingSubscriptions,
  billingInvoices,
  billingPayments,
  billingPaymentReminders,
  supportTickets,
  ticketMessages,
  auditLogs,
  rateLimits,
  securityEvents,
  partnerCommissionEscrow,
  partnerCommissionTransactions,
  commissionReleaseAudit,
  subscriptionDiscounts,
  subscriptionExpenses
} from '@/src/db/schema'
import { eq, and, or, desc, asc, count, sql, inArray, lte, gte, isNotNull, isNull, ilike } from 'drizzle-orm'
import {
  adminAuthMiddleware,
  requireAdminRole,
  requirePermission,
  adminSecurityHeaders,
  adminRateLimit,
  adminLoginRateLimit,
  generateAdminToken,
  getCurrentAdmin,
  ADMIN_ROLES
} from '@/src/middleware/admin-auth'
import { supportNotificationService } from '@/src/services/supportNotificationService'
import { auditLogger } from '@/src/services/auditLogger'
import { advancedSubscriptionManager } from '@/src/services/advancedSubscriptionManager'
import { securityMonitor } from '@/src/services/securityMonitor'
import { calculateDueDate, calculateNextBillingDate, calculatePeriodEndDate, formatDateForDB } from '@/src/utils/dateCalculations'
import { inputValidator } from '@/src/services/inputValidator'
import { billingMonitor } from '@/src/services/billingMonitor'
import { dunningManager } from '@/src/services/dunningManager'
import { paymentFailureHandler } from '@/src/services/paymentFailureHandler'
import { paymentMonitoringService } from '@/src/services/paymentMonitoringService'
import { advancedFinancialAnalytics } from '@/src/services/advancedFinancialAnalytics'
import { emailService } from '@/src/services/emailService'
import { razorpayService } from '@/src/services/razorpayService'
import { commissionProcessor } from '@/src/services/commissionProcessor'
import { escrowReleaseJob } from '@/src/services/escrowReleaseJob'
import { escrowScheduler } from '@/src/services/escrowScheduler'
import { commissionCalculationEngine } from '@/src/services/commissionCalculationEngine'
import { DueDateManager } from '@/src/services/dueDateManager'
import { billingScheduler } from '@/src/services/billingScheduler'
import { SubscriptionStatusManager } from '@/src/services/subscriptionStatusManager'
import { partnerCommissionMicroservice } from '@/src/services/partnerCommissionMicroservice'

const app = new Hono()

// Apply security headers and rate limiting to all admin routes
app.use('*', adminSecurityHeaders)
app.use('*', adminRateLimit)

// ===== ADMIN AUTHENTICATION =====

// Validation schemas
const adminLoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required')
})

// ===== AUTHENTICATION ENDPOINTS =====

// Admin login endpoint with enhanced security
app.post('/login', adminLoginRateLimit, zValidator('json', adminLoginSchema), async (c) => {
  const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
  const userAgent = c.req.header('user-agent') || 'unknown'
  const requestId = crypto.randomUUID()

  try {
    const { email, password } = c.req.valid('json')

    // Enhanced input validation
    const emailValidation = inputValidator.validateEmail(email)
    if (!emailValidation.isValid) {
      await auditLogger.logAuth('failed_login', {
        email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid email format'
      })

      return c.json({
        error: 'Invalid email format',
        requestId
      }, 400)
    }

    const sanitizedEmail = emailValidation.sanitizedData!

    // Find admin user
    const [admin] = await db.select().from(adminUsers).where(eq(adminUsers.email, sanitizedEmail)).limit(1)

    if (!admin) {
      await auditLogger.logAuth('failed_login', {
        email: sanitizedEmail,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Admin not found'
      })

      await securityMonitor.logSecurityEvent({
        type: 'failed_login',
        ipAddress,
        userAgent,
        details: {
          email: sanitizedEmail,
          reason: 'Admin not found',
          requestId
        },
        severity: 'medium'
      })

      return c.json({
        error: 'Invalid email or password',
        requestId
      }, 401)
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, admin.passwordHash)
    if (!isValidPassword) {
      await auditLogger.logAuth('failed_login', {
        adminId: admin.id,
        email: admin.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid password'
      })

      await securityMonitor.logSecurityEvent({
        type: 'failed_login',
        adminId: admin.id,
        ipAddress,
        userAgent,
        details: {
          email: admin.email,
          reason: 'Invalid password',
          requestId
        },
        severity: 'medium'
      })

      return c.json({
        error: 'Invalid email or password',
        requestId
      }, 401)
    }

    // Check if account is active
    if (!admin.isActive) {
      await auditLogger.logAuth('failed_login', {
        adminId: admin.id,
        email: admin.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Account deactivated'
      })

      await securityMonitor.logSecurityEvent({
        type: 'unauthorized_access',
        adminId: admin.id,
        ipAddress,
        userAgent,
        details: {
          email: admin.email,
          reason: 'Deactivated account login attempt',
          requestId
        },
        severity: 'high'
      })

      return c.json({
        error: 'Admin account is deactivated. Please contact system administrator.',
        requestId
      }, 403)
    }

    // Update last login
    await db.update(adminUsers)
      .set({ lastLogin: new Date() })
      .where(eq(adminUsers.id, admin.id))

    // Generate token
    const permissions = Array.isArray(admin.permissions) ? admin.permissions : []
    const token = generateAdminToken(admin.id, admin.email, admin.role, permissions)

    // Log successful login
    await auditLogger.logAuth('login', {
      adminId: admin.id,
      email: admin.email,
      ipAddress,
      userAgent,
      success: true
    })

    await auditLogger.logAdmin('admin_login', {
      adminId: admin.id,
      resource: 'authentication',
      details: {
        loginTime: new Date().toISOString(),
        ipAddress,
        userAgent,
        requestId
      },
      ipAddress,
      userAgent,
      success: true
    })

    return c.json({
      message: 'Admin login successful',
      token,
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        permissions: permissions
      },
      requestId
    })

  } catch (error) {
    console.error('Admin login error:', error)

    await auditLogger.logAuth('failed_login', {
      email: 'unknown',
      ipAddress,
      userAgent,
      success: false,
      errorMessage: error instanceof Error ? error.message : 'Unknown error'
    })

    return c.json({
      error: 'Login failed. Please try again.',
      requestId
    }, 500)
  }
})

// Admin profile endpoint
app.get('/profile', adminAuthMiddleware, async (c) => {
  try {
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    return c.json({
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        permissions: admin.permissions
      }
    })
  } catch (error) {
    console.error('Admin profile error:', error)
    return c.json({ error: 'Failed to fetch profile' }, 500)
  }
})

// ===== DASHBOARD ENDPOINTS =====

// Admin dashboard with analytics
app.get('/dashboard', adminAuthMiddleware, async (c) => {
  try {
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get basic statistics
    const [totalLeads] = await db.select({ count: count() }).from(leads)
    const [totalClients] = await db.select({ count: count() }).from(clients)
    const [totalClientUsers] = await db.select({ count: count() }).from(clientUsers)
    const [pendingRequests] = await db.select({ count: count() }).from(softwareRequests).where(eq(softwareRequests.status, 'pending'))

    // Partner system statistics
    const [totalPartners] = await db.select({ count: count() }).from(partners)
    const [activePartners] = await db.select({ count: count() }).from(partners).where(eq(partners.isActive, true))
    const [totalReferrals] = await db.select({ count: count() }).from(schoolReferrals)
    const [pendingWithdrawals] = await db.select({ count: count() }).from(withdrawalRequests).where(eq(withdrawalRequests.status, 'pending'))

    const [partnerEarningsStats] = await db
      .select({
        totalEarnings: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        availableBalance: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)

    // Get recent leads (last 10)
    const recentLeads = await db
      .select({
        id: leads.id,
        email: leads.email,
        schoolName: leads.schoolName,
        contactPerson: leads.contactPerson,
        estimatedStudents: leads.estimatedStudents,
        status: leads.status,
        createdAt: leads.createdAt
      })
      .from(leads)
      .orderBy(desc(leads.createdAt))
      .limit(10)

    // Get recent clients (last 10)
    const recentClients = await db
      .select({
        id: clients.id,
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        email: clients.email,
        actualStudentCount: clients.actualStudentCount,
        onboardingStatus: clients.onboardingStatus,
        createdAt: clients.createdAt
      })
      .from(clients)
      .orderBy(desc(clients.createdAt))
      .limit(10)

    return c.json({
      statistics: {
        totalLeads: totalLeads.count,
        totalClients: totalClients.count,
        totalClientUsers: totalClientUsers.count,
        pendingRequests: pendingRequests.count
      },
      partnerStats: {
        totalPartners: totalPartners.count,
        activePartners: activePartners.count,
        totalReferrals: totalReferrals.count,
        pendingWithdrawals: pendingWithdrawals.count,
        totalEarnings: partnerEarningsStats?.totalEarnings || 0,
        availableBalance: partnerEarningsStats?.availableBalance || 0
      },
      recentLeads,
      recentClients,
      adminInfo: {
        name: admin.name,
        role: admin.role
      }
    })

  } catch (error) {
    console.error('Admin dashboard error:', error)
    return c.json({ error: 'Failed to fetch dashboard data' }, 500)
  }
})

// ===== LEAD MANAGEMENT =====

// Get all leads with pagination and filters
app.get('/leads', adminAuthMiddleware, requirePermission('leads:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    const whereConditions = []

    if (status) {
      whereConditions.push(eq(leads.status, status))
    }

    if (search) {
      whereConditions.push(
        sql`${leads.schoolName} ILIKE ${`%${search}%`} OR ${leads.email} ILIKE ${`%${search}%`} OR ${leads.contactPerson} ILIKE ${`%${search}%`}`
      )
    }

    // Execute query with conditions
    const leadsData = whereConditions.length > 0
      ? await db.select().from(leads).where(and(...whereConditions)).orderBy(desc(leads.createdAt)).limit(limitNum).offset(offset)
      : await db.select().from(leads).orderBy(desc(leads.createdAt)).limit(limitNum).offset(offset)

    // Get total count for pagination
    const [totalCount] = whereConditions.length > 0
      ? await db.select({ count: count() }).from(leads).where(and(...whereConditions))
      : await db.select({ count: count() }).from(leads)

    return c.json({
      leads: leadsData,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin leads error:', error)
    return c.json({ error: 'Failed to fetch leads' }, 500)
  }
})

// Update lead status
app.put('/leads/:id', adminAuthMiddleware, requirePermission('leads:write'), async (c) => {
  try {
    const leadId = c.req.param('id')
    const { status, notes } = await c.req.json()

    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    await db.update(leads)
      .set({
        status,
        notes,
        updatedAt: new Date()
      })
      .where(eq(leads.id, leadId))

    return c.json({
      message: 'Lead updated successfully',
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Admin update lead error:', error)
    return c.json({ error: 'Failed to update lead' }, 500)
  }
})

// Convert lead to client
app.post('/leads/:id/convert', adminAuthMiddleware, requirePermission('leads:write'), async (c) => {
  try {
    const leadId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get lead details
    const [lead] = await db.select().from(leads).where(eq(leads.id, leadId)).limit(1)

    if (!lead) {
      return c.json({ error: 'Lead not found' }, 404)
    }

    if (lead.status === 'converted') {
      return c.json({ error: 'Lead has already been converted' }, 400)
    }

    // Check if client with this email already exists
    const [existingClient] = await db.select().from(clients).where(eq(clients.email, lead.email)).limit(1)

    if (existingClient) {
      return c.json({ error: 'A client with this email already exists' }, 400)
    }

    // Generate unique school code
    let schoolCode = `SCH${Date.now()}`
    let codeExists = true
    while (codeExists) {
      const [existing] = await db.select().from(clients).where(eq(clients.schoolCode, schoolCode)).limit(1)
      if (!existing) {
        codeExists = false
      } else {
        schoolCode = `SCH${Date.now()}_${Math.floor(Math.random() * 1000)}`
      }
    }

    // Create client record (Neon HTTP driver doesn't support transactions)
    const [newClient] = await db.insert(clients).values({
      leadId: lead.id,
      schoolName: lead.schoolName,
      schoolCode: schoolCode,
      email: lead.email,
      phone: lead.phone,
      contactPerson: lead.contactPerson,
      actualStudentCount: lead.estimatedStudents || 1,
      estimatedStudentCount: lead.estimatedStudents,
      onboardingStatus: 'pending',
      status: 'active'
    }).returning()

    // Update lead status to converted
    await db.update(leads)
      .set({
        status: 'converted',
        updatedAt: new Date()
      })
      .where(eq(leads.id, leadId))

    const result = { newClient }

    // Log the conversion for audit trail
    await auditLogger.logAdmin('lead_converted', {
      adminId: admin.id,
      resource: 'lead_conversion',
      resourceId: result.newClient.id,
      details: {
        leadId: lead.id,
        clientId: result.newClient.id,
        schoolName: lead.schoolName,
        email: lead.email,
        schoolCode: schoolCode
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({
      message: 'Lead converted to client successfully',
      client: {
        id: result.newClient.id,
        schoolName: result.newClient.schoolName,
        schoolCode: result.newClient.schoolCode,
        email: result.newClient.email,
        onboardingStatus: result.newClient.onboardingStatus
      },
      convertedBy: admin.name
    })

  } catch (error) {
    console.error('Lead conversion error:', error)
    return c.json({ error: 'Failed to convert lead to client' }, 500)
  }
})

// ===== CLIENT MANAGEMENT =====

// Get all clients with comprehensive data including software requests and revenue metrics
app.get('/clients', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const {
      status,
      page = '1',
      limit = '10',
      search,
      include_subscription = 'true',
      include_requests = 'true',
      request_status, // Filter by software request status
      revenue_min, // Minimum monthly revenue filter
      revenue_max, // Maximum monthly revenue filter
      partner_id, // Filter by partner
      has_production_request, // Filter clients with production requests
      requires_attention, // Filter clients requiring attention
      sort_by = 'created_at', // Sort field
      sort_order = 'desc' // Sort order
    } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    const whereConditions = []

    if (status) {
      whereConditions.push(eq(clients.onboardingStatus, status))
    }

    if (search) {
      whereConditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${clients.email} ILIKE ${`%${search}%`} OR ${clients.schoolCode} ILIKE ${`%${search}%`} OR ${clients.contactPerson} ILIKE ${`%${search}%`}`
      )
    }

    // Add partner filtering
    if (partner_id) {
      whereConditions.push(eq(schoolReferrals.partnerId, partner_id))
    }

    // Enhanced query with comprehensive data
    const query = db
      .select({
        client: clients,
        subscription: billingSubscriptions,
        partner: {
          id: schoolReferrals.partnerId,
          name: partners.name,
          code: partners.partnerCode,
          referralCode: referralCodes.code,
          referredAt: schoolReferrals.referredAt,
          referralSource: schoolReferrals.referralSource
        }
      })
      .from(clients)
      .leftJoin(billingSubscriptions, and(
        eq(billingSubscriptions.clientId, clients.id),
        eq(billingSubscriptions.status, 'active')
      ))
      .leftJoin(schoolReferrals, and(
        eq(schoolReferrals.clientId, clients.id),
        eq(schoolReferrals.isActive, true)
      ))
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))

    // Apply sorting
    let sortColumn
    switch (sort_by) {
      case 'school_name':
        sortColumn = clients.schoolName
        break
      case 'email':
        sortColumn = clients.email
        break
      case 'student_count':
        sortColumn = clients.actualStudentCount
        break
      case 'created_at':
      default:
        sortColumn = clients.createdAt
        break
    }

    const sortedQuery = sort_order === 'asc' ? query.orderBy(asc(sortColumn)) : query.orderBy(desc(sortColumn))

    const rawClientsData = whereConditions.length > 0
      ? await sortedQuery.where(and(...whereConditions)).limit(limitNum).offset(offset)
      : await sortedQuery.limit(limitNum).offset(offset)

    // Get software requests for all clients in this batch
    const clientIds = rawClientsData.map(row => row.client.id)
    let softwareRequestsData: any[] = []

    if (include_requests === 'true' && clientIds.length > 0) {
      let requestConditions = [inArray(softwareRequests.clientId, clientIds)]

      // Apply request status filter if provided
      if (request_status) {
        requestConditions.push(eq(softwareRequests.status, request_status))
      }

      softwareRequestsData = await db
        .select({
          clientId: softwareRequests.clientId,
          id: softwareRequests.id,
          requestType: softwareRequests.requestType,
          status: softwareRequests.status,
          studentCount: softwareRequests.studentCount,
          facultyCount: softwareRequests.facultyCount,
          completeAddress: softwareRequests.completeAddress,
          contactNumber: softwareRequests.contactNumber,
          primaryEmail: softwareRequests.primaryEmail,
          averageMonthlyFee: softwareRequests.averageMonthlyFee,
          termsAccepted: softwareRequests.termsAccepted,
          createdAt: softwareRequests.createdAt,
          approvedAt: softwareRequests.approvedAt
        })
        .from(softwareRequests)
        .where(and(...requestConditions))
        .orderBy(desc(softwareRequests.createdAt))
    }

    // Group software requests by client ID
    const requestsByClient = softwareRequestsData.reduce((acc: Record<string, any[]>, request: any) => {
      if (!acc[request.clientId]) {
        acc[request.clientId] = []
      }
      acc[request.clientId].push(request)
      return acc
    }, {} as Record<string, any[]>)

    // Enhanced client data with comprehensive information
    let enhancedClientsData = rawClientsData.map(row => {
      const client = row.client
      const clientRequests = requestsByClient[client.id] || []
      const latestProductionRequest = clientRequests.find((req: any) => req.requestType === 'production') || clientRequests[0]

      // Calculate revenue metrics
      const feePerStudent = client.averageMonthlyFee
        ? parseFloat(client.averageMonthlyFee)
        : (latestProductionRequest?.averageMonthlyFee ? parseFloat(latestProductionRequest.averageMonthlyFee) : 0)

      const monthlyRevenue = feePerStudent * (client.actualStudentCount || 0)
      const annualRevenue = monthlyRevenue * 12

      // Enhanced administrative summary
      const hasProductionRequest = clientRequests.some((req: any) => req.requestType === 'production')
      const requiresAttention = clientRequests.some((req: any) => req.status === 'pending') || !client.averageMonthlyFee || monthlyRevenue === 0
      const lastActivity = clientRequests[0]?.createdAt || client.createdAt

      return {
        ...client,
        subscription: row.subscription,
        partner: row.partner.id ? row.partner : null,

        // Software request integration
        softwareRequests: clientRequests,
        latestProductionRequest,

        // Enhanced metrics
        revenueMetrics: {
          monthlyRevenue,
          annualRevenue,
          feePerStudent,
          totalRequests: clientRequests.length,
          activeRequests: clientRequests.filter((req: any) => ['pending', 'under_review', 'approved'].includes(req.status)).length,
          averageRequestValue: clientRequests.length > 0 ? monthlyRevenue : 0
        },

        // Administrative summary
        adminSummary: {
          totalRequests: clientRequests.length,
          lastRequestDate: clientRequests[0]?.createdAt || null,
          lastActivity,
          hasProductionRequest,
          requiresAttention,
          onboardingProgress: hasProductionRequest ? 100 : (clientRequests.length > 0 ? 50 : 0)
        }
      }
    })

    // Apply post-processing filters
    if (revenue_min || revenue_max || has_production_request || requires_attention) {
      enhancedClientsData = enhancedClientsData.filter(client => {
        // Revenue range filter
        if (revenue_min && client.revenueMetrics.monthlyRevenue < parseFloat(revenue_min)) {
          return false
        }
        if (revenue_max && client.revenueMetrics.monthlyRevenue > parseFloat(revenue_max)) {
          return false
        }

        // Production request filter
        if (has_production_request === 'true' && !client.adminSummary.hasProductionRequest) {
          return false
        }
        if (has_production_request === 'false' && client.adminSummary.hasProductionRequest) {
          return false
        }

        // Attention required filter
        if (requires_attention === 'true' && !client.adminSummary.requiresAttention) {
          return false
        }
        if (requires_attention === 'false' && client.adminSummary.requiresAttention) {
          return false
        }

        return true
      })
    }

    // Get total count for pagination
    const [totalCount] = whereConditions.length > 0
      ? await db.select({ count: count() }).from(clients).where(and(...whereConditions))
      : await db.select({ count: count() }).from(clients)

    return c.json({
      clients: enhancedClientsData,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin clients error:', error)
    return c.json({ error: 'Failed to fetch clients' }, 500)
  }
})

// Get client analytics including average fees
app.get('/clients/analytics', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    // Get total clients
    const [totalClients] = await db.select({ count: count() }).from(clients)

    // Get clients with fees set (prioritize averageMonthlyFee, fallback to classFee)
    const [clientsWithFees] = await db.select({ count: count() })
      .from(clients)
      .where(or(isNotNull(clients.averageMonthlyFee), isNotNull(clients.classFee)))

    // Calculate average fee across all schools (prioritize averageMonthlyFee)
    const [avgFeeResult] = await db.select({
      averageFee: sql<number>`COALESCE(AVG(CAST(COALESCE(${clients.averageMonthlyFee}, ${clients.classFee}) AS DECIMAL)), 0)`
    })
    .from(clients)
    .where(or(isNotNull(clients.averageMonthlyFee), isNotNull(clients.classFee)))

    // Get total students across all schools
    const [totalStudents] = await db.select({
      total: sql<number>`COALESCE(SUM(${clients.actualStudentCount}), 0)`
    })
    .from(clients)

    // Get fee distribution (min, max) - prioritize averageMonthlyFee
    const [feeRange] = await db.select({
      minFee: sql<number>`COALESCE(MIN(CAST(COALESCE(${clients.averageMonthlyFee}, ${clients.classFee}) AS DECIMAL)), 0)`,
      maxFee: sql<number>`COALESCE(MAX(CAST(COALESCE(${clients.averageMonthlyFee}, ${clients.classFee}) AS DECIMAL)), 0)`
    })
    .from(clients)
    .where(or(isNotNull(clients.averageMonthlyFee), isNotNull(clients.classFee)))

    // Get partner attribution statistics
    const [partnerAttributed] = await db.select({
      count: sql<number>`COUNT(DISTINCT ${schoolReferrals.clientId})`
    })
    .from(schoolReferrals)
    .where(eq(schoolReferrals.isActive, true))

    return c.json({
      analytics: {
        totalClients: totalClients.count,
        clientsWithFees: clientsWithFees.count,
        averageFee: Math.round(avgFeeResult.averageFee * 100) / 100, // Round to 2 decimal places
        totalStudents: totalStudents.total,
        feeRange: {
          min: feeRange.minFee,
          max: feeRange.maxFee
        },
        partnerAttributed: partnerAttributed.count
      }
    })

  } catch (error) {
    console.error('Client analytics error:', error)
    return c.json({ error: 'Failed to fetch client analytics' }, 500)
  }
})

// Get all clients including those from software requests (pending subscription clients)
app.get('/clients/all-including-requests', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const {
      page = '1',
      limit = '10',
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    console.log('📊 Fetching all clients including software request clients')

    // First, get clients with active subscriptions
    const clientsWithSubscriptions = await db
      .select({
        client: {
          id: clients.id,
          schoolName: clients.schoolName,
          schoolCode: clients.schoolCode,
          email: clients.email,
          phone: clients.phone,
          contactPerson: clients.contactPerson,
          actualStudentCount: clients.actualStudentCount,
          status: clients.status,
          createdAt: clients.createdAt,
          source: sql`'subscription'`.as('source')
        },
        subscription: {
          id: billingSubscriptions.id,
          status: billingSubscriptions.status,
          monthlyAmount: billingSubscriptions.monthlyAmount,
          pricePerStudent: billingSubscriptions.pricePerStudent,
          studentCount: billingSubscriptions.studentCount,
          billingCycle: billingSubscriptions.billingCycle,
          currentPeriodStart: billingSubscriptions.currentPeriodStart,
          currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
          nextBillingDate: billingSubscriptions.nextBillingDate
        }
      })
      .from(clients)
      .innerJoin(billingSubscriptions, and(
        eq(billingSubscriptions.clientId, clients.id),
        eq(billingSubscriptions.status, 'active')
      ))

    // Second, get production software requests that don't have corresponding clients yet
    const pendingProductionRequests = await db
      .select({
        id: softwareRequests.id,
        clientId: softwareRequests.clientId,
        primaryEmail: softwareRequests.primaryEmail,
        contactNumber: softwareRequests.contactNumber,
        completeAddress: softwareRequests.completeAddress,
        studentCount: softwareRequests.studentCount,
        facultyCount: softwareRequests.facultyCount,
        status: softwareRequests.status,
        requestType: softwareRequests.requestType,
        createdAt: softwareRequests.createdAt,
        averageMonthlyFee: softwareRequests.averageMonthlyFee,
        calculatedAverageFee: softwareRequests.calculatedAverageFee
      })
      .from(softwareRequests)
      .leftJoin(clients, eq(softwareRequests.clientId, clients.id))
      .where(and(
        eq(softwareRequests.requestType, 'production'),
        isNull(clients.id), // No corresponding client exists
        inArray(softwareRequests.status, ['pending', 'under_review', 'approved'])
      ))

    // Convert software requests to client format
    const pendingClients = pendingProductionRequests.map(req => ({
      client: {
        id: req.id, // Use request ID as temporary client ID
        schoolName: `School Request ${req.id.slice(0, 8)}`, // Generate temporary school name
        schoolCode: `REQ-${req.id.slice(0, 8)}`, // Generate temporary school code
        email: req.primaryEmail,
        phone: req.contactNumber,
        contactPerson: 'N/A',
        actualStudentCount: req.studentCount,
        status: 'pending_subscription',
        createdAt: req.createdAt,
        source: 'software_request'
      },
      subscription: null,
      softwareRequest: {
        id: req.id,
        status: req.status,
        requestType: req.requestType,
        studentCount: req.studentCount,
        facultyCount: req.facultyCount,
        averageMonthlyFee: req.averageMonthlyFee || req.calculatedAverageFee,
        completeAddress: req.completeAddress
      }
    }))

    // Combine both lists
    const allClients = [...clientsWithSubscriptions, ...pendingClients]

    // Apply search filter if provided
    let filteredClients = allClients
    if (search) {
      const searchLower = search.toLowerCase()
      filteredClients = allClients.filter(item =>
        item.client.schoolName?.toLowerCase().includes(searchLower) ||
        item.client.email?.toLowerCase().includes(searchLower) ||
        item.client.schoolCode?.toLowerCase().includes(searchLower)
      )
    }

    // Apply sorting
    filteredClients.sort((a, b) => {
      const aValue = a.client[sort_by as keyof typeof a.client] || ''
      const bValue = b.client[sort_by as keyof typeof b.client] || ''

      if (sort_order === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // Apply pagination
    const paginatedClients = filteredClients.slice(offset, offset + limitNum)

    console.log(`📊 Found ${clientsWithSubscriptions.length} clients with subscriptions`)
    console.log(`📊 Found ${pendingClients.length} pending software request clients`)
    console.log(`📊 Total combined: ${allClients.length}`)

    return c.json({
      clients: paginatedClients,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: filteredClients.length,
        totalPages: Math.ceil(filteredClients.length / limitNum)
      },
      summary: {
        totalClientsWithSubscriptions: clientsWithSubscriptions.length,
        totalPendingRequests: pendingClients.length,
        totalCombined: allClients.length,
        totalMonthlyRevenue: clientsWithSubscriptions.reduce((sum, item) =>
          sum + parseFloat(item.subscription?.monthlyAmount || '0'), 0
        )
      }
    })

  } catch (error) {
    console.error('All clients including requests error:', error)
    return c.json({ error: 'Failed to fetch all clients' }, 500)
  }
})

// Get clients with active subscriptions only (1:1 relationship) - MUST BE BEFORE /clients/:id
app.get('/clients/with-subscriptions', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const {
      page = '1',
      limit = '10',
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    console.log('📊 Fetching clients with active subscriptions only')

    // Build where conditions
    const whereConditions = [
      eq(billingSubscriptions.clientId, clients.id),
      eq(billingSubscriptions.status, 'active')
    ]

    // Add search filter if provided
    if (search) {
      whereConditions.push(
        or(
          ilike(clients.schoolName, `%${search}%`),
          ilike(clients.email, `%${search}%`),
          ilike(clients.schoolCode, `%${search}%`)
        )!
      )
    }

    // Build order by clause
    let orderByClause
    if (sort_by === 'school_name') {
      orderByClause = sort_order === 'asc' ? asc(clients.schoolName) : desc(clients.schoolName)
    } else if (sort_by === 'monthly_amount') {
      orderByClause = sort_order === 'asc' ? asc(billingSubscriptions.monthlyAmount) : desc(billingSubscriptions.monthlyAmount)
    } else {
      orderByClause = sort_order === 'asc' ? asc(clients.createdAt) : desc(clients.createdAt)
    }

    // Execute query with pagination
    const clientsWithSubscriptions = await db
      .select({
        client: {
          id: clients.id,
          schoolName: clients.schoolName,
          schoolCode: clients.schoolCode,
          email: clients.email,
          phone: clients.phone,
          contactPerson: clients.contactPerson,
          actualStudentCount: clients.actualStudentCount,
          status: clients.status,
          createdAt: clients.createdAt
        },
        subscription: {
          id: billingSubscriptions.id,
          status: billingSubscriptions.status,
          monthlyAmount: billingSubscriptions.monthlyAmount,
          pricePerStudent: billingSubscriptions.pricePerStudent,
          studentCount: billingSubscriptions.studentCount,
          billingCycle: billingSubscriptions.billingCycle,
          currentPeriodStart: billingSubscriptions.currentPeriodStart,
          currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
          nextBillingDate: billingSubscriptions.nextBillingDate
        }
      })
      .from(clients)
      .innerJoin(billingSubscriptions, and(...whereConditions))
      .orderBy(orderByClause)
      .limit(limitNum)
      .offset(offset)

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(clients)
      .innerJoin(billingSubscriptions, and(
        eq(billingSubscriptions.clientId, clients.id),
        eq(billingSubscriptions.status, 'active')
      ))

    console.log(`📊 Found ${clientsWithSubscriptions.length} clients with active subscriptions`)

    return c.json({
      clients: clientsWithSubscriptions,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      },
      summary: {
        totalClientsWithSubscriptions: totalCount.count,
        totalMonthlyRevenue: clientsWithSubscriptions.reduce((sum, item) =>
          sum + parseFloat(item.subscription.monthlyAmount || '0'), 0
        )
      }
    })

  } catch (error) {
    console.error('Clients with subscriptions error:', error)
    return c.json({ error: 'Failed to fetch clients with subscriptions' }, 500)
  }
})

// Get specific client details with comprehensive school information
app.get('/clients/:id', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const clientId = c.req.param('id')

    // Get client with subscription and partner data
    const [clientData] = await db
      .select({
        client: clients,
        subscription: billingSubscriptions,
        partner: {
          id: schoolReferrals.partnerId,
          name: partners.name,
          code: partners.partnerCode,
          referralCode: referralCodes.code,
          referredAt: schoolReferrals.referredAt,
          referralSource: schoolReferrals.referralSource
        }
      })
      .from(clients)
      .leftJoin(billingSubscriptions, and(
        eq(billingSubscriptions.clientId, clients.id),
        eq(billingSubscriptions.status, 'active')
      ))
      .leftJoin(schoolReferrals, and(
        eq(schoolReferrals.clientId, clients.id),
        eq(schoolReferrals.isActive, true)
      ))
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .where(eq(clients.id, clientId))
      .limit(1)

    if (!clientData) {
      return c.json({ error: 'Client not found' }, 404)
    }

    const client = clientData.client

    // Get client users
    const users = await db.select().from(clientUsers).where(eq(clientUsers.clientId, clientId))

    // Get software requests for this client with comprehensive details
    const requests = await db.select().from(softwareRequests).where(eq(softwareRequests.clientId, clientId)).orderBy(desc(softwareRequests.createdAt))

    // Get the most recent production request for comprehensive school details
    const latestProductionRequest = requests.find((req: any) => req.requestType === 'production') || requests[0]

    // Calculate comprehensive revenue metrics
    const feePerStudent = client.averageMonthlyFee
      ? parseFloat(client.averageMonthlyFee)
      : (latestProductionRequest?.averageMonthlyFee ? parseFloat(latestProductionRequest.averageMonthlyFee) : 0)

    const monthlyRevenue = feePerStudent * (client.actualStudentCount || 0)
    const annualRevenue = monthlyRevenue * 12

    // Prepare comprehensive school details from software request
    const schoolDetails = latestProductionRequest ? {
      // Basic school information
      schoolName: client.schoolName,
      schoolCode: client.schoolCode,
      email: client.email,
      phone: client.phone,

      // Detailed information from software request
      averageMonthlyFee: latestProductionRequest.averageMonthlyFee ? parseFloat(latestProductionRequest.averageMonthlyFee) : null,
      studentCount: latestProductionRequest.studentCount,
      facultyCount: latestProductionRequest.facultyCount,
      completeAddress: latestProductionRequest.completeAddress,
      contactNumber: latestProductionRequest.contactNumber,
      primaryEmail: latestProductionRequest.primaryEmail,

      // Legacy fee structure (for reference)
      legacyFeeStructure: {
        class1Fee: latestProductionRequest.class1Fee ? parseFloat(latestProductionRequest.class1Fee) : null,
        class4Fee: latestProductionRequest.class4Fee ? parseFloat(latestProductionRequest.class4Fee) : null,
        class6Fee: latestProductionRequest.class6Fee ? parseFloat(latestProductionRequest.class6Fee) : null,
        class10Fee: latestProductionRequest.class10Fee ? parseFloat(latestProductionRequest.class10Fee) : null,
        class11Fee: latestProductionRequest.class11Fee ? parseFloat(latestProductionRequest.class11Fee) : null,
        class12Fee: latestProductionRequest.class12Fee ? parseFloat(latestProductionRequest.class12Fee) : null,
        calculatedAverageFee: latestProductionRequest.calculatedAverageFee ? parseFloat(latestProductionRequest.calculatedAverageFee) : null,
      },

      // Request status and dates
      requestStatus: latestProductionRequest.status,
      requestType: latestProductionRequest.requestType,
      termsAccepted: latestProductionRequest.termsAccepted,
      termsAcceptedAt: latestProductionRequest.termsAcceptedAt,
      createdAt: latestProductionRequest.createdAt,
      updatedAt: latestProductionRequest.updatedAt
    } : null

    // Enhanced client data with comprehensive information
    const enhancedClient = {
      ...client,
      subscription: clientData.subscription,
      partner: clientData.partner.id ? clientData.partner : null,

      // Revenue metrics
      revenueMetrics: {
        monthlyRevenue,
        annualRevenue,
        feePerStudent,
        totalRequests: requests.length,
        activeRequests: requests.filter((req: any) => ['pending', 'under_review', 'approved'].includes(req.status)).length
      },

      // Administrative summary
      adminSummary: {
        totalRequests: requests.length,
        lastRequestDate: requests[0]?.createdAt || null,
        hasProductionRequest: requests.some((req: any) => req.requestType === 'production'),
        requiresAttention: requests.some((req: any) => req.status === 'pending') || !client.averageMonthlyFee
      }
    }

    return c.json({
      client: enhancedClient,
      users,
      requests,
      schoolDetails // Comprehensive school information for admin view
    })

  } catch (error) {
    console.error('Admin client details error:', error)
    return c.json({ error: 'Failed to fetch client details' }, 500)
  }
})

// Get comprehensive client details with complete history and analytics
app.get('/clients/:id/comprehensive', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const clientId = c.req.param('id')

    // Get client with all related data
    const [clientData] = await db
      .select({
        client: clients,
        subscription: billingSubscriptions,
        partner: {
          id: schoolReferrals.partnerId,
          name: partners.name,
          code: partners.partnerCode,
          referralCode: referralCodes.code,
          referredAt: schoolReferrals.referredAt,
          referralSource: schoolReferrals.referralSource
        }
      })
      .from(clients)
      .leftJoin(billingSubscriptions, and(
        eq(billingSubscriptions.clientId, clients.id),
        eq(billingSubscriptions.status, 'active')
      ))
      .leftJoin(schoolReferrals, and(
        eq(schoolReferrals.clientId, clients.id),
        eq(schoolReferrals.isActive, true)
      ))
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .where(eq(clients.id, clientId))

    if (!clientData) {
      return c.json({ error: 'Client not found' }, 404)
    }

    const client = clientData.client

    // Get all software requests with complete details
    const requests = await db
      .select()
      .from(softwareRequests)
      .where(eq(softwareRequests.clientId, clientId))
      .orderBy(desc(softwareRequests.createdAt))

    // Get client users
    const users = await db
      .select()
      .from(clientUsers)
      .where(eq(clientUsers.clientId, clientId))

    // Get billing history (invoices and payments)
    const billingHistory = await db
      .select({
        invoice: billingInvoices,
        payment: billingPayments
      })
      .from(billingInvoices)
      .leftJoin(billingPayments, eq(billingPayments.invoiceId, billingInvoices.id))
      .where(eq(billingInvoices.clientId, clientId))
      .orderBy(desc(billingInvoices.createdAt))

    // Get support tickets
    const supportTicketsData = await db
      .select()
      .from(supportTickets)
      .where(eq(supportTickets.clientId, clientId))
      .orderBy(desc(supportTickets.createdAt))
      .limit(10) // Latest 10 tickets

    // Get audit logs for this client
    const auditLogsData = await db
      .select()
      .from(auditLogs)
      .where(sql`${auditLogs.details}->>'clientId' = ${clientId}`)
      .orderBy(desc(auditLogs.timestamp))
      .limit(20) // Latest 20 audit entries

    // Calculate comprehensive analytics
    const latestProductionRequest = requests.find((req: any) => req.requestType === 'production') || requests[0]

    // Revenue calculations
    const feePerStudent = client.averageMonthlyFee
      ? parseFloat(client.averageMonthlyFee)
      : (latestProductionRequest?.averageMonthlyFee ? parseFloat(latestProductionRequest.averageMonthlyFee) : 0)

    const monthlyRevenue = feePerStudent * (client.actualStudentCount || 0)
    const annualRevenue = monthlyRevenue * 12

    // Request analytics
    const requestsByType = requests.reduce((acc: any, req: any) => {
      acc[req.requestType] = (acc[req.requestType] || 0) + 1
      return acc
    }, {})

    const requestsByStatus = requests.reduce((acc: any, req: any) => {
      acc[req.status] = (acc[req.status] || 0) + 1
      return acc
    }, {})

    // Timeline analysis
    const requestTimeline = requests.map((req: any) => ({
      id: req.id,
      type: req.requestType,
      status: req.status,
      createdAt: req.createdAt,
      approvedAt: req.approvedAt,
      studentCount: req.studentCount,
      facultyCount: req.facultyCount,
      monthlyFee: req.averageMonthlyFee
    }))

    // Billing analytics
    const totalInvoices = billingHistory.length
    const totalPaid = billingHistory
      .filter((item: any) => item.payment?.status === 'completed')
      .reduce((sum: number, item: any) => sum + (parseFloat(item.payment?.amount || '0')), 0)

    const pendingAmount = billingHistory
      .filter((item: any) => item.invoice?.status === 'pending')
      .reduce((sum: number, item: any) => sum + (parseFloat(item.invoice?.amount || '0')), 0)

    // Communication history
    const communicationHistory = [
      ...supportTicketsData.map((ticket: any) => ({
        type: 'support_ticket',
        id: ticket.id,
        subject: ticket.subject,
        status: ticket.status,
        priority: ticket.priority,
        createdAt: ticket.createdAt,
        updatedAt: ticket.updatedAt,
        timestamp: ticket.createdAt // Normalize timestamp field
      })),
      ...auditLogsData.map((log: any) => ({
        type: 'admin_action',
        id: log.id,
        action: log.action,
        details: log.details,
        adminId: log.adminId,
        timestamp: log.timestamp,
        createdAt: log.timestamp // Normalize createdAt field
      }))
    ].sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Comprehensive client data
    const comprehensiveClient = {
      // Basic client information
      ...client,
      subscription: clientData.subscription,
      partner: clientData.partner.id ? clientData.partner : null,

      // Enhanced metrics
      revenueMetrics: {
        monthlyRevenue,
        annualRevenue,
        feePerStudent,
        totalRequests: requests.length,
        activeRequests: requests.filter((req: any) => ['pending', 'under_review', 'approved'].includes(req.status)).length,
        averageRequestValue: requests.length > 0 ? monthlyRevenue : 0
      },

      // Request analytics
      requestAnalytics: {
        total: requests.length,
        byType: requestsByType,
        byStatus: requestsByStatus,
        timeline: requestTimeline,
        latestProductionRequest
      },

      // Billing analytics
      billingAnalytics: {
        totalInvoices,
        totalPaid,
        pendingAmount,
        paymentHistory: billingHistory
      },

      // Administrative summary
      adminSummary: {
        totalRequests: requests.length,
        lastRequestDate: requests[0]?.createdAt || null,
        lastActivity: communicationHistory[0]?.timestamp || client.createdAt,
        hasProductionRequest: requests.some((req: any) => req.requestType === 'production'),
        requiresAttention: requests.some((req: any) => req.status === 'pending') || !client.averageMonthlyFee || monthlyRevenue === 0,
        onboardingProgress: requests.some((req: any) => req.requestType === 'production') ? 100 : (requests.length > 0 ? 50 : 0),
        supportTicketsCount: supportTicketsData.length,
        openTicketsCount: supportTicketsData.filter((ticket: any) => ticket.status === 'open').length
      }
    }

    return c.json({
      client: comprehensiveClient,
      users,
      requests,
      supportTickets: supportTicketsData,
      auditLogs: auditLogsData,
      communicationHistory,
      analytics: {
        revenue: {
          monthly: monthlyRevenue,
          annual: annualRevenue,
          perStudent: feePerStudent
        },
        requests: {
          total: requests.length,
          byType: requestsByType,
          byStatus: requestsByStatus
        },
        billing: {
          totalInvoices,
          totalPaid,
          pendingAmount
        }
      }
    })

  } catch (error) {
    console.error('Admin comprehensive client error:', error)
    return c.json({ error: 'Failed to fetch comprehensive client data' }, 500)
  }
})

// Update client information
app.put('/clients/:id', adminAuthMiddleware, requirePermission('clients:write'), async (c) => {
  try {
    const clientId = c.req.param('id')
    const updateData = await c.req.json()

    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    await db.update(clients)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(clients.id, clientId))

    return c.json({
      message: 'Client updated successfully',
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Admin update client error:', error)
    return c.json({ error: 'Failed to update client' }, 500)
  }
})

// ===== SUBSCRIPTION & BILLING MANAGEMENT =====

// Validation schemas for subscription management
const createSubscriptionSchema = z.object({
  clientId: z.string().uuid(),
  planId: z.string().uuid().optional(),
  planName: z.string().default('Basic Plan'),
  studentCount: z.number().min(1).max(10000),
  pricePerStudent: z.number().min(10).max(10000), // Minimum ₹10, Maximum ₹10,000 per student
  startDate: z.string().refine((date) => {
    const parsedDate = new Date(date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return parsedDate >= today
  }, { message: "Start date must be today or in the future" }),
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly'),
  autoRenew: z.boolean().default(true),
  nextBillingDate: z.number().min(1).max(31).default(15), // Due date of the month (1-31)
  gracePeriodDays: z.number().min(0).max(30).default(3), // Grace period in days
  discountPercentage: z.number().min(0).max(100).default(0),
  setupFee: z.number().min(0).default(0),
  notes: z.string().optional(),
  // Operational expenses (admin/partner only)
  operationalExpenses: z.object({
    databaseCosts: z.number().min(0).default(0),
    websiteMaintenance: z.number().min(0).default(0),
    supportCosts: z.number().min(0).default(0),
    infrastructureCosts: z.number().min(0).default(0)
  }).optional()
})

const updateSubscriptionSchema = z.object({
  studentCount: z.number().min(1).optional(),
  pricePerStudent: z.number().min(0).optional(),
  billingCycle: z.enum(['monthly', 'quarterly', 'yearly']).optional(),
  startDate: z.string().optional(),
  gracePeriodDays: z.union([z.number(), z.string()]).optional(),
  setupFee: z.number().min(0).optional(),
  discountPercentage: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  status: z.enum(['active', 'cancelled', 'suspended']).optional(),
  autoRenew: z.boolean().optional(),
  // Operational expenses (admin/partner only)
  operationalExpenses: z.object({
    databaseCosts: z.number().min(0).default(0),
    websiteMaintenance: z.number().min(0).default(0),
    supportCosts: z.number().min(0).default(0),
    infrastructureCosts: z.number().min(0).default(0)
  }).optional()
})

const generateInvoiceSchema = z.object({
  id: z.string().uuid(),
  nextBillingDate: z.string().optional()
})

const bulkGenerateBillingSchema = z.object({
  month: z.number().min(1).max(12),
  year: z.number().min(2024),
  nextBillingDate: z.string().optional()
})

const updateSubscriptionAmountSchema = z.object({
  monthlyAmount: z.number().min(0),
  effectiveDate: z.string().optional()
})

// NOTE: Subscription list endpoint moved to line 3055 to avoid duplication

// Get subscription statistics (MUST be before /:id route)
app.get('/subscriptions/stats', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    // Get total subscriptions count
    const [totalSubscriptions] = await db
      .select({ count: count() })
      .from(billingSubscriptions)

    // Get active subscriptions count
    const [activeSubscriptions] = await db
      .select({ count: count() })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))

    // Get monthly gross revenue (sum of all active subscription monthly amounts)
    const [monthlyGrossRevenue] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingSubscriptions.monthlyAmount} AS DECIMAL)), 0)`
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))

    // Get monthly operational expenses
    const currentDate = new Date()
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
    const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)

    const [monthlyExpenses] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${subscriptionExpenses.monthlyOperationalCost} AS DECIMAL)), 0)`
      })
      .from(subscriptionExpenses)
      .where(and(
        eq(subscriptionExpenses.isActive, true),
        gte(subscriptionExpenses.effectiveFrom, startOfMonth.toISOString().split('T')[0])
      ))

    // Get monthly partner commissions
    const [monthlyPartnerCommissions] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`
      })
      .from(partnerEarnings)
      .where(and(
        gte(partnerEarnings.calculatedAt, startOfMonth),
        lte(partnerEarnings.calculatedAt, endOfMonth)
      ))

    // Calculate net monthly revenue
    const monthlyNetRevenue = Math.max(0,
      (monthlyGrossRevenue.total || 0) -
      (monthlyExpenses.total || 0) -
      (monthlyPartnerCommissions.total || 0)
    )

    // Get yearly gross revenue (monthly * 12 for active subscriptions)
    const [yearlyGrossRevenue] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${subscriptions.monthlyAmount} AS DECIMAL) * 12), 0)`
      })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'active'))

    // Get yearly expenses and commissions
    const startOfYear = new Date(currentDate.getFullYear(), 0, 1)

    const [yearlyExpenses] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${subscriptionExpenses.monthlyOperationalCost} AS DECIMAL) * 12), 0)`
      })
      .from(subscriptionExpenses)
      .where(and(
        eq(subscriptionExpenses.isActive, true),
        gte(subscriptionExpenses.effectiveFrom, startOfYear.toISOString().split('T')[0])
      ))

    const [yearlyPartnerCommissions] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`
      })
      .from(partnerEarnings)
      .where(gte(partnerEarnings.calculatedAt, startOfYear))

    // Calculate net yearly revenue
    const yearlyNetRevenue = Math.max(0,
      (yearlyGrossRevenue.total || 0) -
      (yearlyExpenses.total || 0) -
      (yearlyPartnerCommissions.total || 0)
    )

    // Get subscription status breakdown
    const statusBreakdown = await db
      .select({
        status: subscriptions.status,
        count: count()
      })
      .from(subscriptions)
      .groupBy(subscriptions.status)

    // Get billing cycle breakdown
    const billingCycleBreakdown = await db
      .select({
        billingCycle: subscriptions.billingCycle,
        count: count()
      })
      .from(subscriptions)
      .groupBy(subscriptions.billingCycle)

    return c.json({
      stats: {
        totalSubscriptions: totalSubscriptions.count,
        activeSubscriptions: activeSubscriptions.count,
        monthlyRevenue: monthlyNetRevenue,
        yearlyRevenue: yearlyNetRevenue,
        statusBreakdown,
        billingCycleBreakdown
      }
    })

  } catch (error) {
    console.error('Subscription stats error:', error)
    return c.json({ error: 'Failed to fetch subscription statistics' }, 500)
  }
})

// Get specific subscription details
app.get('/subscriptions/:id', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const [subscription] = await db.select({
      id: billingSubscriptions.id,
      clientId: billingSubscriptions.clientId,
      planId: billingSubscriptions.planId,
      studentCount: billingSubscriptions.studentCount,
      currentPeriodStart: billingSubscriptions.currentPeriodStart,
      currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
      status: billingSubscriptions.status,
      autoRenew: billingSubscriptions.autoRenew,
      createdAt: billingSubscriptions.createdAt,
      updatedAt: billingSubscriptions.updatedAt,
      // Subscription-specific pricing and billing information
      pricePerStudent: billingSubscriptions.pricePerStudent,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      billingCycle: billingSubscriptions.billingCycle,
      nextBillingDate: billingSubscriptions.nextBillingDate,
      gracePeriodDays: billingSubscriptions.gracePeriodDays,
      penaltyRate: billingSubscriptions.penaltyRate,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email,
      phone: clients.phone
    })
    .from(billingSubscriptions)
    .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
    .where(eq(billingSubscriptions.id, subscriptionId))
    .limit(1)

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Get enhanced subscription data including operational expenses
    const [enhancedSubscription] = await db.select({
      id: billingSubscriptions.id,
      clientId: billingSubscriptions.clientId,
      studentCount: billingSubscriptions.studentCount,
      pricePerStudent: billingSubscriptions.pricePerStudent,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      currentPeriodStart: billingSubscriptions.currentPeriodStart,
      currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
      nextBillingDate: billingSubscriptions.nextBillingDate,
      status: billingSubscriptions.status,
      gracePeriodDays: billingSubscriptions.gracePeriodDays,
      currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage,
      billingCycle: billingSubscriptions.billingCycle,
      createdAt: billingSubscriptions.createdAt,
      updatedAt: billingSubscriptions.updatedAt,
      // Operational expenses fields
      operationalExpenses: billingSubscriptions.operationalExpenses,
      databaseCosts: billingSubscriptions.databaseCosts,
      websiteMaintenance: billingSubscriptions.websiteMaintenance,
      supportCosts: billingSubscriptions.supportCosts,
      infrastructureCosts: billingSubscriptions.infrastructureCosts,
      totalOperationalExpenses: billingSubscriptions.totalOperationalExpenses,
      notes: billingSubscriptions.notes,
      setupFee: billingSubscriptions.setupFee
    })
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.id, subscriptionId))
    .limit(1)

    // Get billing cycles for this subscription
    const billingCyclesList = await db.select({
      id: billingSubscriptions.id,
      clientId: billingSubscriptions.clientId,
      studentCount: billingSubscriptions.studentCount,
      pricePerStudent: billingSubscriptions.pricePerStudent,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      currentPeriodStart: billingSubscriptions.currentPeriodStart,
      currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
      nextBillingDate: billingSubscriptions.nextBillingDate,
      status: billingSubscriptions.status,
      gracePeriodDays: billingSubscriptions.gracePeriodDays,
      currentDiscountPercentage: billingSubscriptions.currentDiscountPercentage,
      createdAt: billingSubscriptions.createdAt,
      updatedAt: billingSubscriptions.updatedAt
    })
    .from(billingSubscriptions)
    .where(eq(billingSubscriptions.id, subscriptionId))
    .orderBy(desc(billingSubscriptions.currentPeriodStart))

    // Get recent invoices
    const invoicesList = subscription.clientId ? await db.select()
      .from(billingInvoices)
      .where(eq(billingInvoices.clientId, subscription.clientId))
      .orderBy(desc(billingInvoices.createdAt))
      .limit(10) : []

    // Prepare operational expenses data
    const operationalExpensesData = enhancedSubscription ? {
      operationalExpenses: enhancedSubscription.operationalExpenses || {
        databaseCosts: 0,
        websiteMaintenance: 0,
        supportCosts: 0,
        infrastructureCosts: 0
      },
      databaseCosts: parseFloat(enhancedSubscription.databaseCosts || '0'),
      websiteMaintenance: parseFloat(enhancedSubscription.websiteMaintenance || '0'),
      supportCosts: parseFloat(enhancedSubscription.supportCosts || '0'),
      infrastructureCosts: parseFloat(enhancedSubscription.infrastructureCosts || '0'),
      totalOperationalExpenses: parseFloat(enhancedSubscription.totalOperationalExpenses || '0'),
      notes: enhancedSubscription.notes || '',
      setupFee: parseFloat(enhancedSubscription.setupFee || '0'),
      billingCycle: enhancedSubscription.billingCycle || 'monthly',
      currentDiscountPercentage: parseFloat(enhancedSubscription.currentDiscountPercentage || '0')
    } : {
      operationalExpenses: {
        databaseCosts: 0,
        websiteMaintenance: 0,
        supportCosts: 0,
        infrastructureCosts: 0
      },
      databaseCosts: 0,
      websiteMaintenance: 0,
      supportCosts: 0,
      infrastructureCosts: 0,
      totalOperationalExpenses: 0,
      notes: '',
      setupFee: 0,
      billingCycle: 'monthly',
      currentDiscountPercentage: 0
    }

    console.log('📊 Subscription operational expenses data:', {
      subscriptionId,
      operationalExpensesData,
      enhancedSubscriptionExists: !!enhancedSubscription
    })

    return c.json({
      subscription: {
        ...subscription,
        ...operationalExpensesData
      },
      billingCycles: billingCyclesList,
      invoices: invoicesList
    })

  } catch (error) {
    console.error('Admin subscription details error:', error)
    return c.json({ error: 'Failed to fetch subscription details' }, 500)
  }
})

// Create new subscription for client
app.post('/subscriptions', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', createSubscriptionSchema), async (c) => {
  try {
    const subscriptionData = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      console.error('Subscription creation attempted without valid admin')
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Enhanced client validation with comprehensive checks
    const [client] = await db.select({
      id: clients.id,
      schoolName: clients.schoolName,
      email: clients.email,
      phone: clients.phone,
      status: clients.status,
      actualStudentCount: clients.actualStudentCount,
      createdAt: clients.createdAt
    }).from(clients).where(eq(clients.id, subscriptionData.clientId)).limit(1)

    if (!client) {
      console.error(`Subscription creation failed: Client ${subscriptionData.clientId} not found`)
      return c.json({
        error: 'Client not found',
        details: 'The specified client ID does not exist in the system'
      }, 404)
    }

    // Allow subscription creation for active and pending clients
    if (client.status !== 'active' && client.status !== 'pending') {
      console.error(`Subscription creation failed: Client ${client.id} has status ${client.status}`)
      return c.json({
        error: 'Cannot create subscription for inactive client',
        details: `Client status is '${client.status}'. Only active or pending clients can have subscriptions.`,
        suggestion: 'Please activate the client first or ensure the client status is correct.'
      }, 400)
    }

    // Validate student count against actual student count if available
    if (client.actualStudentCount && subscriptionData.studentCount > client.actualStudentCount * 1.2) {
      console.warn(`Subscription student count (${subscriptionData.studentCount}) significantly exceeds actual count (${client.actualStudentCount})`)
      return c.json({
        error: 'Student count validation failed',
        details: `Subscription student count (${subscriptionData.studentCount}) exceeds actual student count (${client.actualStudentCount}) by more than 20%`,
        suggestion: 'Please verify the student count or update the client\'s actual student count first'
      }, 400)
    }

    // ===== PARTNER REFERRAL VALIDATION =====
    // Check if school has an active partner referral
    const [partnerReferral] = await db
      .select({
        id: schoolReferrals.id,
        partnerId: schoolReferrals.partnerId,
        referralCode: referralCodes.code,
        partnerName: partners.name,
        partnerCompany: partners.companyName,
        profitSharePercentage: partners.profitSharePercentage,
        isActive: schoolReferrals.isActive,
        verifiedAt: schoolReferrals.verifiedAt
      })
      .from(schoolReferrals)
      .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(and(
        eq(schoolReferrals.clientId, subscriptionData.clientId),
        eq(schoolReferrals.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    // Allow direct clients (without partner referrals) for admin-created subscriptions
    let isDirectClient = false
    if (!partnerReferral) {
      console.log(`ℹ️ No partner referral found for client ${client.schoolName} - treating as direct client`)
      isDirectClient = true
    } else if (!partnerReferral.verifiedAt) {
      console.error(`❌ Subscription creation blocked: Partner referral not verified for client ${client.schoolName}`)
      return c.json({
        error: 'Partner referral not verified',
        details: 'The partner referral for this school has not been verified yet.',
        suggestion: 'Please verify the partner referral before creating a subscription.'
      }, 400)
    } else {
      console.log(`✅ Partner referral validated: ${partnerReferral.partnerName} (${partnerReferral.referralCode}) - ${partnerReferral.profitSharePercentage}% commission`)
    }

    // Enhanced subscription conflict check
    const existingSubscriptions = await db.select({
      id: subscriptions.id,
      status: subscriptions.status,
      startDate: subscriptions.startDate,
      endDate: subscriptions.endDate,
      planName: subscriptions.planName,
      studentCount: subscriptions.studentCount,
      monthlyAmount: subscriptions.monthlyAmount
    })
    .from(subscriptions)
    .where(and(
      eq(subscriptions.clientId, subscriptionData.clientId),
      inArray(subscriptions.status, ['active', 'pending', 'suspended'])
    ))

    if (existingSubscriptions.length > 0) {
      const activeSubscription = existingSubscriptions.find(sub => sub.status === 'active')
      const pendingSubscription = existingSubscriptions.find(sub => sub.status === 'pending')

      console.error(`Subscription creation failed: Client ${client.id} has existing subscriptions`, {
        existing: existingSubscriptions.map(sub => ({ id: sub.id, status: sub.status }))
      })

      return c.json({
        error: 'Client already has an existing subscription',
        details: activeSubscription
          ? `Client has an active subscription (${activeSubscription.planName}) since ${activeSubscription.startDate}`
          : `Client has a ${existingSubscriptions[0].status} subscription`,
        existingSubscriptions: existingSubscriptions.map(sub => ({
          id: sub.id,
          status: sub.status,
          planName: sub.planName,
          startDate: sub.startDate,
          endDate: sub.endDate,
          studentCount: sub.studentCount,
          monthlyAmount: sub.monthlyAmount
        })),
        suggestion: 'Cancel or modify the existing subscription before creating a new one'
      }, 400)
    }

    // Validate pricing parameters
    if (subscriptionData.pricePerStudent < 10) {
      return c.json({
        error: 'Invalid pricing',
        details: 'Price per student must be at least ₹10'
      }, 400)
    }

    if (subscriptionData.pricePerStudent > 10000) {
      return c.json({
        error: 'Invalid pricing',
        details: 'Price per student cannot exceed ₹10,000'
      }, 400)
    }

    // Validate start date
    const startDateValidation = new Date(subscriptionData.startDate)
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    if (startDateValidation < today) {
      const daysDiff = Math.floor((today.getTime() - startDateValidation.getTime()) / (1000 * 60 * 60 * 24))
      if (daysDiff > 30) {
        return c.json({
          error: 'Invalid start date',
          details: `Start date cannot be more than 30 days in the past (${daysDiff} days ago)`
        }, 400)
      }
    }

    // Enhanced date calculations with proper month-end handling
    const startDate = new Date(subscriptionData.startDate)

    // Calculate next billing date using proper date calculation utility
    const nextBillingDate = calculateNextBillingDate(startDate, subscriptionData.billingCycle)

    // Calculate period end date (day before next billing)
    const periodEndDate = calculatePeriodEndDate(startDate, subscriptionData.billingCycle)

    console.log(`📅 Billing dates calculated:`)
    console.log(`  Start: ${formatDateForDB(startDate)}`)
    console.log(`  Period End: ${formatDateForDB(periodEndDate)}`)
    console.log(`  Next Billing: ${formatDateForDB(nextBillingDate)}`)

    // Enhanced pricing calculations with validation
    const baseMonthlyAmount = subscriptionData.pricePerStudent * subscriptionData.studentCount

    // Validate minimum billing amount
    if (baseMonthlyAmount < 100) {
      return c.json({
        error: 'Minimum billing amount not met',
        details: `Total monthly amount (₹${baseMonthlyAmount}) must be at least ₹100`,
        suggestion: 'Increase student count or price per student'
      }, 400)
    }

    // Apply yearly discount if applicable (2-month discount = 16.67%)
    const yearlyDiscountPercentage = subscriptionData.billingCycle === 'yearly' ? 16.67 : 0
    const yearlyDiscountAmount = subscriptionData.billingCycle === 'yearly' ?
      (baseMonthlyAmount * 12 * yearlyDiscountPercentage) / 100 : 0

    // Calculate final monthly amount (no discount in new schema)
    const additionalDiscountAmount = 0

    // Calculate final monthly amount (for storage and billing)
    const finalMonthlyAmount = baseMonthlyAmount - additionalDiscountAmount

    // Validate final amount is positive
    if (finalMonthlyAmount <= 0) {
      return c.json({
        error: 'Invalid final amount',
        details: 'Final monthly amount after discounts must be positive',
        calculation: {
          monthlyAmount: baseMonthlyAmount,
          additionalDiscount: additionalDiscountAmount,
          finalAmount: finalMonthlyAmount
        }
      }, 400)
    }

    // Store operational expenses if provided with validation
    let expenseBreakdown = null
    if (subscriptionData.operationalExpenses) {
      const expenses = subscriptionData.operationalExpenses
      const totalExpenses = Object.values(expenses).reduce((sum, cost) => sum + cost, 0)

      // Validate expense amounts
      if (totalExpenses > finalMonthlyAmount * 0.8) {
        return c.json({
          error: 'Excessive operational expenses',
          details: `Total expenses (₹${totalExpenses}) cannot exceed 80% of monthly amount (₹${finalMonthlyAmount})`
        }, 400)
      }

      expenseBreakdown = {
        databaseCosts: expenses.databaseCosts,
        websiteMaintenance: expenses.websiteMaintenance,
        supportCosts: expenses.supportCosts,
        infrastructureCosts: expenses.infrastructureCosts,
        totalExpenses
      }
    }

    // Step 1: Create Razorpay plan for automatic billing
    console.log('🔄 Creating Razorpay plan for automatic billing...')
    const razorpayPlanResult = await razorpayService.createPlan({
      period: subscriptionData.billingCycle === 'yearly' ? 'yearly' : 'monthly',
      interval: 1,
      amount: Math.round(finalMonthlyAmount * 100), // Convert to paise
      currency: 'INR',
      description: `${subscriptionData.planName} - ${client.schoolName} (${subscriptionData.studentCount} students)`
    })

    if (!razorpayPlanResult.success) {
      console.error('❌ Failed to create Razorpay plan:', razorpayPlanResult.error)
      throw new Error(`Failed to create billing plan: ${razorpayPlanResult.error}`)
    }

    const razorpayPlan = razorpayPlanResult.plan
    console.log(`✅ Razorpay plan created: ${razorpayPlan.id}`)

    // Step 2: Create or find existing Razorpay customer for the school
    console.log('🔄 Checking for existing Razorpay customer...')
    let razorpayCustomer = null

    // Check for existing customer first
    const existingCustomerResult = await razorpayService.findCustomerByEmail(client.email)

    if (existingCustomerResult.success && existingCustomerResult.customer) {
      razorpayCustomer = existingCustomerResult.customer
      console.log(`✅ Found existing Razorpay customer: ${razorpayCustomer.id}`)
    }

    // If no existing customer found, create a new one
    if (!razorpayCustomer) {
      console.log('🔄 Creating new Razorpay customer...')
      const razorpayCustomerResult = await razorpayService.createCustomer({
        name: client.schoolName,
        email: client.email,
        contact: client.phone || undefined,
        failExisting: 0, // Don't fail if customer exists
        notes: {
          client_id: client.id,
          school_name: client.schoolName,
          student_count: subscriptionData.studentCount.toString(),
          plan_name: subscriptionData.planName
        }
      })

      if (!razorpayCustomerResult.success) {
        // Check if error is about existing customer
        if (razorpayCustomerResult.error && razorpayCustomerResult.error.includes('already exists')) {
          console.log('Customer already exists, trying to fetch existing customer...')
          const retryExistingCustomerResult = await razorpayService.findCustomerByEmail(client.email)

          if (retryExistingCustomerResult.success && retryExistingCustomerResult.customer) {
            razorpayCustomer = retryExistingCustomerResult.customer
            console.log(`✅ Retrieved existing Razorpay customer: ${razorpayCustomer.id}`)
          } else {
            console.error('❌ Failed to fetch existing customer:', retryExistingCustomerResult.error)
            throw new Error(`Failed to create or fetch customer: ${razorpayCustomerResult.error}`)
          }
        } else {
          console.error('❌ Failed to create Razorpay customer:', razorpayCustomerResult.error)
          throw new Error(`Failed to create customer: ${razorpayCustomerResult.error}`)
        }
      } else {
        razorpayCustomer = razorpayCustomerResult.customer
        console.log(`✅ Razorpay customer created: ${razorpayCustomer.id}`)
      }
    }

    // Step 3: Manual Billing Model - No Razorpay Subscription Creation
    // Schopio uses manual monthly billing where:
    // - Schools receive invoices monthly on their due dates
    // - Schools make manual payments via Razorpay orders (not automatic subscriptions)
    // - Subscription status is managed internally based on payment history
    // - No automatic recurring billing is used
    console.log('ℹ️ Using manual billing model - no automatic Razorpay subscription created')

    // Create subscription without transaction (Neon HTTP driver doesn't support transactions)
    try {
      // Create billing subscription record (basic fields only until migration is run)
      const subscriptionValues: any = {
        clientId: subscriptionData.clientId,
        studentCount: subscriptionData.studentCount,
        pricePerStudent: subscriptionData.pricePerStudent.toString(),
        currentPeriodStart: formatDateForDB(startDate),
        currentPeriodEnd: formatDateForDB(periodEndDate),
        nextBillingDate: formatDateForDB(nextBillingDate),
        monthlyAmount: finalMonthlyAmount.toString(),
        status: 'active',
        gracePeriodDays: subscriptionData.gracePeriodDays || 3
      }

        // Add operational expenses fields only if they exist in the schema
        try {
          // Try to add the new fields - if they don't exist, the insert will still work with basic fields
          if (expenseBreakdown) {
            subscriptionValues.operationalExpenses = expenseBreakdown
            subscriptionValues.databaseCosts = expenseBreakdown?.databaseCosts?.toString() || '0'
            subscriptionValues.websiteMaintenance = expenseBreakdown?.websiteMaintenance?.toString() || '0'
            subscriptionValues.supportCosts = expenseBreakdown?.supportCosts?.toString() || '0'
            subscriptionValues.infrastructureCosts = expenseBreakdown?.infrastructureCosts?.toString() || '0'
            subscriptionValues.totalOperationalExpenses = expenseBreakdown?.totalExpenses?.toString() || '0'
          }

          if (subscriptionData.notes) {
            subscriptionValues.notes = subscriptionData.notes
          }

          if (subscriptionData.setupFee) {
            subscriptionValues.setupFee = subscriptionData.setupFee.toString()
          }
        } catch (error) {
          console.log('⚠️ Some subscription fields not available in schema, using basic fields only')
        }

      const [newSubscription] = await db.insert(billingSubscriptions).values(subscriptionValues).returning()

        console.log(`✅ Subscription created successfully: ${newSubscription.id} for client ${client.schoolName}`)

        // Create operational expenses record if expenses are provided
        if (subscriptionData.operationalExpenses) {
          const totalExpenses = Object.values(subscriptionData.operationalExpenses).reduce((sum, cost) => sum + cost, 0);

          if (totalExpenses > 0) {
          await db.insert(subscriptionExpenses).values({
              subscriptionId: newSubscription.id,
              monthlyOperationalCost: totalExpenses.toString(),
              expenseBreakdown: subscriptionData.operationalExpenses, // Store the breakdown
              description: 'Operational expenses set during subscription creation',
              category: 'operational',
              effectiveFrom: subscriptionData.startDate,
              isActive: true,
              createdBy: admin.id
            });

            console.log(`✅ Operational expenses created: ₹${totalExpenses}/month with breakdown:`, subscriptionData.operationalExpenses);
          }
        }

        // ===== REAL-TIME PARTNER COMMISSION CALCULATION =====
        // Calculate and store partner commission immediately upon subscription creation (only for partner-referred clients)
        if (!isDirectClient && partnerReferral) {
          try {
            const grossAmount = finalMonthlyAmount
            const discountAmount = additionalDiscountAmount || 0
            const operationalExpenses = expenseBreakdown ? Object.values(expenseBreakdown).reduce((sum, cost) => sum + cost, 0) : 0

            // Net amount after discount and expenses
            const netAmount = grossAmount - discountAmount - operationalExpenses
            const partnerSharePercentage = parseFloat(partnerReferral.profitSharePercentage || '20')
            const partnerEarning = Math.max(0, (netAmount * partnerSharePercentage) / 100)

          if (partnerEarning > 0) {
            // Create partner earning record immediately
          await db.insert(partnerEarnings).values({
              partnerId: partnerReferral.partnerId,
              clientId: subscriptionData.clientId,
              invoiceId: '', // Will be updated when invoice is created
              grossAmount: grossAmount.toString(),
              totalExpenses: operationalExpenses.toString(),
              netProfit: netAmount.toString(),
              partnerSharePercentage: partnerSharePercentage.toString(),
              partnerEarning: partnerEarning.toString(),
              status: 'available', // Immediately available for withdrawal
              calculatedAt: new Date(),
              availableAt: new Date(),
              escrowStatus: 'released', // No escrow needed for subscription creation
              notes: `Commission for subscription creation - ${client.schoolName} (${subscriptionData.billingCycle} billing)`
            })

            console.log(`✅ Partner commission calculated and stored: ₹${partnerEarning} for ${partnerReferral.partnerName} (${partnerSharePercentage}% of ₹${netAmount})`)
            } else {
              console.log(`ℹ️ No partner commission calculated (net amount: ₹${netAmount}, expenses: ₹${operationalExpenses})`)
            }
          } catch (commissionError) {
            console.error('❌ Failed to calculate partner commission:', commissionError)
            // Don't fail the subscription creation if commission calculation fails
          }
        } else {
          console.log(`ℹ️ Direct client subscription - no partner commission calculated for ${client.schoolName}`)
        }

      const subscriptionResult = {
        subscription: newSubscription,
        pricing: {
          pricePerStudent: subscriptionData.pricePerStudent,
          studentCount: subscriptionData.studentCount,
          baseMonthlyAmount,
          yearlyDiscountAmount,
          additionalDiscountAmount,finalMonthlyAmount,
          billingCycle: subscriptionData.billingCycle,
          expenseBreakdown
        },
        razorpayIntegration: {
          planId: razorpayPlan.id,
          customerId: razorpayCustomer.id,
          billingModel: 'manual', // Manual billing - no automatic subscriptions
          status: 'manual_billing'
        }
      }

    // Log successful subscription creation for audit trail
    await auditLogger.logAdmin('subscription_created', {
      adminId: admin.id,
      resource: 'subscription',
      resourceId: subscriptionResult.subscription.id,
      details: {
        clientId: client.id,
        clientName: client.schoolName,
        planName: subscriptionData.planName,
        studentCount: subscriptionData.studentCount,
        monthlyAmount: finalMonthlyAmount,
        billingCycle: subscriptionData.billingCycle,
        startDate: startDate.toISOString().split('T')[0],
        pricing: subscriptionResult.pricing,
        operationalExpenses: expenseBreakdown
      },
      ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    // Send notification email to client about subscription creation
    try {
      const subscriptionEmailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb; margin-bottom: 20px;">Subscription Created Successfully</h2>

          <p>Dear ${client.schoolName} Team,</p>

          <p>Your Schopio subscription has been successfully created by our admin team. Here are the details:</p>

          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1e40af; margin-top: 0;">Subscription Details</h3>
            <p><strong>Plan:</strong> ${subscriptionData.planName}</p>
            <p><strong>Student Count:</strong> ${subscriptionData.studentCount} students</p>
            <p><strong>Monthly Amount:</strong> ₹${finalMonthlyAmount}</p>
            <p><strong>Billing Cycle:</strong> ${subscriptionData.billingCycle}</p>
            <p><strong>Start Date:</strong> ${startDate.toISOString().split('T')[0]}</p>
            <p><strong>Status:</strong> Pending (awaiting first payment)</p>
          </div>

          <p>You can now log in to your school portal to view billing details and make billingPayments.</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/school/login" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Access School Portal</a>
          </div>

          <p>If you have any questions, please contact our support team.</p>

          <p>Best regards,<br>Schopio Team</p>
        </div>
      `

      await emailService.sendEmail({
        to: client.email,
        subject: `Schopio Subscription Created - ${subscriptionData.planName}`,
        html: subscriptionEmailHtml
      })
      console.log(`📧 Subscription creation email sent to ${client.email}`)
    } catch (emailError) {
      console.error('Failed to send subscription creation email:', emailError)
      // Don't fail the subscription creation if email fails
    }

    return c.json({
      success: true,
      message: 'Subscription with automatic billing created successfully',
      subscription: {
        ...subscriptionResult.subscription,
        client: {
          id: client.id,
          schoolName: client.schoolName,
          email: client.email,
          actualStudentCount: client.actualStudentCount
        },
        pricing: subscriptionResult.pricing,
        razorpayIntegration: subscriptionResult.razorpayIntegration
      },
      createdBy: {
        id: admin.id,
        name: admin.name,
        email: admin.email
      },
      automaticBilling: {
        enabled: true,
        razorpayPlanId: subscriptionResult.razorpayIntegration.planId,
        razorpayCustomerId: subscriptionResult.razorpayIntegration.customerId,
        billingModel: subscriptionResult.razorpayIntegration.billingModel,
        status: subscriptionResult.razorpayIntegration.status,
        billingCycle: subscriptionData.billingCycle,
        nextBillingDate: nextBillingDate.toISOString().split('T')[0]
      },
      nextSteps: [
        '✅ Subscription created with automatic billing enabled',
        '✅ Razorpay plan and customer created',
        '✅ Automatic recurring billing configured',
        '📧 Client will receive email notification with setup instructions',
        '🔐 Client needs to complete authentication transaction to activate automatic billing',
        '💳 After authentication, monthly charges will be automatic',
        '📊 First billing cycle will be created on start date'
      ]
    })

    } catch (subscriptionError) {
      console.error('❌ Failed to create subscription records:', subscriptionError)
      throw subscriptionError
    }

  } catch (error) {
    console.error('❌ Subscription creation failed:', error)

    // Get admin and subscription data for error logging
    const adminForLogging = getCurrentAdmin(c)
    const subscriptionDataForLogging = c.req.valid('json')

    // Log failed subscription creation attempt
    try {
      await auditLogger.logAdmin('subscription_creation_failed', {
        adminId: adminForLogging?.id || 'unknown',
        resource: 'subscription',
        details: {
          clientId: subscriptionDataForLogging?.clientId,
          error: error instanceof Error ? error.message : 'Unknown error',
          requestData: subscriptionDataForLogging
        },
        ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
        userAgent: c.req.header('user-agent') || 'unknown',
        success: false
      })
    } catch (auditError) {
      console.error('Failed to log subscription creation failure:', auditError)
    }

    // Return appropriate error response
    if (error instanceof Error) {
      // Check for specific database errors
      if (error.message.includes('duplicate key') || error.message.includes('unique constraint')) {
        return c.json({
          error: 'Subscription creation conflict',
          details: 'A subscription with similar details already exists',
          suggestion: 'Please check existing subscriptions or contact support'
        }, 409)
      }

      if (error.message.includes('foreign key') || error.message.includes('violates')) {
        return c.json({
          error: 'Data validation error',
          details: 'Invalid reference data provided',
          suggestion: 'Please verify client ID and plan details'
        }, 400)
      }

      return c.json({
        error: 'Subscription creation failed',
        details: error.message,
        timestamp: new Date().toISOString()
      }, 500)
    }

    return c.json({
      error: 'Subscription creation failed',
      details: 'An unexpected error occurred',
      timestamp: new Date().toISOString()
    }, 500)
  }
})

// Update subscription
app.put('/subscriptions/:id', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', updateSubscriptionSchema), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const updateData = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Check if subscription exists in billingSubscriptions table
    const [existingSubscription] = await db.select()
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

    if (!existingSubscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Calculate monthly amount if pricePerStudent is provided
    let monthlyAmount = existingSubscription.monthlyAmount
    if (updateData.pricePerStudent && updateData.studentCount) {
      monthlyAmount = (updateData.pricePerStudent * updateData.studentCount).toString()
    } else if (updateData.pricePerStudent) {
      monthlyAmount = (updateData.pricePerStudent * existingSubscription.studentCount).toString()
    } else if (updateData.studentCount) {
      const currentPricePerStudent = parseFloat(existingSubscription.pricePerStudent || '0')
      monthlyAmount = (currentPricePerStudent * updateData.studentCount).toString()
    }

    // Prepare update data with proper date handling
    const updateFields: any = {
      updatedAt: new Date()
    }

    console.log('🔧 [Admin] Subscription update data received:', updateData)

    if (updateData.studentCount) updateFields.studentCount = updateData.studentCount
    if (updateData.pricePerStudent) updateFields.pricePerStudent = updateData.pricePerStudent.toString()
    if (monthlyAmount !== existingSubscription.monthlyAmount) updateFields.monthlyAmount = monthlyAmount
    if (updateData.billingCycle) updateFields.billingCycle = updateData.billingCycle

    // Handle date fields properly - only update fields that exist in the schema
    // Note: currentPeriodStart, currentPeriodEnd, nextBillingDate are not in the billingSubscriptions schema
    // These would be handled by the billing system automatically

    // Due date is calculated automatically based on 30-day cycles
    // No manual due date setting required

    if (updateData.gracePeriodDays) updateFields.gracePeriodDays = parseInt(updateData.gracePeriodDays.toString())
    if (updateData.setupFee !== undefined) updateFields.setupFee = updateData.setupFee.toString()
    if (updateData.discountPercentage !== undefined) {
      updateFields.discountPercentage = updateData.discountPercentage.toString()
      updateFields.currentDiscountPercentage = updateData.discountPercentage.toString()

      // Set discount dates if discount is being applied
      if (updateData.discountPercentage > 0) {
        try {
          updateFields.discountStartDate = new Date().toISOString().split('T')[0]
          // Set discount end date to 1 year from now if not specified
          const endDate = new Date()
          endDate.setFullYear(endDate.getFullYear() + 1)
          updateFields.discountEndDate = endDate.toISOString().split('T')[0]
          updateFields.discountReason = updateData.notes || 'Admin applied discount'
        } catch (error) {
          console.log('⚠️ [Admin] Discount date fields not available in schema')
        }
      }
    }
    if (updateData.notes !== undefined) updateFields.notes = updateData.notes
    if (updateData.status) updateFields.status = updateData.status

    // Handle operational expenses update
    if (updateData.operationalExpenses) {
      const expenses = updateData.operationalExpenses
      const totalExpenses = Object.values(expenses).reduce((sum, cost) => sum + cost, 0)

      try {
        // Try to update operational expenses fields if they exist in schema
        updateFields.operationalExpenses = expenses
        updateFields.databaseCosts = expenses.databaseCosts?.toString() || '0'
        updateFields.websiteMaintenance = expenses.websiteMaintenance?.toString() || '0'
        updateFields.supportCosts = expenses.supportCosts?.toString() || '0'
        updateFields.infrastructureCosts = expenses.infrastructureCosts?.toString() || '0'
        updateFields.totalOperationalExpenses = totalExpenses.toString()

        console.log('✅ [Admin] Operational expenses added to update:', expenses)
      } catch (error) {
        console.log('⚠️ [Admin] Operational expenses fields not available in schema, skipping')
      }
    }

    console.log('🔧 [Admin] Prepared update fields:', updateFields)

    // Update subscription in billingSubscriptions table
    const [updatedSubscription] = await db.update(billingSubscriptions)
      .set(updateFields)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .returning()

    // Also update client student count if it changed
    if (updateData.studentCount && existingSubscription.clientId) {
      await db.update(clients)
        .set({
          actualStudentCount: updateData.studentCount,
          updatedAt: new Date()
        })
        .where(eq(clients.id, existingSubscription.clientId))
    }

    return c.json({
      message: 'Subscription updated successfully',
      subscription: updatedSubscription,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update subscription error:', error)
    return c.json({ error: 'Failed to update subscription' }, 500)
  }
})

// ===== ADVANCED SUBSCRIPTION MANAGEMENT =====

// Advanced subscription update with billing cycle management and commission recalculation
const advancedSubscriptionUpdateSchema = z.object({
  changes: z.object({
    pricePerStudent: z.number().min(0).optional(),
    studentCount: z.number().min(1).optional(),
    operationalExpenses: z.object({
      databaseCosts: z.number().min(0).optional(),
      websiteMaintenance: z.number().min(0).optional(),
      supportCosts: z.number().min(0).optional(),
      infrastructureCosts: z.number().min(0).optional()
    }).optional(),
    billingCycle: z.enum(['monthly', 'yearly']).optional(),
    gracePeriodDays: z.number().min(0).max(30).optional()
  }),
  effectiveDate: z.string().transform(str => new Date(str)),
  reason: z.string().optional()
})

app.put('/subscriptions/:id/advanced-update', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', advancedSubscriptionUpdateSchema), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { changes, effectiveDate, reason } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log('🔄 Advanced subscription update requested:', {
      subscriptionId,
      changes,
      effectiveDate,
      adminId: admin.id
    })

    // Use the advanced subscription manager
    const result = await advancedSubscriptionManager.updateSubscriptionAdvanced({
      subscriptionId,
      changes,
      effectiveDate,
      adminId: admin.id,
      reason
    })

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    // Log the advanced update
    await auditLogger.log({
      action: 'ADVANCED_SUBSCRIPTION_UPDATE',
      resource: 'subscription',
      resourceId: subscriptionId,
      adminId: admin.id,
      details: {
        changes,
        effectiveDate,
        billingAdjustment: result.billingAdjustment,
        commissionRecalculation: result.commissionRecalculation,
        reason
      },
      success: true,
      severity: 'medium',
      category: 'payment',
      ipAddress: c.req.header('x-forwarded-for') || 'unknown'
    })

    return c.json({
      message: 'Subscription updated successfully with advanced billing management',
      subscription: result.subscription,
      billingAdjustment: result.billingAdjustment,
      commissionRecalculation: result.commissionRecalculation,
      changeLogId: result.changeLogId,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Advanced subscription update error:', error)
    return c.json({ error: 'Failed to update subscription' }, 500)
  }
})

// Get client subscription details
app.get('/clients/:clientId/subscription', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const clientId = c.req.param('clientId')

    const [subscription] = await db.select({
      id: subscriptions.id,
      studentCount: subscriptions.studentCount,
      startDate: subscriptions.startDate,
      endDate: subscriptions.endDate,
      nextBillingDate: subscriptions.nextBillingDate,
      status: subscriptions.status,
      autoRenew: subscriptions.autoRenew,
      // Subscription information (from subscriptions table)
      planName: subscriptions.planName,
      billingCycle: subscriptions.billingCycle,
      pricePerStudent: subscriptions.pricePerStudent,
      monthlyAmount: subscriptions.monthlyAmount,
      gracePeriodDays: subscriptions.gracePeriodDays,
      notes: subscriptions.notes,
      createdAt: subscriptions.createdAt,
      updatedAt: subscriptions.updatedAt
    })
    .from(subscriptions)
    .where(eq(subscriptions.clientId, clientId))
    .limit(1)

    if (!subscription) {
      return c.json({ error: 'No subscription found for this client' }, 404)
    }

    // Get recent billing cycles
    const recentBillingCycles = await db.select()
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscription.id))
      .orderBy(desc(billingSubscriptions.currentPeriodStart))
      .limit(5)

    return c.json({
      subscription,
      recentBillingCycles
    })

  } catch (error) {
    console.error('Get client subscription error:', error)
    return c.json({ error: 'Failed to fetch client subscription' }, 500)
  }
})

// ===== INVOICE MANAGEMENT =====

// Get all invoices with filtering
app.get('/invoices', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search, clientId } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    let baseQuery = db.select({
      id: billingInvoices.id,
      invoiceNumber: billingInvoices.invoiceNumber,
      clientId: billingInvoices.clientId,
      monthlyAmount: billingInvoices.totalAmount,
      taxAmount: billingInvoices.taxAmount,
      status: billingInvoices.status,
      issuedDate: billingInvoices.issuedDate,
      nextBillingDate: billingInvoices.dueDate,
      paidDate: billingInvoices.paidDate,
      createdAt: billingInvoices.createdAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email
    })
    .from(billingInvoices)
    .leftJoin(clients, eq(billingInvoices.clientId, clients.id))

    // Apply filters
    const conditions = []
    if (status) {
      conditions.push(eq(billingInvoices.status, status))
    }
    if (clientId) {
      conditions.push(eq(billingInvoices.clientId, clientId))
    }
    if (search) {
      conditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${billingInvoices.invoiceNumber} ILIKE ${`%${search}%`}`
      )
    }

    const invoicesList = conditions.length > 0
      ? await baseQuery.where(and(...conditions))
          .orderBy(desc(billingInvoices.createdAt))
          .limit(limitNum)
          .offset(offset)
      : await baseQuery
          .orderBy(desc(billingInvoices.createdAt))
          .limit(limitNum)
          .offset(offset)

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(billingInvoices)
      .leftJoin(clients, eq(billingInvoices.clientId, clients.id))

    return c.json({
      invoices: invoicesList,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin invoices error:', error)
    return c.json({ error: 'Failed to fetch invoices' }, 500)
  }
})

// Generate invoice for subscription
app.post('/invoices/generate', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', generateInvoiceSchema), async (c) => {
  try {
    const { id: subscriptionId, nextBillingDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get subscription details
    const [subscription] = await db.select({
      id: subscriptions.id,
      clientId: subscriptions.clientId,
      studentCount: subscriptions.studentCount,
      pricePerStudent: subscriptions.pricePerStudent,
      billingCycle: subscriptions.billingCycle,
      yearlyDiscountPercentage: subscriptions.yearlyDiscountPercentage
    })
    .from(subscriptions)
    .where(eq(subscriptions.id, subscriptionId))
    .limit(1)

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Calculate amounts based on billing cycle
    let monthlyAmount: number
    let discountAmount = 0
    let baseAmount = 0

    if (subscription.billingCycle === 'yearly') {
      // For yearly billing, calculate annual amount with discount
      const monthlyAmount = Number(subscription.pricePerStudent) * subscription.studentCount
      const annualAmountBeforeDiscount = monthlyAmount * 12
      const discountPercentage = Number(subscription.yearlyDiscountPercentage) || 16.67
      discountAmount = (annualAmountBeforeDiscount * discountPercentage) / 100
      baseAmount = annualAmountBeforeDiscount - discountAmount
    } else {
      // For monthly billing, use the monthly amount
      baseAmount = Number(subscription.pricePerStudent) * subscription.studentCount
    }

    const taxAmount = baseAmount * 0.18 // 18% GST
    const totalAmount = baseAmount + taxAmount

    // Generate invoice number
    const invoiceNumber = `INV-${Date.now()}-${subscription.clientId?.slice(-6).toUpperCase() || 'UNKNOWN'}`

    // Create billing cycle
    const cycleStart = new Date()
    const cycleEnd = new Date()
    if (subscription.billingCycle === 'monthly') {
      cycleEnd.setMonth(cycleEnd.getMonth() + 1)
    } else {
      cycleEnd.setFullYear(cycleEnd.getFullYear() + 1)
    }

    const [billingCycle] = await db.insert(billingSubscriptions).values({
      clientId: subscription.clientId,
      studentCount: subscription.studentCount,
      pricePerStudent: subscription.pricePerStudent.toString(),
      currentPeriodStart: cycleStart.toISOString().split('T')[0],
      currentPeriodEnd: cycleEnd.toISOString().split('T')[0],
      nextBillingDate: cycleEnd.toISOString().split('T')[0],
      monthlyAmount: totalAmount.toString(),
      status: 'pending'
    }).returning()

    // Create invoice
    const [invoice] = await db.insert(billingInvoices).values({
      subscriptionId: billingCycle.id,
      clientId: subscription.clientId,
      invoiceNumber,
      subtotal: baseAmount.toString(),
      taxAmount: taxAmount.toString(),
      totalAmount: totalAmount.toString(),
      status: 'sent',
      issuedDate: new Date().toISOString().split('T')[0],
      dueDate: cycleEnd.toISOString().split('T')[0],
      periodStart: cycleStart.toISOString().split('T')[0],
      periodEnd: cycleEnd.toISOString().split('T')[0]
    }).returning()

    return c.json({
      message: 'Invoice generated successfully',
      invoice,
      billingCycle,
      generatedBy: admin.name
    })

  } catch (error) {
    console.error('Generate invoice error:', error)
    return c.json({ error: 'Failed to generate invoice' }, 500)
  }
})

// ===== PAYMENT MANAGEMENT =====

// Get all payments with filtering
app.get('/payments', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search, clientId } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    let baseQuery = db.select({
      id: billingPayments.id,
      invoiceId: billingPayments.invoiceId,
      clientId: billingPayments.clientId,
      razorpayPaymentId: billingPayments.razorpayPaymentId,
      razorpayOrderId: billingPayments.razorpayOrderId,
      monthlyAmount: billingPayments.amount,
      currency: billingPayments.currency,
      status: billingPayments.status,
      paymentMethod: billingPayments.paymentMethod,
      createdAt: billingPayments.createdAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      // Invoice information
      invoiceNumber: billingInvoices.invoiceNumber,
      invoiceStatus: billingInvoices.status
    })
    .from(billingPayments)
    .leftJoin(clients, eq(billingPayments.clientId, clients.id))
    .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))

    // Apply filters
    const conditions = []
    if (status) {
      conditions.push(eq(billingPayments.status, status))
    }
    if (clientId) {
      conditions.push(eq(billingPayments.clientId, clientId))
    }
    if (search) {
      conditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${billingInvoices.invoiceNumber} ILIKE ${`%${search}%`}`
      )
    }

    const paymentsList = conditions.length > 0
      ? await baseQuery.where(and(...conditions))
          .orderBy(desc(billingPayments.createdAt))
          .limit(limitNum)
          .offset(offset)
      : await baseQuery
          .orderBy(desc(billingPayments.createdAt))
          .limit(limitNum)
          .offset(offset)

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(billingPayments)
      .leftJoin(clients, eq(billingPayments.clientId, clients.id))

    return c.json({
      payments: paymentsList,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin payments error:', error)
    return c.json({ error: 'Failed to fetch payments' }, 500)
  }
})

// Get payment details
app.get('/payments/:id', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const paymentId = c.req.param('id')

    const [payment] = await db.select({
      id: billingPayments.id,
      invoiceId: billingPayments.invoiceId,
      clientId: billingPayments.clientId,
      razorpayPaymentId: billingPayments.razorpayPaymentId,
      razorpayOrderId: billingPayments.razorpayOrderId,
      monthlyAmount: billingPayments.amount,
      currency: billingPayments.currency,
      status: billingPayments.status,
      paymentMethod: billingPayments.paymentMethod,
      failureReason: billingPayments.failureReason,
      createdAt: billingPayments.createdAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email,
      // Invoice information
      invoiceNumber: billingInvoices.invoiceNumber,
      invoiceAmount: billingInvoices.totalAmount,
      invoiceStatus: billingInvoices.status
    })
    .from(billingPayments)
    .leftJoin(clients, eq(billingPayments.clientId, clients.id))
    .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
    .where(eq(billingPayments.id, paymentId))
    .limit(1)

    if (!payment) {
      return c.json({ error: 'Payment not found' }, 404)
    }

    return c.json({ payment })

  } catch (error) {
    console.error('Get payment details error:', error)
    return c.json({ error: 'Failed to fetch payment details' }, 500)
  }
})

// Mark payment as verified (manual verification)
app.put('/payments/:id/verify', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const paymentId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get payment details
    const [payment] = await db.select()
      .from(billingPayments)
      .where(eq(billingPayments.id, paymentId))
      .limit(1)

    if (!payment) {
      return c.json({ error: 'Payment not found' }, 404)
    }

    // Update payment status
    const [updatedPayment] = await db.update(billingPayments)
      .set({
        status: 'success',
        createdAt: new Date()
      })
      .where(eq(billingPayments.id, paymentId))
      .returning()

    // Update invoice status
    if (payment.invoiceId) {
      await db.update(billingInvoices)
        .set({
          status: 'paid',
          paidDate: new Date().toISOString().split('T')[0]
        })
        .where(eq(billingInvoices.id, payment.invoiceId))
    }

    return c.json({
      message: 'Payment verified successfully',
      payment: updatedPayment,
      verifiedBy: admin.name
    })

  } catch (error) {
    console.error('Verify payment error:', error)
    return c.json({ error: 'Failed to verify payment' }, 500)
  }
})

// ===== SOFTWARE REQUEST MANAGEMENT =====

// Debug endpoint to check unique status values
app.get('/software-requests/debug/statuses', adminAuthMiddleware, requirePermission('requests:read'), async (c) => {
  try {
    const allRequests = await db.select({
      status: softwareRequests.status,
      requestType: softwareRequests.requestType,
      id: softwareRequests.id
    }).from(softwareRequests)

    const uniqueStatuses = [...new Set(allRequests.map(r => r.status))]
    const uniqueTypes = [...new Set(allRequests.map(r => r.requestType))]

    console.log('📊 All unique statuses in DB:', uniqueStatuses)
    console.log('📊 All unique types in DB:', uniqueTypes)
    console.log('📊 Total requests in DB:', allRequests.length)

    return c.json({
      uniqueStatuses,
      uniqueTypes,
      totalRequests: allRequests.length,
      sampleRequests: allRequests.slice(0, 5)
    })
  } catch (error) {
    console.error('Debug statuses error:', error)
    return c.json({ error: 'Failed to fetch debug data' }, 500)
  }
})

// Get all software requests with pagination and filters
app.get('/software-requests', adminAuthMiddleware, requirePermission('requests:read'), async (c) => {
  try {
    const { status, type, page = '1', limit = '20' } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    console.log('🔍 Software requests API called with:', { status, type, page, limit })

    // Build where conditions
    const whereConditions = []

    if (status) {
      // Support multiple statuses separated by comma
      const statusList = status.split(',').map((s: string) => s.trim())
      console.log('📊 Filtering by status list:', statusList)
      if (statusList.length === 1) {
        whereConditions.push(eq(softwareRequests.status, statusList[0]))
      } else {
        whereConditions.push(
          sql`${softwareRequests.status} IN (${statusList.map((s: string) => `'${s}'`).join(',')})`
        )
      }
    }

    if (type) {
      console.log('📊 Filtering by type:', type)
      whereConditions.push(eq(softwareRequests.requestType, type))
    }

    // Execute query with conditions
    const baseQuery = db.select({
      id: softwareRequests.id,
      clientId: softwareRequests.clientId,
      requestType: softwareRequests.requestType,
      status: softwareRequests.status,
      studentCount: softwareRequests.studentCount,
      facultyCount: softwareRequests.facultyCount,
      calculatedAverageFee: softwareRequests.calculatedAverageFee,
      termsAccepted: softwareRequests.termsAccepted,
      createdAt: softwareRequests.createdAt,
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email
    }).from(softwareRequests)
    .leftJoin(clients, eq(softwareRequests.clientId, clients.id))

    const requestsData = whereConditions.length > 0
      ? await baseQuery.where(and(...whereConditions)).orderBy(desc(softwareRequests.createdAt)).limit(limitNum).offset(offset)
      : await baseQuery.orderBy(desc(softwareRequests.createdAt)).limit(limitNum).offset(offset)

    // Get total count for pagination
    const [totalCount] = whereConditions.length > 0
      ? await db.select({ count: count() }).from(softwareRequests).where(and(...whereConditions))
      : await db.select({ count: count() }).from(softwareRequests)

    console.log('📊 Found requests:', requestsData.length)
    console.log('📊 Total count:', totalCount.count)
    console.log('📊 Sample request statuses:', requestsData.slice(0, 3).map(r => r.status))

    return c.json({
      requests: requestsData,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin software requests error:', error)
    return c.json({ error: 'Failed to fetch software requests' }, 500)
  }
})

// Get specific software request by ID
app.get('/software-requests/:id', adminAuthMiddleware, requirePermission('requests:read'), async (c) => {
  try {
    const requestId = c.req.param('id')

    // Get software request with client details
    const [requestData] = await db.select({
      request: softwareRequests,
      client: clients
    })
    .from(softwareRequests)
    .leftJoin(clients, eq(softwareRequests.clientId, clients.id))
    .where(eq(softwareRequests.id, requestId))
    .limit(1)

    if (!requestData) {
      return c.json({ error: 'Software request not found' }, 404)
    }

    // Combine request and client data for frontend compatibility
    const combinedRequest = {
      ...requestData.request,
      schoolName: requestData.client?.schoolName,
      schoolCode: requestData.client?.schoolCode,
      email: requestData.client?.email,
      phone: requestData.client?.phone,
      address: requestData.client?.address
    }

    return c.json({
      request: combinedRequest
    })

  } catch (error) {
    console.error('Admin get software request error:', error)
    return c.json({ error: 'Failed to fetch software request' }, 500)
  }
})

// Update software request status
app.put('/software-requests/:id', adminAuthMiddleware, requirePermission('requests:write'), async (c) => {
  try {
    const requestId = c.req.param('id')
    const { status, reviewNotes, rejectionReason } = await c.req.json()

    const admin = getCurrentAdmin(c)

    const updateData: any = {
      status,
      reviewedBy: admin?.id,
      updatedAt: new Date()
    }

    if (reviewNotes) updateData.reviewNotes = reviewNotes
    if (rejectionReason) updateData.rejectionReason = rejectionReason

    await db.update(softwareRequests)
      .set(updateData)
      .where(eq(softwareRequests.id, requestId))

    return c.json({
      message: 'Software request updated successfully',
      updatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin update software request error:', error)
    return c.json({ error: 'Failed to update software request' }, 500)
  }
})

// Approve software request and create client
app.post('/software-requests/:id/approve', adminAuthMiddleware, requirePermission('requests:write'), zValidator('json', z.object({
  monthlyAmount: z.number().min(100, 'Monthly amount must be at least ₹100'),
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly'),
  notes: z.string().optional(),
  // Billing configuration fields
  setupFee: z.number().min(0).default(0),
  gracePeriodDays: z.number().min(0).max(30).default(3),
  nextBillingDate: z.number().min(1).max(31).default(15),
  // Discount management fields
  discountPercentage: z.number().min(0).max(100).default(0),
  discountDurationMonths: z.number().min(0).max(24).default(0),
  discountStartDate: z.string().optional(),
  discountReason: z.string().optional(),
  // Operational expenses fields
  operationalExpenses: z.object({
    databaseCosts: z.number().min(0).default(0),
    websiteMaintenance: z.number().min(0).default(0),
    supportCosts: z.number().min(0).default(0),
    infrastructureCosts: z.number().min(0).default(0)
  }).optional()
})), async (c) => {
  try {
    const requestId = c.req.param('id')
    const approvalData = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get software request details with client information
    const [request] = await db.select({
      // Software request fields
      id: softwareRequests.id,
      clientId: softwareRequests.clientId,
      requestType: softwareRequests.requestType,
      studentCount: softwareRequests.studentCount,
      facultyCount: softwareRequests.facultyCount,
      completeAddress: softwareRequests.completeAddress,
      contactNumber: softwareRequests.contactNumber,
      primaryEmail: softwareRequests.primaryEmail,
      averageMonthlyFee: softwareRequests.averageMonthlyFee,
      calculatedAverageFee: softwareRequests.calculatedAverageFee,
      status: softwareRequests.status,
      termsAccepted: softwareRequests.termsAccepted,
      createdAt: softwareRequests.createdAt,
      updatedAt: softwareRequests.updatedAt,
      approvedAt: softwareRequests.approvedAt,
      activatedAt: softwareRequests.activatedAt,
      // Client fields
      schoolName: clients.schoolName,
      email: clients.email,
      phone: clients.phone
    })
    .from(softwareRequests)
    .innerJoin(clients, eq(softwareRequests.clientId, clients.id))
    .where(eq(softwareRequests.id, requestId))
    .limit(1)

    if (!request) {
      return c.json({ error: 'Software request not found' }, 404)
    }

    if (request.status !== 'pending') {
      return c.json({ error: 'Software request is not pending approval' }, 400)
    }

    // Check if client already exists for this request
    if (request.clientId) {
      // Update existing client with fee information from software request
      await db.update(clients)
        .set({
          averageMonthlyFee: request.averageMonthlyFee,
          classFee: request.averageMonthlyFee || request.calculatedAverageFee, // Legacy field
          actualStudentCount: request.studentCount,
          status: 'active',
          onboardingStatus: 'approved',
          updatedAt: new Date()
        })
        .where(eq(clients.id, request.clientId))

      // Update software request status
      await db.update(softwareRequests)
        .set({
          status: 'approved',
          reviewedBy: admin.id,
          reviewNotes: approvalData.notes,
          updatedAt: new Date()
        })
        .where(eq(softwareRequests.id, requestId))

      // Automatically create subscription after approval
      try {
        // Calculate price per student from monthly amount and student count
        const pricePerStudent = approvalData.monthlyAmount / request.studentCount

        // Validate price per student is within acceptable range
        if (pricePerStudent < 20 || pricePerStudent > 250) {
          console.warn(`Price per student (₹${pricePerStudent}) is outside recommended range (₹20-₹250)`)
        }

        // Check if subscription already exists for this client
        const existingSubscriptions = await db.select()
          .from(subscriptions)
          .where(and(
            eq(subscriptions.clientId, request.clientId),
            inArray(subscriptions.status, ['active', 'pending', 'suspended'])
          ))

        if (existingSubscriptions.length === 0) {
          // Create subscription data with discount and expense management
          const subscriptionData = {
            clientId: request.clientId,
            planName: 'Basic Plan',
            studentCount: request.studentCount,
            pricePerStudent: pricePerStudent,
            monthlyAmount: approvalData.monthlyAmount,
            billingCycle: approvalData.billingCycle,
            startDate: new Date().toISOString().split('T')[0],
            nextBillingDate: approvalData.nextBillingDate || 15,
            gracePeriodDays: approvalData.gracePeriodDays || 3,
            // Discount management fields
            discountPercentage: approvalData.discountPercentage || 0,
            discountDurationMonths: approvalData.discountDurationMonths || 0,
            discountStartDate: approvalData.discountStartDate || new Date().toISOString().split('T')[0],
            discountReason: approvalData.discountReason || '',
            setupFee: approvalData.setupFee || 0,
            notes: approvalData.notes || 'Created automatically during software request approval',
            operationalExpenses: approvalData.operationalExpenses || {
              databaseCosts: 0,
              websiteMaintenance: 0,
              supportCosts: 0,
              infrastructureCosts: 0
            }
          }

          // Calculate next billing date - Modified for immediate payment collection
          const currentDate = new Date()
          const nextBillingDate = new Date(currentDate)

          if (subscriptionData.billingCycle === 'monthly') {
            // Set to the due date of current month first
            nextBillingDate.setDate(subscriptionData.nextBillingDate)

            // If the due date has already passed this month, move to next month
            if (nextBillingDate <= currentDate) {
              nextBillingDate.setMonth(nextBillingDate.getMonth() + 1)
              nextBillingDate.setDate(subscriptionData.nextBillingDate)
            }

            console.log(`📅 [Subscription Creation] Billing date calculation:`, {
              currentDate: currentDate.toISOString().split('T')[0],
              dueDate: subscriptionData.nextBillingDate,
              calculatedBillingDate: nextBillingDate.toISOString().split('T')[0],
              isCurrentMonth: nextBillingDate.getMonth() === currentDate.getMonth()
            })
          } else {
            // For yearly billing, set to next year
            nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1)
            nextBillingDate.setDate(subscriptionData.nextBillingDate)
          }

          // Create Razorpay plan and subscription for automatic billing
          console.log('🔄 Creating Razorpay plan for automatic billing...')
          const razorpayPlanResult = await razorpayService.createPlan({
            interval: subscriptionData.billingCycle === 'yearly' ? 12 : 1,
            period: subscriptionData.billingCycle === 'yearly' ? 'monthly' : 'monthly',
            amount: Math.round(subscriptionData.monthlyAmount * 100), // Convert to paise
            currency: 'INR',
            description: `${subscriptionData.planName} - ${request.schoolName} (${subscriptionData.studentCount} students)`
          })

          if (!razorpayPlanResult.success) {
            console.error('❌ Failed to create Razorpay plan:', razorpayPlanResult.error)
            throw new Error(`Failed to create Razorpay plan: ${razorpayPlanResult.error}`)
          }

          const razorpayPlan = razorpayPlanResult.plan
          console.log(`✅ Razorpay plan created: ${razorpayPlan.id}`)

          // Check if Razorpay customer already exists for this email
          console.log('🔄 Checking for existing Razorpay customer...')
          let razorpayCustomer = null

          // Check for existing customer first
          const existingCustomerResult = await razorpayService.findCustomerByEmail(request.email)

          if (existingCustomerResult.success && existingCustomerResult.customer) {
            razorpayCustomer = existingCustomerResult.customer
            console.log(`✅ Found existing Razorpay customer: ${razorpayCustomer.id}`)
          }

          // If no existing customer found, create a new one
          if (!razorpayCustomer) {
            console.log('🔄 Creating new Razorpay customer...')
            const razorpayCustomerResult = await razorpayService.createCustomer({
              name: request.schoolName,
              email: request.email,
              contact: request.phone || undefined,
              failExisting: 0, // Don't fail if customer exists
              notes: {
                client_id: subscriptionData.clientId,
                school_name: request.schoolName,
                student_count: subscriptionData.studentCount.toString(),
                plan_name: subscriptionData.planName
              }
            })

            if (!razorpayCustomerResult.success) {
              // Check if error is about existing customer
              if (razorpayCustomerResult.error && razorpayCustomerResult.error.includes('already exists')) {
                console.log('Customer already exists, trying to fetch existing customer...')
                const existingCustomerResult = await razorpayService.findCustomerByEmail(request.email)

                if (existingCustomerResult.success && existingCustomerResult.customer) {
                  razorpayCustomer = existingCustomerResult.customer
                  console.log(`✅ Retrieved existing Razorpay customer: ${razorpayCustomer.id}`)
                } else {
                  console.error('❌ Failed to fetch existing customer:', existingCustomerResult.error)
                  throw new Error(`Failed to create or fetch customer: ${razorpayCustomerResult.error}`)
                }
              } else {
                console.error('❌ Failed to create Razorpay customer:', razorpayCustomerResult.error)
                throw new Error(`Failed to create customer: ${razorpayCustomerResult.error}`)
              }
            } else {
              razorpayCustomer = razorpayCustomerResult.customer
              console.log(`✅ Razorpay customer created: ${razorpayCustomer.id}`)
            }
          }

          // Create Razorpay subscription
          console.log('🔄 Creating Razorpay subscription...')
          const subscriptionStartDate = new Date()
          subscriptionStartDate.setDate(subscriptionData.nextBillingDate) // Start from next billing date
          if (subscriptionStartDate <= new Date()) {
            subscriptionStartDate.setMonth(subscriptionStartDate.getMonth() + 1) // If date has passed, start next month
          }

          const razorpaySubscriptionResult = await razorpayService.createSubscription({
            planId: razorpayPlan.id,
            customerEmail: request.email,
            customerName: request.schoolName,
            customerContact: request.phone || undefined,
            totalCount: subscriptionData.billingCycle === 'yearly' ? 5 : 60, // 5 years or 60 months
            startAt: Math.floor(subscriptionStartDate.getTime() / 1000), // Unix timestamp
            customerNotify: 1,
            notes: {
              client_id: subscriptionData.clientId,
              school_name: request.schoolName,
              student_count: subscriptionData.studentCount.toString(),
              plan_name: subscriptionData.planName,
              created_by: admin.id,
              schopio_subscription: 'true'
            }
          })

          if (!razorpaySubscriptionResult.success) {
            console.error('❌ Failed to create Razorpay subscription:', razorpaySubscriptionResult.error)
            throw new Error(`Failed to create subscription: ${razorpaySubscriptionResult.error}`)
          }

          const razorpaySubscription = razorpaySubscriptionResult.subscription
          console.log(`✅ Razorpay subscription created: ${razorpaySubscription.id}`)

          // Create subscription record with Razorpay integration in billingSubscriptions table
          const [newSubscription] = await db.insert(billingSubscriptions).values({
            clientId: subscriptionData.clientId,
            studentCount: subscriptionData.studentCount,
            pricePerStudent: subscriptionData.pricePerStudent.toString(),
            monthlyAmount: subscriptionData.monthlyAmount.toString(),
            billingCycle: subscriptionData.billingCycle,
            currentPeriodStart: subscriptionData.startDate,
            currentPeriodEnd: nextBillingDate.toISOString().split('T')[0],
            nextBillingDate: nextBillingDate.toISOString().split('T')[0],
            gracePeriodDays: subscriptionData.gracePeriodDays,
            // Discount fields
            hasActiveDiscount: subscriptionData.discountPercentage > 0,
            currentDiscountPercentage: subscriptionData.discountPercentage > 0 ? subscriptionData.discountPercentage.toString() : null,
            discountEndDate: subscriptionData.discountPercentage > 0 && subscriptionData.discountDurationMonths > 0 ?
              (() => {
                const endDate = new Date(subscriptionData.discountStartDate);
                endDate.setMonth(endDate.getMonth() + subscriptionData.discountDurationMonths);
                return endDate.toISOString().split('T')[0];
              })() : null,
            status: 'active',
            createdBy: admin.id,
            // Add Razorpay integration fields
            razorpaySubscriptionId: razorpaySubscription.id,
            razorpayCustomerId: razorpayCustomer.id
          }).returning()

          console.log(`Subscription created automatically for client ${request.clientId}:`, newSubscription.id)

          // Create discount record if discount is applied
          if (subscriptionData.discountPercentage > 0 && subscriptionData.discountDurationMonths > 0) {
            const discountEndDate = new Date(subscriptionData.discountStartDate);
            discountEndDate.setMonth(discountEndDate.getMonth() + subscriptionData.discountDurationMonths);

            await db.insert(subscriptionDiscounts).values({
              subscriptionId: newSubscription.id,
              discountPercentage: subscriptionData.discountPercentage.toString(),
              discountDurationMonths: subscriptionData.discountDurationMonths,
              startDate: subscriptionData.discountStartDate,
              endDate: discountEndDate.toISOString().split('T')[0],
              remainingMonths: subscriptionData.discountDurationMonths,
              isActive: true,
              reason: subscriptionData.discountReason || 'Applied during software request approval',
              createdBy: admin.id
            });

            console.log(`✅ Discount created: ${subscriptionData.discountPercentage}% for ${subscriptionData.discountDurationMonths} months`);
          }

          // Create operational expenses record if expenses are provided
          if (subscriptionData.operationalExpenses) {
            const totalExpenses = Object.values(subscriptionData.operationalExpenses).reduce((sum, cost) => sum + cost, 0);

            if (totalExpenses > 0) {
              await db.insert(subscriptionExpenses).values({
                subscriptionId: newSubscription.id,
                monthlyOperationalCost: totalExpenses.toString(),
                expenseBreakdown: subscriptionData.operationalExpenses, // Store the breakdown
                description: 'Operational expenses set during subscription creation',
                category: 'operational',
                effectiveFrom: subscriptionData.startDate,
                isActive: true,
                createdBy: admin.id
              });

              console.log(`✅ Operational expenses created: ₹${totalExpenses}/month with breakdown:`, subscriptionData.operationalExpenses);
            }
          }

          // ===== CRITICAL FIX: GENERATE FIRST INVOICE =====
          // This fixes the "payment status always pending" issue
          try {
            console.log('📋 Generating first invoice for new subscription...')

            // Calculate invoice details
            const invoiceAmount = parseFloat(subscriptionData.monthlyAmount.toString())
            const setupFeeAmount = parseFloat((subscriptionData.setupFee || 0).toString())
            const totalInvoiceAmount = invoiceAmount + setupFeeAmount

            // Generate invoice number
            const invoiceNumber = `INV-${Date.now()}-${newSubscription.id.slice(-6)}`

            // Set invoice dates
            const issuedDate = new Date().toISOString().split('T')[0]
            const dueDate = new Date()
            dueDate.setDate(dueDate.getDate() + (subscriptionData.gracePeriodDays || 3))
            const dueDateStr = dueDate.toISOString().split('T')[0]

            // Create the first invoice
            const [firstInvoice] = await db.insert(billingInvoices).values({
              subscriptionId: newSubscription.id,
              clientId: request.clientId,
              invoiceNumber,
              subtotal: invoiceAmount.toString(),
              taxAmount: '0', // No tax for now
              totalAmount: totalInvoiceAmount.toString(),
              status: 'open', // Use 'open' instead of 'pending'
              issuedDate,
              dueDate: dueDateStr,
              periodStart: subscriptionData.startDate,
              periodEnd: nextBillingDate.toISOString().split('T')[0],
              notes: `First invoice for ${request.schoolName} - ${subscriptionData.billingCycle} subscription`,
              discountAmount: '0' // Apply discounts later if needed
            }).returning()

            console.log(`✅ First invoice created: ${invoiceNumber} for ₹${totalInvoiceAmount}`)
            console.log(`   Due date: ${dueDateStr}`)
            console.log(`   This fixes the payment status issue!`)

          } catch (invoiceError) {
            console.error('⚠️  Failed to create first invoice:', invoiceError)
            // Don't fail the subscription creation if invoice fails
          }

          return c.json({
            message: 'Software request approved, client updated, and subscription created successfully',
            clientId: request.clientId,
            subscriptionId: newSubscription.id,
            approvedBy: admin.name,
            subscriptionDetails: {
              monthlyAmount: subscriptionData.monthlyAmount,
              billingCycle: subscriptionData.billingCycle,
              studentCount: subscriptionData.studentCount,
              pricePerStudent: subscriptionData.pricePerStudent
            }
          })
        } else {
          console.log(`Client ${request.clientId} already has an existing subscription, skipping creation`)
          return c.json({
            message: 'Software request approved and client updated successfully (subscription already exists)',
            clientId: request.clientId,
            approvedBy: admin.name,
            existingSubscription: existingSubscriptions[0].id
          })
        }
      } catch (subscriptionError) {
        console.error('Failed to create subscription during approval:', subscriptionError)
        // Don't fail the approval if subscription creation fails
        return c.json({
          message: 'Software request approved and client updated successfully (subscription creation failed)',
          clientId: request.clientId,
          approvedBy: admin.name,
          warning: 'Subscription creation failed - please create manually',
          error: subscriptionError instanceof Error ? subscriptionError.message : 'Unknown error'
        })
      }
    } else {
      return c.json({ error: 'No client associated with this software request' }, 400)
    }

  } catch (error) {
    console.error('Admin approve software request error:', error)
    return c.json({ error: 'Failed to approve software request' }, 500)
  }
})

// Upgrade demo request to production
app.post('/software-requests/:id/upgrade', adminAuthMiddleware, requirePermission('requests:write'), async (c) => {
  try {
    const requestId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get software request details
    const [request] = await db.select().from(softwareRequests).where(eq(softwareRequests.id, requestId)).limit(1)

    if (!request) {
      return c.json({ error: 'Software request not found' }, 404)
    }

    if (request.requestType !== 'demo') {
      return c.json({ error: 'Only demo requests can be upgraded to production' }, 400)
    }

    if (request.status !== 'activated') {
      return c.json({ error: 'Demo request must be activated before upgrading' }, 400)
    }

    // Update request type to production and reset status to pending
    await db.update(softwareRequests)
      .set({
        requestType: 'production',
        status: 'pending',
        reviewedBy: admin.id,
        reviewNotes: 'Upgraded from demo to production request',
        updatedAt: new Date()
      })
      .where(eq(softwareRequests.id, requestId))

    return c.json({
      message: 'Demo request upgraded to production successfully',
      upgradedBy: admin.name
    })

  } catch (error) {
    console.error('Admin upgrade software request error:', error)
    return c.json({ error: 'Failed to upgrade software request' }, 500)
  }
})

// ===== ADMIN USER MANAGEMENT =====

// Get all admin users (super admin only)
app.get('/admin-users', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const { search, status, page = '1', limit = '10' } = c.req.query()
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    const conditions = []
    if (search) {
      conditions.push(
        sql`${adminUsers.name} ILIKE ${`%${search}%`} OR ${adminUsers.email} ILIKE ${`%${search}%`}`
      )
    }
    if (status === 'active') {
      conditions.push(eq(adminUsers.isActive, true))
    } else if (status === 'inactive') {
      conditions.push(eq(adminUsers.isActive, false))
    }

    const adminUsersData = conditions.length > 0
      ? await db.select({
          id: adminUsers.id,
          email: adminUsers.email,
          name: adminUsers.name,
          role: adminUsers.role,
          permissions: adminUsers.permissions,
          isActive: adminUsers.isActive,
          lastLogin: adminUsers.lastLogin,
          createdAt: adminUsers.createdAt
        }).from(adminUsers)
        .where(and(...conditions))
        .orderBy(desc(adminUsers.createdAt))
        .limit(limitNum)
        .offset(offset)
      : await db.select({
          id: adminUsers.id,
          email: adminUsers.email,
          name: adminUsers.name,
          role: adminUsers.role,
          permissions: adminUsers.permissions,
          isActive: adminUsers.isActive,
          lastLogin: adminUsers.lastLogin,
          createdAt: adminUsers.createdAt
        }).from(adminUsers)
        .orderBy(desc(adminUsers.createdAt))
        .limit(limitNum)
        .offset(offset)

    // Get total count
    const [totalCount] = conditions.length > 0
      ? await db.select({ count: count() }).from(adminUsers).where(and(...conditions))
      : await db.select({ count: count() }).from(adminUsers)

    return c.json({
      adminUsers: adminUsersData,
      roles: ADMIN_ROLES,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin users fetch error:', error)
    return c.json({ error: 'Failed to fetch admin users' }, 500)
  }
})

// Create new admin user (super admin only)
app.post('/admin-users', adminAuthMiddleware, requireAdminRole(['super_admin']), zValidator('json', z.object({
  email: z.string().email(),
  name: z.string().min(2),
  role: z.enum(['super_admin', 'sales', 'support', 'billing']),
  password: z.string().min(8)
})), async (c) => {
  try {
    const { email, name, role, password } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    // Check if email already exists
    const [existingAdmin] = await db.select().from(adminUsers).where(eq(adminUsers.email, email)).limit(1)
    if (existingAdmin) {
      return c.json({ error: 'Email already exists' }, 400)
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12)

    // Get default permissions for role
    const permissions = ADMIN_ROLES[role as keyof typeof ADMIN_ROLES]?.permissions || []

    // Create admin user
    const [newAdmin] = await db.insert(adminUsers).values({
      email,
      name,
      role,
      passwordHash,
      permissions,
      isActive: true
    }).returning()

    return c.json({
      message: 'Admin user created successfully',
      adminUser: {
        id: newAdmin.id,
        email: newAdmin.email,
        name: newAdmin.name,
        role: newAdmin.role,
        permissions: newAdmin.permissions
      },
      createdBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user creation error:', error)
    return c.json({ error: 'Failed to create admin user' }, 500)
  }
})

// Update admin user (super admin only)
app.put('/admin-users/:id', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const { name, role, isActive, permissions } = await c.req.json()
    const admin = getCurrentAdmin(c)

    const updateData: any = {}
    if (name) updateData.name = name
    if (role) {
      updateData.role = role
      // Update permissions based on role if not explicitly provided
      if (!permissions) {
        updateData.permissions = ADMIN_ROLES[role as keyof typeof ADMIN_ROLES]?.permissions || []
      }
    }
    if (typeof isActive === 'boolean') updateData.isActive = isActive
    if (permissions) updateData.permissions = permissions

    await db.update(adminUsers)
      .set(updateData)
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user updated successfully',
      updatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user update error:', error)
    return c.json({ error: 'Failed to update admin user' }, 500)
  }
})

// Change admin password
app.put('/change-password', adminAuthMiddleware, zValidator('json', z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
})), async (c) => {
  try {
    const { currentPassword, newPassword, confirmPassword } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    // Validate password confirmation
    if (newPassword !== confirmPassword) {
      return c.json({ error: 'New password and confirmation do not match' }, 400)
    }

    // Get current admin data from database
    const [currentAdmin] = await db.select().from(adminUsers).where(eq(adminUsers.id, admin.id)).limit(1)

    if (!currentAdmin) {
      return c.json({ error: 'Admin user not found' }, 404)
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentAdmin.passwordHash)
    if (!isCurrentPasswordValid) {
      return c.json({ error: 'Current password is incorrect' }, 400)
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, 12)

    // Update password in database
    await db.update(adminUsers)
      .set({ passwordHash: newPasswordHash })
      .where(eq(adminUsers.id, admin.id))

    return c.json({
      message: 'Password changed successfully',
      changedBy: admin.name
    })

  } catch (error) {
    console.error('Admin password change error:', error)
    return c.json({ error: 'Failed to change password' }, 500)
  }
})

// Deactivate admin user (super admin only)
app.put('/admin-users/:id/deactivate', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    // Prevent self-deactivation
    if (adminUserId === admin?.id) {
      return c.json({ error: 'Cannot deactivate your own account' }, 400)
    }

    await db.update(adminUsers)
      .set({ isActive: false })
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user deactivated successfully',
      deactivatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user deactivation error:', error)
    return c.json({ error: 'Failed to deactivate admin user' }, 500)
  }
})

// Reactivate admin user (super admin only)
app.put('/admin-users/:id/activate', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    await db.update(adminUsers)
      .set({ isActive: true })
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user activated successfully',
      activatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user activation error:', error)
    return c.json({ error: 'Failed to activate admin user' }, 500)
  }
})

// Reset admin user password (super admin only)
app.put('/admin-users/:id/reset-password', adminAuthMiddleware, requireAdminRole(['super_admin']), zValidator('json', z.object({
  newPassword: z.string().min(8, 'New password must be at least 8 characters')
})), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const { newPassword } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    // Hash new password
    const passwordHash = await bcrypt.hash(newPassword, 12)

    // Update password in database
    await db.update(adminUsers)
      .set({ passwordHash })
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user password reset successfully',
      resetBy: admin?.name
    })

  } catch (error) {
    console.error('Admin password reset error:', error)
    return c.json({ error: 'Failed to reset admin user password' }, 500)
  }
})

// ===== MONTHLY BILLING AUTOMATION =====

// Update subscription monthly amount
app.put('/subscriptions/:id/amount', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', updateSubscriptionAmountSchema), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { monthlyAmount, effectiveDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Check if subscription exists
    const [subscription] = await db.select().from(subscriptions).where(eq(subscriptions.id, subscriptionId)).limit(1)
    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Update subscription amount
    await db.update(subscriptions)
      .set({
        monthlyAmount: monthlyAmount.toString(),
        updatedAt: new Date()
      })
      .where(eq(subscriptions.id, subscriptionId))

    return c.json({
      message: 'Subscription amount updated successfully',
      subscriptionId,
      newAmount: monthlyAmount,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update subscription amount error:', error)
    return c.json({ error: 'Failed to update subscription amount' }, 500)
  }
})

// Generate billing cycles for all active subscriptions for a specific month
app.post('/billing/generate-monthly', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', bulkGenerateBillingSchema), async (c) => {
  try {
    const { month, year, nextBillingDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get all active subscriptions
    const activeSubscriptions = await db.select({
      id: billingSubscriptions.id,
      clientId: billingSubscriptions.clientId,
      studentCount: billingSubscriptions.studentCount,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      client: {
        schoolName: clients.schoolName,
        email: clients.email
      }
    })
    .from(billingSubscriptions)
    .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
    .where(eq(billingSubscriptions.status, 'active'))

    if (activeSubscriptions.length === 0) {
      return c.json({ message: 'No active subscriptions found' })
    }

    // Calculate cycle dates
    const cycleStart = new Date(year, month - 1, 1).toISOString().split('T')[0]
    const cycleEnd = new Date(year, month, 0).toISOString().split('T')[0] // Last day of month
    const defaultDueDate = nextBillingDate || new Date(year, month, 5).toISOString().split('T')[0] // 5th of next month

    const generatedCycles = []
    const generatedInvoices = []

    for (const subscription of activeSubscriptions) {
      // Check if billing cycle already exists for this month
      const [existingCycle] = await db.select()
        .from(billingSubscriptions)
        .where(and(
          eq(billingSubscriptions.id, subscription.id),
          eq(billingSubscriptions.currentPeriodStart, cycleStart)
        ))
        .limit(1)

      if (existingCycle) {
        continue // Skip if already exists
      }

      const baseAmount = parseFloat(subscription.monthlyAmount)
      const taxAmount = baseAmount * 0.18 // 18% GST
      const totalAmount = baseAmount + taxAmount

      // Create billing cycle
      const [newCycle] = await db.insert(billingSubscriptions).values({
        clientId: subscription.clientId,
        studentCount: subscription.studentCount,
        pricePerStudent: (baseAmount / subscription.studentCount).toString(),
        currentPeriodStart: cycleStart,
        currentPeriodEnd: cycleEnd,
        nextBillingDate: defaultDueDate,
        monthlyAmount: totalAmount.toString(),
        status: 'pending'
      }).returning()

      generatedCycles.push(newCycle)

      // Generate invoice number
      const invoiceNumber = `INV-${year}${month.toString().padStart(2, '0')}-${subscription.clientId!.slice(-6).toUpperCase()}`

      // Create invoice
      const [newInvoice] = await db.insert(billingInvoices).values({
        subscriptionId: newCycle.id,
        clientId: subscription.clientId,
        invoiceNumber,
        subtotal: baseAmount.toString(),
        taxAmount: taxAmount.toString(),
        totalAmount: totalAmount.toString(),
        status: 'sent',
        issuedDate: new Date().toISOString().split('T')[0],
        dueDate: defaultDueDate,
        periodStart: cycleStart,
        periodEnd: cycleEnd
      }).returning()

      generatedInvoices.push({
        ...newInvoice,
        clientName: subscription.client?.schoolName
      })
    }

    return c.json({
      message: `Generated ${generatedCycles.length} billing cycles and ${generatedInvoices.length} invoices for ${month}/${year}`,
      cycles: generatedCycles.length,
      invoices: generatedInvoices.length,
      generatedBy: admin.name,
      details: generatedInvoices
    })

  } catch (error) {
    console.error('Generate monthly billing error:', error)
    return c.json({ error: 'Failed to generate monthly billing' }, 500)
  }
})

// Get client subscription and billing details
app.get('/clients/:id/billing', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const clientId = c.req.param('id')

    // Get client with subscription details
    const [clientData] = await db.select({
      client: clients,
      subscription: billingSubscriptions
    })
    .from(clients)
    .leftJoin(billingSubscriptions, and(
      eq(billingSubscriptions.clientId, clients.id),
      eq(billingSubscriptions.status, 'active')
    ))
    .where(eq(clients.id, clientId))
    .limit(1)

    if (!clientData) {
      return c.json({ error: 'Client not found' }, 404)
    }

    // Get recent billing cycles
    const recentCycles = clientData.subscription ? await db.select()
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, clientData.subscription.id))
      .orderBy(desc(billingSubscriptions.createdAt))
      .limit(6) : []

    // Get recent invoices
    const recentInvoices = await db.select()
      .from(billingInvoices)
      .where(eq(billingInvoices.clientId, clientId))
      .orderBy(desc(billingInvoices.createdAt))
      .limit(10)

    // Get recent payments
    const recentPayments = await db.select()
      .from(billingPayments)
      .where(eq(billingPayments.clientId, clientId))
      .orderBy(desc(billingPayments.createdAt))
      .limit(10)

    // Calculate financial summary
    const totalPaid = recentPayments
      .filter(p => p.status === 'success')
      .reduce((sum, p) => sum + parseFloat(p.amount), 0)

    const totalOutstanding = recentInvoices
      .filter(i => i.status && ['sent', 'overdue'].includes(i.status))
      .reduce((sum, i) => sum + parseFloat(i.totalAmount), 0)

    return c.json({
      client: clientData.client,
      subscription: clientData.subscription,
      billingCycles: recentCycles,
      invoices: recentInvoices,
      payments: recentPayments,
      summary: {
        monthlyAmount: clientData.subscription?.monthlyAmount || '0',
        totalPaid: totalPaid.toString(),
        totalOutstanding: totalOutstanding.toString(),
        lastPaymentDate: recentPayments.find(p => p.status === 'success')?.createdAt || null
      }
    })

  } catch (error) {
    console.error('Get client billing error:', error)
    return c.json({ error: 'Failed to fetch client billing details' }, 500)
  }
})

// Mark invoice as paid and create payment record
app.post('/invoices/:id/mark-paid', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const invoiceId = c.req.param('id')
    const { paymentMethod, transactionId, notes } = await c.req.json()
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get invoice details
    const [invoice] = await db.select().from(billingInvoices).where(eq(billingInvoices.id, invoiceId)).limit(1)
    if (!invoice) {
      return c.json({ error: 'Invoice not found' }, 404)
    }

    if (invoice.status === 'paid') {
      return c.json({ error: 'Invoice is already paid' }, 400)
    }

    // Create payment record
    const [payment] = await db.insert(billingPayments).values({
      invoiceId: invoice.id,
      subscriptionId: invoice.subscriptionId,
      clientId: invoice.clientId,
      amount: invoice.totalAmount,
      currency: 'INR',
      status: 'succeeded',
      paymentMethod: paymentMethod || 'manual',
      razorpayPaymentId: transactionId
    }).returning()

    // Update invoice status
    await db.update(billingInvoices)
      .set({
        status: 'paid',
        paidDate: new Date().toISOString().split('T')[0]
      })
      .where(eq(billingInvoices.id, invoiceId))

    return c.json({
      message: 'Invoice marked as paid successfully',
      payment,
      markedBy: admin.name
    })

  } catch (error) {
    console.error('Mark invoice paid error:', error)
    return c.json({ error: 'Failed to mark invoice as paid' }, 500)
  }
})

// Get billing dashboard analytics
app.get('/billing/dashboard', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    // Get current month stats
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1
    const currentYear = currentDate.getFullYear()

    // Total active subscriptions
    const [activeSubscriptionsCount] = await db
      .select({ count: count() })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))

    // Monthly revenue (current month)
    const monthlyRevenue = await db
      .select({
        monthlyAmount: sql<string>`SUM(${billingPayments.amount})`
      })
      .from(billingPayments)
      .where(and(
        eq(billingPayments.status, 'success'),
        sql`EXTRACT(MONTH FROM ${billingPayments.createdAt}) = ${currentMonth}`,
        sql`EXTRACT(YEAR FROM ${billingPayments.createdAt}) = ${currentYear}`
      ))

    // Outstanding invoices
    const outstandingInvoices = await db
      .select({
        count: count(),
        monthlyAmount: sql<string>`SUM(${billingInvoices.totalAmount})`
      })
      .from(billingInvoices)
      .where(sql`${billingInvoices.status} IN ('sent', 'overdue')`)

    // Overdue invoices
    const overdueInvoices = await db
      .select({
        count: count(),
        monthlyAmount: sql<string>`SUM(${billingInvoices.totalAmount})`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'overdue'),
        sql`${billingInvoices.dueDate} < CURRENT_DATE`
      ))

    // Recent payments
    const recentPayments = await db
      .select({
        payment: billingPayments,
        client: {
          schoolName: clients.schoolName
        },
        invoice: {
          invoiceNumber: billingInvoices.invoiceNumber
        }
      })
      .from(billingPayments)
      .leftJoin(clients, eq(billingPayments.clientId, clients.id))
      .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
      .where(eq(billingPayments.status, 'success'))
      .orderBy(desc(billingPayments.createdAt))
      .limit(10)

    return c.json({
      activeSubscriptions: activeSubscriptionsCount.count,
      monthlyRevenue: parseFloat(monthlyRevenue[0]?.monthlyAmount || '0'),
      outstanding: {
        count: outstandingInvoices[0]?.count || 0,
        monthlyAmount: parseFloat(outstandingInvoices[0]?.monthlyAmount || '0')
      },
      overdue: {
        count: overdueInvoices[0]?.count || 0,
        monthlyAmount: parseFloat(overdueInvoices[0]?.monthlyAmount || '0')
      },
      recentPayments
    })

  } catch (error) {
    console.error('Billing dashboard error:', error)
    return c.json({ error: 'Failed to fetch billing dashboard' }, 500)
  }
})

// Get all subscriptions with client details (updated for admin subscriptions management)
app.get('/subscriptions', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { status, search, billingCycle, limit = '50', page = '1' } = c.req.query()
    const limitNum = parseInt(limit)
    const offset = (parseInt(page) - 1) * limitNum

    let query = db
      .select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        studentCount: billingSubscriptions.studentCount,
        pricePerStudent: billingSubscriptions.pricePerStudent,
        monthlyAmount: billingSubscriptions.monthlyAmount,
        billingCycle: billingSubscriptions.billingCycle,
        status: billingSubscriptions.status,
        currentPeriodStart: billingSubscriptions.currentPeriodStart,
        currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
        nextBillingDate: billingSubscriptions.nextBillingDate,
        gracePeriodDays: billingSubscriptions.gracePeriodDays,
        penaltyRate: billingSubscriptions.penaltyRate,
        autoRenew: billingSubscriptions.autoRenew,
        createdAt: billingSubscriptions.createdAt,
        updatedAt: billingSubscriptions.updatedAt,
        client: {
          id: clients.id,
          schoolName: clients.schoolName,
          email: clients.email,
          phone: clients.phone,
          status: clients.status
        }
      })
      .from(billingSubscriptions)
      .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))

    // Apply filters
    const conditions = []
    if (status) {
      conditions.push(eq(billingSubscriptions.status, status))
    }
    if (billingCycle) {
      conditions.push(eq(billingSubscriptions.billingCycle, billingCycle))
    }
    if (search) {
      conditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${clients.email} ILIKE ${`%${search}%`}`
      )
    }

    const subscriptionsList = conditions.length > 0
      ? await query.where(and(...conditions))
          .orderBy(desc(billingSubscriptions.createdAt))
          .limit(limitNum)
          .offset(offset)
      : await query
          .orderBy(desc(billingSubscriptions.createdAt))
          .limit(limitNum)
          .offset(offset)

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(billingSubscriptions)
      .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))

    return c.json({
      subscriptions: subscriptionsList,
      pagination: {
        total: totalCount.count,
        page: parseInt(page),
        limit: limitNum,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Get subscriptions error:', error)
    return c.json({ error: 'Failed to fetch subscriptions' }, 500)
  }
})



// Update subscription status (suspend/reactivate)
app.put('/subscriptions/:id/status', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { status, reason } = await c.req.json()
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    if (!['active', 'suspended', 'cancelled'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400)
    }

    // Update subscription status
    await db.update(subscriptions)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(subscriptions.id, subscriptionId))

    return c.json({
      message: `Subscription ${status} successfully`,
      reason,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update subscription status error:', error)
    return c.json({ error: 'Failed to update subscription status' }, 500)
  }
})

// ===== PARTNER MANAGEMENT =====

// Validation schemas for partner management
const createPartnerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  companyName: z.string().optional(),
  phone: z.string().min(10, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  bankAccountNumber: z.string().optional(),
  bankIfscCode: z.string().optional(),
  bankAccountHolderName: z.string().optional(),
  profitSharePercentage: z.number().min(35).max(50).optional()
})

const updatePartnerSchema = z.object({
  name: z.string().min(1).optional(),
  companyName: z.string().optional(),
  phone: z.string().min(10).optional(),
  address: z.string().optional(),
  bankAccountNumber: z.string().optional(),
  bankIfscCode: z.string().optional(),
  bankAccountHolderName: z.string().optional(),
  profitSharePercentage: z.number().min(35).max(50).optional(),
  isActive: z.boolean().optional()
})

const createExpenseSchema = z.object({
  categoryName: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  amountPerSchool: z.number().positive().optional(),
  isPercentage: z.boolean().default(false),
  percentageValue: z.number().min(0).max(100).optional(),
  appliesTo: z.enum(['all', 'specific_partners', 'specific_schools']).default('all')
})

// Generate unique partner code
function generatePartnerCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// Generate unique referral code
function generateReferralCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// Create new partner (Admin only)
app.post('/partners', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales']), zValidator('json', createPartnerSchema), async (c) => {
  try {
    const partnerData = c.req.valid('json')
    const currentAdmin = getCurrentAdmin(c)

    // Hash password
    const passwordHash = await bcrypt.hash(partnerData.password, 12)

    // Generate unique partner code
    let partnerCode = generatePartnerCode()
    let codeExists = true
    while (codeExists) {
      const [existing] = await db.select().from(partners).where(eq(partners.partnerCode, partnerCode)).limit(1)
      if (!existing) {
        codeExists = false
      } else {
        partnerCode = generatePartnerCode()
      }
    }

    // Create partner
    const [newPartner] = await db.insert(partners).values({
      partnerCode,
      email: partnerData.email,
      passwordHash,
      name: partnerData.name,
      companyName: partnerData.companyName,
      phone: partnerData.phone,
      address: partnerData.address,
      bankAccountNumber: partnerData.bankAccountNumber,
      bankIfscCode: partnerData.bankIfscCode,
      bankAccountHolderName: partnerData.bankAccountHolderName,
      profitSharePercentage: partnerData.profitSharePercentage ? partnerData.profitSharePercentage.toString() : null,
      createdBy: currentAdmin!.id
    }).returning()

    // Generate referral code
    let referralCode = generateReferralCode()
    let refCodeExists = true
    while (refCodeExists) {
      const [existing] = await db.select().from(referralCodes).where(eq(referralCodes.code, referralCode)).limit(1)
      if (!existing) {
        refCodeExists = false
      } else {
        referralCode = generateReferralCode()
      }
    }

    // Create referral code for partner
    await db.insert(referralCodes).values({
      partnerId: newPartner.id,
      code: referralCode
    })

    return c.json({
      message: 'Partner created successfully',
      partner: {
        id: newPartner.id,
        partnerCode: newPartner.partnerCode,
        name: newPartner.name,
        email: newPartner.email,
        referralCode
      }
    }, 201)

  } catch (error) {
    console.error('Create partner error:', error)
    if (error && typeof error === 'object' && 'code' in error && error.code === '23505') { // Unique constraint violation
      return c.json({ error: 'Email already exists' }, 409)
    }
    return c.json({ error: 'Failed to create partner' }, 500)
  }
})

// Get all partners with filtering and pagination
app.get('/partners', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales', 'support']), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const search = c.req.query('search') || ''
    const status = c.req.query('status') // 'active', 'inactive'
    const sortBy = c.req.query('sortBy') || 'createdAt'
    const sortOrder = c.req.query('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // Build query conditions
    let whereConditions = []
    if (search) {
      whereConditions.push(
        sql`(${partners.name} ILIKE ${`%${search}%`} OR ${partners.email} ILIKE ${`%${search}%`} OR ${partners.companyName} ILIKE ${`%${search}%`})`
      )
    }
    if (status === 'active') {
      whereConditions.push(eq(partners.isActive, true))
    } else if (status === 'inactive') {
      whereConditions.push(eq(partners.isActive, false))
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined

    // Get partners with referral codes
    const partnersData = await db
      .select({
        id: partners.id,
        partnerCode: partners.partnerCode,
        name: partners.name,
        email: partners.email,
        companyName: partners.companyName,
        phone: partners.phone,
        profitSharePercentage: partners.profitSharePercentage,
        isActive: partners.isActive,
        emailVerified: partners.emailVerified,
        lastLogin: partners.lastLogin,
        createdAt: partners.createdAt,
        referralCode: referralCodes.code
      })
      .from(partners)
      .leftJoin(referralCodes, and(eq(referralCodes.partnerId, partners.id), eq(referralCodes.isActive, true)))
      .where(whereClause)
      .orderBy(sortOrder === 'desc' ? desc(partners.createdAt) : partners.createdAt)
      .limit(limit)
      .offset(offset)

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(partners)
      .where(whereClause)

    return c.json({
      partners: partnersData,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partners error:', error)
    return c.json({ error: 'Failed to fetch partners' }, 500)
  }
})

// Get partner by ID with detailed information
app.get('/partners/:id', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales', 'support']), async (c) => {
  try {
    const partnerId = c.req.param('id')

    const [partner] = await db
      .select({
        id: partners.id,
        partnerCode: partners.partnerCode,
        name: partners.name,
        email: partners.email,
        companyName: partners.companyName,
        phone: partners.phone,
        address: partners.address,
        bankAccountNumber: partners.bankAccountNumber,
        bankIfscCode: partners.bankIfscCode,
        bankAccountHolderName: partners.bankAccountHolderName,
        profitSharePercentage: partners.profitSharePercentage,
        isActive: partners.isActive,
        emailVerified: partners.emailVerified,
        lastLogin: partners.lastLogin,
        createdAt: partners.createdAt,
        referralCode: referralCodes.code
      })
      .from(partners)
      .leftJoin(referralCodes, and(eq(referralCodes.partnerId, partners.id), eq(referralCodes.isActive, true)))
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!partner) {
      return c.json({ error: 'Partner not found' }, 404)
    }

    // Get partner statistics
    const [totalReferrals] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(eq(schoolReferrals.partnerId, partnerId))

    const [totalEarnings] = await db
      .select({
        total: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        available: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partnerId))

    return c.json({
      partner,
      statistics: {
        totalReferrals: totalReferrals.count,
        totalEarnings: totalEarnings.total || 0,
        availableBalance: totalEarnings.available || 0
      }
    })

  } catch (error) {
    console.error('Get partner error:', error)
    return c.json({ error: 'Failed to fetch partner' }, 500)
  }
})

// Update partner
app.put('/partners/:id', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales']), zValidator('json', updatePartnerSchema), async (c) => {
  try {
    const partnerId = c.req.param('id')
    const updateData = c.req.valid('json')

    // Convert numeric fields to strings for decimal columns
    const processedUpdateData = {
      ...updateData,
      profitSharePercentage: updateData.profitSharePercentage !== undefined
        ? (updateData.profitSharePercentage ? updateData.profitSharePercentage.toString() : null)
        : undefined
    }

    const [updatedPartner] = await db
      .update(partners)
      .set({
        ...processedUpdateData,
        updatedAt: new Date()
      })
      .where(eq(partners.id, partnerId))
      .returning()

    if (!updatedPartner) {
      return c.json({ error: 'Partner not found' }, 404)
    }

    return c.json({
      message: 'Partner updated successfully',
      partner: updatedPartner
    })

  } catch (error) {
    console.error('Update partner error:', error)
    return c.json({ error: 'Failed to update partner' }, 500)
  }
})

// Get schools referred by a partner
app.get('/partners/:id/schools', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales', 'support']), async (c) => {
  try {
    const partnerId = c.req.param('id')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const offset = (page - 1) * limit

    const schools = await db
      .select({
        id: clients.id,
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        email: clients.email,
        phone: clients.phone,
        actualStudentCount: clients.actualStudentCount,
        status: clients.status,
        onboardingStatus: clients.onboardingStatus,
        referredAt: schoolReferrals.referredAt,
        referralCode: referralCodes.code
      })
      .from(schoolReferrals)
      .innerJoin(clients, eq(clients.id, schoolReferrals.clientId))
      .innerJoin(referralCodes, eq(referralCodes.id, schoolReferrals.referralCodeId))
      .where(eq(schoolReferrals.partnerId, partnerId))
      .orderBy(desc(schoolReferrals.referredAt))
      .limit(limit)
      .offset(offset)

    const [totalCount] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(eq(schoolReferrals.partnerId, partnerId))

    return c.json({
      schools,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partner schools error:', error)
    return c.json({ error: 'Failed to fetch partner schools' }, 500)
  }
})

// Create operational expense
app.post('/expenses', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', createExpenseSchema), async (c) => {
  try {
    const expenseData = c.req.valid('json')
    const currentAdmin = getCurrentAdmin(c)

    // Convert numeric fields to strings for decimal columns
    const processedExpenseData = {
      ...expenseData,
      amountPerSchool: expenseData.amountPerSchool ? expenseData.amountPerSchool.toString() : null,
      percentageValue: expenseData.percentageValue ? expenseData.percentageValue.toString() : null
    }

    const [newExpense] = await db.insert(operationalExpenses).values({
      ...processedExpenseData,
      createdBy: currentAdmin!.id
    }).returning()

    return c.json({
      message: 'Operational expense created successfully',
      expense: newExpense
    }, 201)

  } catch (error) {
    console.error('Create expense error:', error)
    return c.json({ error: 'Failed to create expense' }, 500)
  }
})

// Get all operational expenses
app.get('/expenses', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing', 'support']), async (c) => {
  try {
    const expenses = await db
      .select({
        id: operationalExpenses.id,
        categoryName: operationalExpenses.categoryName,
        description: operationalExpenses.description,
        amountPerSchool: operationalExpenses.amountPerSchool,
        isPercentage: operationalExpenses.isPercentage,
        percentageValue: operationalExpenses.percentageValue,
        appliesTo: operationalExpenses.appliesTo,
        isActive: operationalExpenses.isActive,
        createdAt: operationalExpenses.createdAt
      })
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))
      .orderBy(desc(operationalExpenses.createdAt))

    return c.json({ expenses })

  } catch (error) {
    console.error('Get expenses error:', error)
    return c.json({ error: 'Failed to fetch expenses' }, 500)
  }
})

// ===== WITHDRAWAL MANAGEMENT =====

const processWithdrawalSchema = z.object({
  status: z.enum(['approved', 'rejected']),
  transactionReference: z.string().optional(),
  rejectionReason: z.string().optional(),
  processingFee: z.number().min(0).optional()
})

// Get all withdrawal requests
app.get('/withdrawals', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const status = c.req.query('status') // 'pending', 'approved', 'processed', 'rejected'
    const offset = (page - 1) * limit

    let whereConditions = []
    if (status) {
      whereConditions.push(eq(withdrawalRequests.status, status))
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined

    const withdrawals = await db
      .select({
        id: withdrawalRequests.id,
        partnerId: withdrawalRequests.partnerId,
        partnerName: partners.name,
        partnerEmail: partners.email,
        requestedAmount: withdrawalRequests.requestedAmount,
        availableBalance: withdrawalRequests.availableBalance,
        status: withdrawalRequests.status,
        requestMonth: withdrawalRequests.requestMonth,
        requestedAt: withdrawalRequests.requestedAt,
        reviewedAt: withdrawalRequests.reviewedAt,
        createdAt: withdrawalRequests.processedAt,
        transactionReference: withdrawalRequests.transactionReference,
        rejectionReason: withdrawalRequests.rejectionReason,
        processingFee: withdrawalRequests.processingFee,
        netAmount: withdrawalRequests.netAmount
      })
      .from(withdrawalRequests)
      .innerJoin(partners, eq(partners.id, withdrawalRequests.partnerId))
      .where(whereClause)
      .orderBy(desc(withdrawalRequests.requestedAt))
      .limit(limit)
      .offset(offset)

    const [totalCount] = await db
      .select({ count: count() })
      .from(withdrawalRequests)
      .where(whereClause)

    return c.json({
      withdrawals,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get withdrawals error:', error)
    return c.json({ error: 'Failed to fetch withdrawals' }, 500)
  }
})

// Process withdrawal request (approve/reject)
app.put('/withdrawals/:id/process', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', processWithdrawalSchema), async (c) => {
  try {
    const withdrawalId = c.req.param('id')
    const { status, transactionReference, rejectionReason, processingFee } = c.req.valid('json')
    const currentAdmin = getCurrentAdmin(c)

    // Get withdrawal request
    const [withdrawal] = await db
      .select()
      .from(withdrawalRequests)
      .where(eq(withdrawalRequests.id, withdrawalId))
      .limit(1)

    if (!withdrawal) {
      return c.json({ error: 'Withdrawal request not found' }, 404)
    }

    if (withdrawal.status !== 'pending') {
      return c.json({ error: 'Withdrawal request already processed' }, 400)
    }

    const updateData: any = {
      status,
      reviewedBy: currentAdmin!.id,
      reviewedAt: new Date()
    }

    if (status === 'approved') {
      if (!transactionReference) {
        return c.json({ error: 'Transaction reference is required for approval' }, 400)
      }
      updateData.transactionReference = transactionReference
      updateData.processingFee = (processingFee || 0).toString()
      updateData.netAmount = (parseFloat(withdrawal.requestedAmount) - (processingFee || 0)).toString()
      updateData.processedAt = new Date()
      updateData.processedBy = currentAdmin!.id
      updateData.status = 'processed'
    } else if (status === 'rejected') {
      if (!rejectionReason) {
        return c.json({ error: 'Rejection reason is required' }, 400)
      }
      updateData.rejectionReason = rejectionReason
    }

    // Update withdrawal request
    const [updatedWithdrawal] = await db
      .update(withdrawalRequests)
      .set(updateData)
      .where(eq(withdrawalRequests.id, withdrawalId))
      .returning()

    // If approved, create transaction record
    if (status === 'approved') {
      // Get current partner balance
      const [balanceResult] = await db
        .select({
          balance: sql<number>`COALESCE(SUM(CASE
            WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
            WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
            ELSE 0
          END), 0)`
        })
        .from(partnerTransactions)
        .where(eq(partnerTransactions.partnerId, withdrawal.partnerId))

      const currentBalance = balanceResult?.balance || 0
      const newBalance = currentBalance - parseFloat(withdrawal.requestedAmount)

      // Create withdrawal transaction
      await db.insert(partnerTransactions).values({
        partnerId: withdrawal.partnerId!,
        transactionType: 'WITHDRAWAL',
        amount: withdrawal.requestedAmount,
        description: `Withdrawal processed - ${transactionReference}`,
        referenceId: withdrawalId,
        referenceType: 'withdrawal_request',
        balanceBefore: currentBalance.toString(),
        balanceAfter: newBalance.toString(),
        createdBy: currentAdmin!.id
      })
    }

    return c.json({
      message: `Withdrawal request ${status} successfully`,
      withdrawal: updatedWithdrawal
    })

  } catch (error) {
    console.error('Process withdrawal error:', error)
    return c.json({ error: 'Failed to process withdrawal request' }, 500)
  }
})

// Get partner earnings and transactions
app.get('/partners/:id/earnings', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing', 'support']), async (c) => {
  try {
    const partnerId = c.req.param('id')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const offset = (page - 1) * limit

    // Get earnings
    const earnings = await db
      .select({
        id: partnerEarnings.id,
        clientId: partnerEarnings.clientId,
        schoolName: clients.schoolName,
        grossAmount: partnerEarnings.grossAmount,
        totalExpenses: partnerEarnings.totalExpenses,
        netProfit: partnerEarnings.netProfit,
        partnerSharePercentage: partnerEarnings.partnerSharePercentage,
        partnerEarning: partnerEarnings.partnerEarning,
        status: partnerEarnings.status,
        calculatedAt: partnerEarnings.calculatedAt,
        availableAt: partnerEarnings.availableAt
      })
      .from(partnerEarnings)
      .innerJoin(clients, eq(clients.id, partnerEarnings.clientId))
      .where(eq(partnerEarnings.partnerId, partnerId))
      .orderBy(desc(partnerEarnings.calculatedAt))
      .limit(limit)
      .offset(offset)

    // Get transactions
    const transactions = await db
      .select()
      .from(partnerTransactions)
      .where(eq(partnerTransactions.partnerId, partnerId))
      .orderBy(desc(partnerTransactions.createdAt))
      .limit(10)

    // Get summary
    const [summary] = await db
      .select({
        totalEarnings: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        availableBalance: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`,
        pendingEarnings: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'pending' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partnerId))

    const [totalCount] = await db
      .select({ count: count() })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partnerId))

    return c.json({
      earnings,
      transactions,
      summary,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partner earnings error:', error)
    return c.json({ error: 'Failed to fetch partner earnings' }, 500)
  }
})

// ===== FINANCIAL MANAGEMENT & CALCULATIONS =====

// Get enhanced admin financial overview with 7 key metrics
app.get('/financial/overview', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1
    const currentYear = currentDate.getFullYear()
    const startOfMonth = new Date(currentYear, currentMonth - 1, 1)
    const endOfMonth = new Date(currentYear, currentMonth, 0)

    console.log(`📊 Calculating financial metrics for ${currentMonth}/${currentYear}`)
    console.log(`Period: ${startOfMonth.toISOString()} to ${endOfMonth.toISOString()}`)

    // ===== 1. THIS MONTH SUBSCRIPTION REVENUE =====
    // Total subscription payments collected in current calendar month
    const [thisMonthRevenue] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'paid'),
        gte(billingInvoices.paidDate, startOfMonth.toISOString().split('T')[0]),
        lte(billingInvoices.paidDate, endOfMonth.toISOString().split('T')[0])
      ))

    // ===== 2. PENDING SUBSCRIPTION REVENUE =====
    // Outstanding subscription amounts not yet received (overdue + upcoming)
    const [pendingRevenue] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        or(
          eq(billingInvoices.status, 'pending'),
          eq(billingInvoices.status, 'sent')
        ),
        gte(billingInvoices.createdAt, startOfMonth),
        lte(billingInvoices.createdAt, endOfMonth)
      ))

    // ===== 3. TOTAL REVENUE THIS MONTH =====
    // Complete revenue expected for current calendar month (dynamic calculation)
    const [totalMonthlyRevenue] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingSubscriptions.monthlyAmount} AS DECIMAL)), 0)`
      })
      .from(billingSubscriptions)
      .where(and(
        eq(billingSubscriptions.status, 'active'),
        lte(billingSubscriptions.currentPeriodStart, endOfMonth.toISOString().split('T')[0])
      ))

    // ===== 6. TOTAL EXPENSES THIS MONTH =====
    // Get subscription-specific expenses for active subscriptions this month
    const [subscriptionSpecificExpenses] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(monthly_operational_cost AS DECIMAL)), 0)`
      })
      .from(subscriptionExpenses)
      .innerJoin(billingSubscriptions, eq(billingSubscriptions.id, subscriptionExpenses.subscriptionId))
      .where(and(
        eq(subscriptionExpenses.isActive, true),
        eq(billingSubscriptions.status, 'active'),
        gte(subscriptionExpenses.effectiveFrom, startOfMonth.toISOString().split('T')[0]),
        lte(subscriptionExpenses.effectiveFrom, endOfMonth.toISOString().split('T')[0])
      ))

    // Get general operational expenses for this month (not tied to specific subscriptions)
    const [generalExpenses] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(monthly_operational_cost AS DECIMAL)), 0)`
      })
      .from(subscriptionExpenses)
      .where(and(
        eq(subscriptionExpenses.isActive, true),
        isNull(subscriptionExpenses.subscriptionId), // General expenses not tied to specific subscriptions
        gte(subscriptionExpenses.effectiveFrom, startOfMonth.toISOString().split('T')[0]),
        lte(subscriptionExpenses.effectiveFrom, endOfMonth.toISOString().split('T')[0])
      ))

    // Get detailed expense breakdown from JSONB fields
    const expenseBreakdownData = await db
      .select({
        expenseBreakdown: subscriptionExpenses.expenseBreakdown,
        monthlyOperationalCost: subscriptionExpenses.monthlyOperationalCost
      })
      .from(subscriptionExpenses)
      .innerJoin(billingSubscriptions, eq(billingSubscriptions.id, subscriptionExpenses.subscriptionId))
      .where(and(
        eq(subscriptionExpenses.isActive, true),
        eq(billingSubscriptions.status, 'active'),
        gte(subscriptionExpenses.effectiveFrom, startOfMonth.toISOString().split('T')[0]),
        lte(subscriptionExpenses.effectiveFrom, endOfMonth.toISOString().split('T')[0])
      ))

    // Calculate breakdown totals
    let databaseCosts = 0, websiteMaintenance = 0, supportCosts = 0, infrastructureCosts = 0

    expenseBreakdownData.forEach(expense => {
      if (expense.expenseBreakdown && typeof expense.expenseBreakdown === 'object') {
        const breakdown = expense.expenseBreakdown as any
        databaseCosts += parseFloat(breakdown.databaseCosts || '0')
        websiteMaintenance += parseFloat(breakdown.websiteMaintenance || '0')
        supportCosts += parseFloat(breakdown.supportCosts || '0')
        infrastructureCosts += parseFloat(breakdown.infrastructureCosts || '0')
      }
    })

    const totalExpensesCalculated = (subscriptionSpecificExpenses.total || 0) + (generalExpenses.total || 0)

    // ===== 4. PARTNERS PAYOUT AMOUNT =====
    // Total commission amount owed to all partners for current month
    const [monthlyPartnerEarnings] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`
      })
      .from(partnerEarnings)
      .where(and(
        gte(partnerEarnings.calculatedAt, startOfMonth),
        lte(partnerEarnings.calculatedAt, endOfMonth)
      ))

    const [monthlyEscrowCommissions] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerCommissionEscrow.commissionAmount} AS DECIMAL)), 0)`
      })
      .from(partnerCommissionEscrow)
      .where(and(
        gte(partnerCommissionEscrow.createdAt, startOfMonth),
        lte(partnerCommissionEscrow.createdAt, endOfMonth)
      ))

    const [monthlyCommissionTransactions] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(and(
        gte(partnerCommissionTransactions.createdAt, startOfMonth),
        lte(partnerCommissionTransactions.createdAt, endOfMonth)
      ))

    const totalPartnerCommissions =
      (monthlyPartnerEarnings.total || 0) +
      (monthlyEscrowCommissions.total || 0) +
      (monthlyCommissionTransactions.total || 0)

    // ===== 5. PARTNER PAYMENT STATUS =====
    // Amount already paid vs pending amount to partners this month
    const [paidToPartners] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(and(
        eq(partnerCommissionTransactions.status, 'paid'),
        gte(partnerCommissionTransactions.createdAt, startOfMonth),
        lte(partnerCommissionTransactions.createdAt, endOfMonth)
      ))

    const pendingToPartners = totalPartnerCommissions - (paidToPartners.total || 0)

    // Get discounts applied this month
    const [monthlyDiscounts] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.discountAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'paid'),
        gte(billingInvoices.paidDate, startOfMonth.toISOString().split('T')[0]),
        lte(billingInvoices.paidDate, endOfMonth.toISOString().split('T')[0])
      ))

    // ===== 7. FINAL ADMIN EARNINGS =====
    // Net profit after deducting all expenses, discounts, and partner commissions
    const thisMonthRevenueTotal = thisMonthRevenue.total || 0
    const totalExpenses = totalExpensesCalculated
    const totalDiscounts = monthlyDiscounts.total || 0

    const finalAdminEarnings = Math.max(0, thisMonthRevenueTotal - totalExpenses - totalDiscounts - totalPartnerCommissions)

    console.log('📊 Enhanced Financial Metrics Calculation:', {
      thisMonthRevenue: thisMonthRevenueTotal,
      pendingRevenue: pendingRevenue.total || 0,
      totalMonthlyRevenue: totalMonthlyRevenue.total || 0,
      totalPartnerCommissions,
      paidToPartners: paidToPartners.total || 0,
      pendingToPartners,
      totalExpenses,
      finalAdminEarnings,
      breakdown: {
        partnerEarnings: monthlyPartnerEarnings.total || 0,
        escrowCommissions: monthlyEscrowCommissions.total || 0,
        commissionTransactions: monthlyCommissionTransactions.total || 0
      }
    })

    // Return the 7 key financial metrics
    return c.json({
      // 1. This Month Subscription Revenue
      thisMonthRevenue: thisMonthRevenueTotal,

      // 2. Pending Subscription Revenue
      pendingRevenue: pendingRevenue.total || 0,

      // 3. Total Revenue This Month
      totalMonthlyRevenue: totalMonthlyRevenue.total || 0,

      // 4. Partners Payout Amount
      partnersPayoutAmount: totalPartnerCommissions,

      // 5. Partner Payment Status
      partnerPaymentStatus: {
        paidAmount: paidToPartners.total || 0,
        pendingAmount: pendingToPartners
      },

      // 6. Total Expenses This Month
      totalExpensesThisMonth: totalExpenses,

      // 7. Final Admin Earnings
      finalAdminEarnings: finalAdminEarnings,

      // Additional context
      period: {
        month: currentMonth,
        year: currentYear,
        startDate: startOfMonth.toISOString().split('T')[0],
        endDate: endOfMonth.toISOString().split('T')[0]
      },

      // Breakdown for transparency
      breakdown: {
        discounts: totalDiscounts,
        profitMargin: thisMonthRevenueTotal > 0 ? ((finalAdminEarnings / thisMonthRevenueTotal) * 100).toFixed(2) : 0,
        expenses: {
          subscriptionSpecific: subscriptionSpecificExpenses.total || 0,
          generalOperational: generalExpenses.total || 0,
          total: totalExpenses,
          detailedBreakdown: {
            databaseCosts,
            websiteMaintenance,
            supportCosts,
            infrastructureCosts
          }
        }
      }
    })

  } catch (error) {
    console.error('Admin financial overview error:', error)
    return c.json({ error: 'Failed to fetch admin financial overview' }, 500)
  }
})

/**
 * Calculate and store partner commission directly (bypassing escrow)
 */
async function calculatePartnerCommissionDirect(
  paymentId: string,
  subscriptionId: string,
  grossAmount: number,
  adminId: string
): Promise<void> {
  try {
    // Get subscription details
    const [subscription] = await db
      .select({
        id: billingSubscriptions.id,
        clientId: billingSubscriptions.clientId,
        monthlyAmount: billingSubscriptions.monthlyAmount
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.id, subscriptionId))
      .limit(1)

    if (!subscription || !subscription.clientId) {
      console.log(`❌ Subscription not found or missing clientId: ${subscriptionId}`)
      return
    }

    // Check if this school has a partner referral
    const [referral] = await db
      .select({
        partnerId: schoolReferrals.partnerId,
        isActive: schoolReferrals.isActive
      })
      .from(schoolReferrals)
      .where(and(
        eq(schoolReferrals.clientId, subscription.clientId),
        eq(schoolReferrals.isActive, true)
      ))
      .limit(1)

    if (!referral) {
      console.log(`ℹ️ No active partner referral found for client: ${subscription.clientId}`)
      return
    }

    // Get partner details
    const [partner] = await db
      .select({
        id: partners.id,
        profitSharePercentage: partners.profitSharePercentage,
        name: partners.name
      })
      .from(partners)
      .where(and(
        eq(partners.id, referral.partnerId),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (!partner) {
      console.log(`❌ Partner not found or inactive: ${referral.partnerId}`)
      return
    }

    // Get operational expenses for this subscription
    const [expenses] = await db
      .select({
        monthlyOperationalCost: subscriptionExpenses.monthlyOperationalCost
      })
      .from(subscriptionExpenses)
      .where(and(
        eq(subscriptionExpenses.subscriptionId, subscriptionId),
        eq(subscriptionExpenses.isActive, true)
      ))
      .limit(1)

    const operationalExpenses = parseFloat(expenses?.monthlyOperationalCost || '0')
    const netProfit = grossAmount - operationalExpenses
    const partnerSharePercentage = parseFloat(partner.profitSharePercentage || '20')
    const partnerEarning = Math.max(0, (netProfit * partnerSharePercentage) / 100)

    // Create partner earning record (direct, no escrow)
    await db.insert(partnerEarnings).values({
      partnerId: partner.id,
      clientId: subscription.clientId!, // We already checked this is not null above
      invoiceId: paymentId, // Using payment ID as invoice reference
      paymentId: paymentId,
      grossAmount: grossAmount.toString(),
      totalExpenses: operationalExpenses.toString(),
      netProfit: netProfit.toString(),
      partnerSharePercentage: partnerSharePercentage.toString(),
      partnerEarning: partnerEarning.toString(),
      status: 'available', // Immediately available, no escrow
      calculatedAt: new Date(),
      availableAt: new Date(),
      calculatedBy: adminId,
      escrowStatus: 'manual', // Bypassing escrow
      notes: `Direct commission calculation - bypassing escrow system`
    })

    console.log(`✅ Partner commission calculated directly:`, {
      partnerId: partner.id,
      partnerName: partner.name,
      grossAmount,
      operationalExpenses,
      netProfit,
      partnerSharePercentage,
      partnerEarning,
      paymentId
    })

  } catch (error) {
    console.error('❌ Error calculating partner commission:', error)
    throw error
  }
}

// Manually trigger commission calculation for existing payments
app.post('/recalculate-commissions', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const currentAdmin = getCurrentAdmin(c)

    // Get all paid invoices that don't have partner earnings
    const paymentsWithoutCommissions = await db
      .select({
        paymentId: billingPayments.id,
        subscriptionId: billingPayments.subscriptionId,
        amount: billingPayments.amount,
        paidDate: billingPayments.createdAt
      })
      .from(billingPayments)
      .leftJoin(partnerEarnings, eq(partnerEarnings.paymentId, billingPayments.id))
      .where(and(
        eq(billingPayments.status, 'completed'),
        isNull(partnerEarnings.id) // No existing commission record
      ))

    let processedCount = 0
    let errorCount = 0

    for (const payment of paymentsWithoutCommissions) {
      try {
        await calculatePartnerCommissionDirect(
          payment.paymentId,
          payment.subscriptionId || '',
          parseFloat(payment.amount),
          currentAdmin?.id || 'system'
        )
        processedCount++
      } catch (error) {
        console.error(`❌ Failed to process payment ${payment.paymentId}:`, error)
        errorCount++
      }
    }

    return c.json({
      success: true,
      message: `Commission recalculation completed`,
      results: {
        totalPayments: paymentsWithoutCommissions.length,
        processed: processedCount,
        errors: errorCount
      }
    })

  } catch (error) {
    console.error('Commission recalculation error:', error)
    return c.json({ error: 'Failed to recalculate commissions' }, 500)
  }
})

// Release all existing escrow commissions (remove escrow system)
app.post('/release-all-escrow-commissions', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const currentAdmin = getCurrentAdmin(c)

    // Get all held escrow commissions
    const escrowCommissions = await db
      .select({
        id: partnerCommissionEscrow.id,
        partnerId: partnerCommissionEscrow.partnerId,
        schoolId: partnerCommissionEscrow.schoolId,
        commissionAmount: partnerCommissionEscrow.commissionAmount,
        baseAmount: partnerCommissionEscrow.baseAmount,
        operationalExpenses: partnerCommissionEscrow.operationalExpenses,
        commissionPercentage: partnerCommissionEscrow.commissionPercentage,
        monthYear: partnerCommissionEscrow.monthYear
      })
      .from(partnerCommissionEscrow)
      .where(eq(partnerCommissionEscrow.escrowStatus, 'held'))

    let releasedCount = 0
    let errorCount = 0

    for (const escrow of escrowCommissions) {
      try {
        // Create partner earning record from escrow data
        await db.insert(partnerEarnings).values({
          partnerId: escrow.partnerId,
          clientId: escrow.schoolId,
          invoiceId: escrow.id, // Using escrow ID as reference
          grossAmount: escrow.baseAmount,
          totalExpenses: escrow.operationalExpenses || '0',
          netProfit: (parseFloat(escrow.baseAmount) - parseFloat(escrow.operationalExpenses || '0')).toString(),
          partnerSharePercentage: escrow.commissionPercentage,
          partnerEarning: escrow.commissionAmount,
          status: 'available',
          calculatedAt: new Date(),
          availableAt: new Date(),
          calculatedBy: currentAdmin?.id || 'system',
          escrowStatus: 'manual',
          notes: `Released from escrow system - Month: ${escrow.monthYear}`
        })

        // Update escrow status to released
        await db.update(partnerCommissionEscrow)
          .set({
            escrowStatus: 'released',
            releasedAt: new Date(),
            updatedAt: new Date()
          })
          .where(eq(partnerCommissionEscrow.id, escrow.id))

        releasedCount++
      } catch (error) {
        console.error(`❌ Failed to release escrow ${escrow.id}:`, error)
        errorCount++
      }
    }

    return c.json({
      success: true,
      message: `Escrow release completed`,
      results: {
        totalEscrowCommissions: escrowCommissions.length,
        released: releasedCount,
        errors: errorCount
      }
    })

  } catch (error) {
    console.error('Escrow release error:', error)
    return c.json({ error: 'Failed to release escrow commissions' }, 500)
  }
})

// Calculate partner earnings for a specific payment
app.post('/calculate-earnings/:paymentId', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const paymentId = c.req.param('paymentId')
    const currentAdmin = getCurrentAdmin(c)

    // Get all payments without commission calculations
    const paymentsWithoutCommissions = await db
      .select({
        paymentId: billingPayments.id,
        clientId: billingPayments.clientId,
        amount: billingPayments.amount,
        createdAt: billingPayments.createdAt
      })
      .from(billingPayments)
      .leftJoin(partnerEarnings, eq(partnerEarnings.paymentId, billingPayments.id))
      .where(and(
        eq(billingPayments.status, 'succeeded'),
        isNull(partnerEarnings.id) // No commission record exists
      ))
      .orderBy(desc(billingPayments.createdAt))

    console.log(`Found ${paymentsWithoutCommissions.length} payments without commission calculations`)

    let processedCount = 0
    let errorCount = 0

    for (const payment of paymentsWithoutCommissions) {
      try {
        // Skip payments without clientId
        if (!payment.clientId) {
          console.log(`Skipping payment ${payment.paymentId} - no clientId`)
          continue
        }

        // Check if this school has a partner referral
        const [partnerReferral] = await db
          .select({
            partnerId: schoolReferrals.partnerId,
            isActive: schoolReferrals.isActive
          })
          .from(schoolReferrals)
          .where(and(
            eq(schoolReferrals.clientId, payment.clientId),
            eq(schoolReferrals.isActive, true)
          ))
          .limit(1)

        if (partnerReferral && partnerReferral.partnerId) {
          console.log(`Processing commission for partner ${partnerReferral.partnerId} on payment ${payment.paymentId}`)

          const grossAmount = parseFloat(payment.amount)

          // Import commission processor
          const { commissionProcessor } = await import('@/src/services/commissionProcessor')

          await commissionProcessor.processCommissionForPayment(
            payment.paymentId,
            partnerReferral.partnerId,
            payment.clientId,
            grossAmount
          )

          processedCount++
          console.log(`Commission processing completed for payment ${payment.paymentId}`)
        }
      } catch (error) {
        console.error(`Error processing commission for payment ${payment.paymentId}:`, error)
        errorCount++
      }
    }

    return c.json({
      message: 'Commission recalculation completed',
      results: {
        totalPayments: paymentsWithoutCommissions.length,
        processed: processedCount,
        errors: errorCount
      }
    })

  } catch (error) {
    console.error('Commission recalculation error:', error)
    return c.json({ error: 'Failed to recalculate commissions' }, 500)
  }
})

// Calculate partner earnings for a specific payment
app.post('/calculate-earnings/:paymentId', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const paymentId = c.req.param('paymentId')
    const currentAdmin = getCurrentAdmin(c)

    // Get payment details with client and partner information
    const paymentData = await db
      .select({
        paymentId: billingPayments.id,
        monthlyAmount: billingPayments.amount,
        clientId: clients.id,
        schoolName: clients.schoolName,
        partnerId: schoolReferrals.partnerId,
        partnerSharePercentage: partners.profitSharePercentage,
        invoiceId: billingPayments.invoiceId
      })
      .from(billingPayments)
      .innerJoin(clients, eq(clients.id, billingPayments.clientId))
      .leftJoin(schoolReferrals, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(partners, eq(partners.id, schoolReferrals.partnerId))
      .where(eq(billingPayments.id, paymentId))
      .limit(1)

    const [payment] = paymentData

    if (!payment) {
      return c.json({ error: 'Payment not found' }, 404)
    }

    if (!payment.partnerId) {
      return c.json({ error: 'No partner associated with this payment' }, 400)
    }

    // Check if earnings already calculated
    const [existingEarning] = await db
      .select()
      .from(partnerEarnings)
      .where(and(
        eq(partnerEarnings.paymentId, paymentId),
        eq(partnerEarnings.partnerId, payment.partnerId)
      ))
      .limit(1)

    if (existingEarning) {
      return c.json({ error: 'Earnings already calculated for this payment' }, 400)
    }

    // Get operational expenses
    const expenses = await db
      .select()
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))

    // Calculate total expenses
    let totalExpenses = 0
    const expenseBreakdown = []

    for (const expense of expenses) {
      let expenseAmount = 0

      if (expense.isPercentage && expense.percentageValue) {
        expenseAmount = (parseFloat(payment.monthlyAmount) * parseFloat(expense.percentageValue)) / 100
      } else if (expense.amountPerSchool) {
        expenseAmount = parseFloat(expense.amountPerSchool)
      }

      if (expenseAmount > 0) {
        totalExpenses += expenseAmount
        expenseBreakdown.push({
          category: expense.categoryName,
          monthlyAmount: expenseAmount,
          type: expense.isPercentage ? 'percentage' : 'fixed'
        })
      }
    }

    // Calculate net profit and partner earning
    const grossAmount = parseFloat(payment.monthlyAmount)
    const netProfit = grossAmount - totalExpenses
    const partnerSharePercentage = payment.partnerSharePercentage ? parseFloat(payment.partnerSharePercentage) : 40 // Default 40%
    const partnerEarning = (netProfit * partnerSharePercentage) / 100

    // Create partner earning record
    const [newEarning] = await db.insert(partnerEarnings).values({
      partnerId: payment.partnerId!,
      clientId: payment.clientId!,
      invoiceId: payment.invoiceId!,
      paymentId: payment.paymentId || null,
      grossAmount: grossAmount.toString(),
      totalExpenses: totalExpenses.toString(),
      netProfit: netProfit.toString(),
      partnerSharePercentage: partnerSharePercentage.toString(),
      partnerEarning: partnerEarning.toString(),
      status: 'available', // Available immediately after payment confirmation
      calculatedAt: new Date(),
      availableAt: new Date(),
      calculatedBy: currentAdmin!.id,
      expenseBreakdown: expenseBreakdown
    }).returning()

    // Create transaction record
    const [balanceResult] = await db
      .select({
        balance: sql<number>`COALESCE(SUM(CASE
          WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
          WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
          ELSE 0
        END), 0)`
      })
      .from(partnerTransactions)
      .where(eq(partnerTransactions.partnerId, payment.partnerId!))

    const currentBalance = balanceResult?.balance || 0
    const newBalance = currentBalance + partnerEarning

    await db.insert(partnerTransactions).values({
      partnerId: payment.partnerId!,
      transactionType: 'EARNING',
      amount: partnerEarning.toString(),
      description: `Earning from ${payment.schoolName} - Payment ${paymentId}`,
      referenceId: newEarning.id,
      referenceType: 'partner_earning',
      balanceBefore: currentBalance.toString(),
      balanceAfter: newBalance.toString(),
      createdBy: currentAdmin!.id
    })

    return c.json({
      message: 'Partner earnings calculated successfully',
      earning: {
        id: newEarning.id,
        grossAmount,
        totalExpenses,
        netProfit,
        partnerSharePercentage,
        partnerEarning,
        expenseBreakdown
      }
    })

  } catch (error) {
    console.error('Calculate earnings error:', error)
    return c.json({ error: 'Failed to calculate partner earnings' }, 500)
  }
})

// Bulk calculate earnings for all pending payments
app.post('/calculate-all-earnings', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const currentAdmin = getCurrentAdmin(c)

    // Get all successful payments that don't have earnings calculated
    const pendingPayments = await db
      .select({
        paymentId: billingPayments.id,
        monthlyAmount: billingPayments.amount,
        clientId: clients.id,
        schoolName: clients.schoolName,
        partnerId: schoolReferrals.partnerId,
        partnerSharePercentage: partners.profitSharePercentage,
        invoiceId: billingPayments.invoiceId
      })
      .from(billingPayments)
      .innerJoin(clients, eq(clients.id, billingPayments.clientId))
      .leftJoin(schoolReferrals, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(partners, eq(partners.id, schoolReferrals.partnerId))
      .leftJoin(partnerEarnings, and(
        eq(partnerEarnings.paymentId, billingPayments.id),
        eq(partnerEarnings.partnerId, schoolReferrals.partnerId)
      ))
      .where(and(
        eq(billingPayments.status, 'success'),
        sql`${schoolReferrals.partnerId} IS NOT NULL`,
        sql`${partnerEarnings.id} IS NULL`
      ))

    if (pendingPayments.length === 0) {
      return c.json({ message: 'No pending payments to calculate earnings for' })
    }

    // Get operational expenses once
    const expenses = await db
      .select()
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))

    let calculatedCount = 0
    const results = []

    for (const payment of pendingPayments) {
      try {
        // Calculate total expenses
        let totalExpenses = 0
        const expenseBreakdown = []

        for (const expense of expenses) {
          let expenseAmount = 0

          if (expense.isPercentage && expense.percentageValue) {
            expenseAmount = (parseFloat(payment.monthlyAmount) * parseFloat(expense.percentageValue)) / 100
          } else if (expense.amountPerSchool) {
            expenseAmount = parseFloat(expense.amountPerSchool)
          }

          if (expenseAmount > 0) {
            totalExpenses += expenseAmount
            expenseBreakdown.push({
              category: expense.categoryName,
              monthlyAmount: expenseAmount,
              type: expense.isPercentage ? 'percentage' : 'fixed'
            })
          }
        }

        // Calculate earnings
        const grossAmount = parseFloat(payment.monthlyAmount)
        const netProfit = grossAmount - totalExpenses
        const partnerSharePercentage = payment.partnerSharePercentage ? parseFloat(payment.partnerSharePercentage) : 40
        const partnerEarning = (netProfit * partnerSharePercentage) / 100

        // Create earning record
        const [newEarning] = await db.insert(partnerEarnings).values({
          partnerId: payment.partnerId!,
          clientId: payment.clientId!,
          invoiceId: payment.invoiceId!,
          paymentId: payment.paymentId || null,
          grossAmount: grossAmount.toString(),
          totalExpenses: totalExpenses.toString(),
          netProfit: netProfit.toString(),
          partnerSharePercentage: partnerSharePercentage.toString(),
          partnerEarning: partnerEarning.toString(),
          status: 'available',
          calculatedAt: new Date(),
          availableAt: new Date(),
          calculatedBy: currentAdmin!.id,
          expenseBreakdown: expenseBreakdown
        }).returning()

        // Create transaction record
        const [balanceResult] = await db
          .select({
            balance: sql<number>`COALESCE(SUM(CASE
              WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
              WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
              ELSE 0
            END), 0)`
          })
          .from(partnerTransactions)
          .where(eq(partnerTransactions.partnerId, payment.partnerId!))

        const currentBalance = balanceResult?.balance || 0
        const newBalance = currentBalance + partnerEarning

        await db.insert(partnerTransactions).values({
          partnerId: payment.partnerId!,
          transactionType: 'EARNING',
          amount: partnerEarning.toString(),
          description: `Earning from ${payment.schoolName} - Payment ${payment.paymentId}`,
          referenceId: newEarning.id,
          referenceType: 'partner_earning',
          balanceBefore: currentBalance.toString(),
          balanceAfter: newBalance.toString(),
          createdBy: currentAdmin!.id
        })

        calculatedCount++
        results.push({
          paymentId: payment.paymentId,
          schoolName: payment.schoolName,
          partnerEarning,
          status: 'success'
        })

      } catch (error) {
        console.error(`Error calculating earnings for payment ${payment.paymentId}:`, error)
        results.push({
          paymentId: payment.paymentId,
          schoolName: payment.schoolName,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return c.json({
      message: `Calculated earnings for ${calculatedCount} out of ${pendingPayments.length} payments`,
      calculatedCount,
      totalPayments: pendingPayments.length,
      results
    })

  } catch (error) {
    console.error('Bulk calculate earnings error:', error)
    return c.json({ error: 'Failed to calculate bulk earnings' }, 500)
  }
})

// Update operational expense
app.put('/expenses/:id', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const expenseId = c.req.param('id')
    const updateData = c.req.json()

    const [updatedExpense] = await db
      .update(operationalExpenses)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(operationalExpenses.id, expenseId))
      .returning()

    if (!updatedExpense) {
      return c.json({ error: 'Expense not found' }, 404)
    }

    return c.json({
      message: 'Expense updated successfully',
      expense: updatedExpense
    })

  } catch (error) {
    console.error('Update expense error:', error)
    return c.json({ error: 'Failed to update expense' }, 500)
  }
})

// ===== SYSTEM INFORMATION =====

// Get system information and statistics
app.get('/system-info', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    // Get various system statistics
    const [totalLeads] = await db.select({ count: count() }).from(leads)
    const [totalClients] = await db.select({ count: count() }).from(clients)
    const [totalAdminUsers] = await db.select({ count: count() }).from(adminUsers)
    const [totalClientUsers] = await db.select({ count: count() }).from(clientUsers)
    const [pendingRequests] = await db.select({ count: count() }).from(softwareRequests).where(eq(softwareRequests.status, 'pending'))
    const [activeClients] = await db.select({ count: count() }).from(clients).where(eq(clients.onboardingStatus, 'completed'))

    // Partner system statistics
    const [totalPartners] = await db.select({ count: count() }).from(partners)
    const [activePartners] = await db.select({ count: count() }).from(partners).where(eq(partners.isActive, true))
    const [totalReferrals] = await db.select({ count: count() }).from(schoolReferrals)
    const [pendingWithdrawals] = await db.select({ count: count() }).from(withdrawalRequests).where(eq(withdrawalRequests.status, 'pending'))

    const [partnerEarningsStats] = await db
      .select({
        totalEarnings: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        availableBalance: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)

    return c.json({
      systemStats: {
        totalLeads: totalLeads.count,
        totalClients: totalClients.count,
        activeClients: activeClients.count,
        totalAdminUsers: totalAdminUsers.count,
        totalClientUsers: totalClientUsers.count,
        pendingRequests: pendingRequests.count
      },
      partnerStats: {
        totalPartners: totalPartners.count,
        activePartners: activePartners.count,
        totalReferrals: totalReferrals.count,
        pendingWithdrawals: pendingWithdrawals.count,
        totalEarnings: partnerEarningsStats?.totalEarnings || 0,
        availableBalance: partnerEarningsStats?.availableBalance || 0
      },
      serverInfo: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
      }
    })

  } catch (error) {
    console.error('System info error:', error)
    return c.json({ error: 'Failed to fetch system information' }, 500)
  }
})

// ===== DUE DATE MANAGEMENT =====

// Process overdue billing cycles and update statuses
app.post('/billing/process-due-dates', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await DueDateManager.processAllDueDateUpdates()

    return c.json({
      message: 'Due date processing completed successfully',
      result,
      processedBy: admin.name,
      createdAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Process due dates error:', error)
    return c.json({ error: 'Failed to process due dates' }, 500)
  }
})

// Get due date information for a specific subscription
app.get('/subscriptions/:id/due-date-info', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const dueDateInfo = await DueDateManager.getSubscriptionDueDateInfo(subscriptionId)

    if (!dueDateInfo) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      subscriptionId,
      dueDateInfo
    })

  } catch (error) {
    console.error('Get due date info error:', error)
    return c.json({ error: 'Failed to get due date information' }, 500)
  }
})

// Update subscription due date settings
app.put('/subscriptions/:id/due-date-settings', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  nextBillingDate: z.number().min(1).max(31),
  gracePeriodDays: z.number().min(0).max(30)
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { nextBillingDate, gracePeriodDays } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Update subscription due date settings
    const [updatedSubscription] = await db.update(subscriptions)
      .set({
        dueDate: nextBillingDate,
        gracePeriodDays,
        updatedAt: new Date()
      })
      .where(eq(subscriptions.id, subscriptionId))
      .returning()

    if (!updatedSubscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      message: 'Due date settings updated successfully',
      subscription: updatedSubscription,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update due date settings error:', error)
    return c.json({ error: 'Failed to update due date settings' }, 500)
  }
})

// Get overdue billing cycles summary
app.get('/billing/overdue-summary', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Get overdue billing cycles with client information
    const overdueCycles = await db.select({
      id: billingSubscriptions.id,
      nextBillingDate: billingSubscriptions.nextBillingDate,
      monthlyAmount: billingSubscriptions.monthlyAmount,
      status: billingSubscriptions.status,
      gracePeriodDays: billingSubscriptions.gracePeriodDays,
      // Client information
      clientId: billingSubscriptions.clientId,
      schoolName: clients.schoolName,
      email: clients.email
    })
    .from(billingSubscriptions)
    .leftJoin(clients, eq(billingSubscriptions.clientId, clients.id))
    .where(and(
      inArray(billingSubscriptions.status, ['overdue', 'suspended']),
      lte(billingSubscriptions.nextBillingDate, today.toISOString().split('T')[0])
    ))
    .orderBy(desc(billingSubscriptions.nextBillingDate))

    // Calculate summary statistics
    const totalOverdueAmount = overdueCycles.reduce((sum, cycle) => sum + parseFloat(cycle.monthlyAmount), 0)
    const totalPenaltyAmount = 0 // Penalty calculation would need to be implemented separately
    const overdueCount = overdueCycles.filter(c => c.status === 'overdue').length
    const suspendedCount = overdueCycles.filter(c => c.status === 'suspended').length

    return c.json({
      summary: {
        totalOverdueAmount,
        totalPenaltyAmount,
        overdueCount,
        suspendedCount,
        totalCycles: overdueCycles.length
      },
      overdueCycles
    })

  } catch (error) {
    console.error('Get overdue summary error:', error)
    return c.json({ error: 'Failed to get overdue summary' }, 500)
  }
})

// ===== SUBSCRIPTION STATUS MANAGEMENT =====

// Get subscription status information
app.get('/subscriptions/:id/status-info', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const statusInfo = await SubscriptionStatusManager.getSubscriptionStatusInfo(subscriptionId)

    if (!statusInfo) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      subscriptionId,
      statusInfo
    })

  } catch (error) {
    console.error('Get subscription status info error:', error)
    return c.json({ error: 'Failed to get subscription status information' }, 500)
  }
})

// Suspend subscription
app.post('/subscriptions/:id/suspend', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  reason: z.string().min(1, 'Reason is required')
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { reason } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await SubscriptionStatusManager.suspendSubscription(
      subscriptionId,
      reason,
      admin.id
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription suspended successfully',
      subscriptionId,
      reason,
      suspendedBy: admin.name,
      suspendedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Suspend subscription error:', error)
    return c.json({ error: 'Failed to suspend subscription' }, 500)
  }
})

// Reactivate subscription
app.post('/subscriptions/:id/reactivate', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  reason: z.string().min(1, 'Reason is required')
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { reason } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await SubscriptionStatusManager.reactivateSubscription(
      subscriptionId,
      reason,
      admin.id
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription reactivated successfully',
      subscriptionId,
      reason,
      reactivatedBy: admin.name,
      reactivatedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Reactivate subscription error:', error)
    return c.json({ error: 'Failed to reactivate subscription' }, 500)
  }
})

// Cancel subscription
app.post('/subscriptions/:id/cancel', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  reason: z.string().min(1, 'Reason is required'),
  effectiveDate: z.string().optional()
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { reason, effectiveDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const effectiveDateObj = effectiveDate ? new Date(effectiveDate) : undefined
    const result = await SubscriptionStatusManager.cancelSubscription(
      subscriptionId,
      reason,
      admin.id,
      effectiveDateObj
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription cancelled successfully',
      subscriptionId,
      reason,
      effectiveDate: effectiveDateObj?.toISOString(),
      cancelledBy: admin.name,
      cancelledAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Cancel subscription error:', error)
    return c.json({ error: 'Failed to cancel subscription' }, 500)
  }
})

// Get subscription renewal information
app.get('/subscriptions/:id/renewal-info', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const renewalInfo = await SubscriptionStatusManager.getRenewalInfo(subscriptionId)

    if (!renewalInfo) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      subscriptionId,
      renewalInfo
    })

  } catch (error) {
    console.error('Get renewal info error:', error)
    return c.json({ error: 'Failed to get renewal information' }, 500)
  }
})

// Transition subscription status (general endpoint)
app.post('/subscriptions/:id/transition-status', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  newStatus: z.enum(['active', 'suspended', 'cancelled', 'expired', 'pending', 'overdue']),
  reason: z.string().min(1, 'Reason is required'),
  metadata: z.record(z.any()).optional()
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { newStatus, reason, metadata } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await SubscriptionStatusManager.transitionStatus(
      subscriptionId,
      newStatus,
      reason,
      admin.id,
      metadata
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription status updated successfully',
      transition: result.transition,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Transition subscription status error:', error)
    return c.json({ error: 'Failed to transition subscription status' }, 500)
  }
})

// ===== BILLING SYSTEM MONITORING =====

// Get billing system health status
app.get('/billing/system-health', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const healthStatus = await billingScheduler.getSystemHealth()

    return c.json({
      message: 'Billing system health retrieved successfully',
      health: healthStatus,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Get system health error:', error)
    return c.json({ error: 'Failed to get system health status' }, 500)
  }
})

// Get billing scheduler task status
app.get('/billing/scheduler-status', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const taskStatus = billingScheduler.getStatus()

    return c.json({
      message: 'Scheduler status retrieved successfully',
      tasks: taskStatus,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Get scheduler status error:', error)
    return c.json({ error: 'Failed to get scheduler status' }, 500)
  }
})

// Manually trigger billing generation
app.post('/billing/trigger-manual-billing', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log(`🔄 Manual billing triggered by admin: ${admin.name} (${admin.email})`)

    const result = await billingScheduler.triggerManualBilling(admin.id)

    return c.json({
      message: 'Manual billing completed successfully',
      result,
      triggeredBy: admin.name,
      triggeredAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Manual billing trigger error:', error)
    return c.json({ error: 'Failed to trigger manual billing' }, 500)
  }
})

// Manually trigger overdue check
app.post('/billing/trigger-overdue-check', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log(`🔄 Manual overdue check triggered by admin: ${admin.name} (${admin.email})`)

    const result = await billingScheduler.checkOverdueInvoices()

    return c.json({
      message: 'Overdue check completed successfully',
      result,
      triggeredBy: admin.name,
      triggeredAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Manual overdue check error:', error)
    return c.json({ error: 'Failed to trigger overdue check' }, 500)
  }
})

// Manually trigger payment reminders
app.post('/billing/trigger-payment-reminders', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log(`🔄 Manual payment reminders triggered by admin: ${admin.name} (${admin.email})`)

    const result = await billingScheduler.sendPaymentReminders()

    return c.json({
      message: 'Payment reminders completed successfully',
      result,
      triggeredBy: admin.name,
      triggeredAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Manual payment reminders error:', error)
    return c.json({ error: 'Failed to trigger payment reminders' }, 500)
  }
})

// Get billing analytics and metrics
app.get('/billing/analytics', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { period = '30' } = c.req.query()
    const days = parseInt(period)
    const billingStartDate = new Date()
    billingStartDate.setDate(billingStartDate.getDate() - days)

    // Get billing metrics
    const [totalRevenue] = await db.select({
      total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
    })
    .from(billingInvoices)
    .where(and(
      eq(billingInvoices.status, 'paid'),
      gte(billingInvoices.paidDate, billingStartDate.toISOString().split('T')[0])
    ))

    const [pendingRevenue] = await db.select({
      total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
    })
    .from(billingInvoices)
    .where(eq(billingInvoices.status, 'sent'))

    const [overdueRevenue] = await db.select({
      total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
    })
    .from(billingInvoices)
    .where(eq(billingInvoices.status, 'overdue'))

    const [activeSubscriptionsCount] = await db.select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'active'))

    const [totalInvoicesCount] = await db.select({ count: count() })
      .from(billingInvoices)
      .where(gte(billingInvoices.issuedDate, billingStartDate.toISOString().split('T')[0]))

    return c.json({
      period: `${days} days`,
      metrics: {
        totalRevenue: totalRevenue.total || 0,
        pendingRevenue: pendingRevenue.total || 0,
        overdueRevenue: overdueRevenue.total || 0,
        activeSubscriptions: activeSubscriptionsCount.count,
        totalInvoices: totalInvoicesCount.count
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Get billing analytics error:', error)
    return c.json({ error: 'Failed to get billing analytics' }, 500)
  }
})

// ===== ANALYTICS REPORTS EXPORT =====

// Export comprehensive analytics reports
app.get('/reports/export', adminAuthMiddleware, requirePermission('analytics:read'), async (c) => {
  try {
    const { type = 'comprehensive', period = '30' } = c.req.query()
    const days = parseInt(period)
    const reportStartDate = new Date()
    reportStartDate.setDate(reportStartDate.getDate() - days)

    let csvData = ''
    let filename = `${type}-report-${new Date().toISOString().split('T')[0]}.csv`

    switch (type) {
      case 'revenue':
        // Revenue report
        const revenueData = await db.select({
          date: billingInvoices.createdAt,
          clientName: clients.schoolName,
          monthlyAmount: billingInvoices.totalAmount,
          status: billingInvoices.status,
          paidDate: billingInvoices.paidDate
        })
        .from(billingInvoices)
        .leftJoin(clients, eq(billingInvoices.clientId, clients.id))
        .where(gte(billingInvoices.createdAt, reportStartDate))
        .orderBy(desc(billingInvoices.createdAt))

        csvData = 'Date,Client Name,Amount,Status,Paid Date\n'
        revenueData.forEach(row => {
          csvData += `${row.date},${row.clientName || 'Unknown'},₹${row.monthlyAmount},${row.status},${row.paidDate || 'N/A'}\n`
        })
        break

      case 'clients':
        // Client report with partner referral info
        const clientData = await db.select({
          schoolName: clients.schoolName,
          email: clients.email,
          phone: clients.phone,
          classFee: clients.classFee,
          studentCount: clients.actualStudentCount,
          createdAt: clients.createdAt,
          partnerCode: referralCodes.code
        })
        .from(clients)
        .leftJoin(schoolReferrals, eq(clients.id, schoolReferrals.clientId))
        .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
        .where(gte(clients.createdAt, reportStartDate))
        .orderBy(desc(clients.createdAt))

        csvData = 'School Name,Email,Phone,Class Fee,Student Count,Created Date,Partner Code\n'
        clientData.forEach(row => {
          csvData += `${row.schoolName},${row.email},${row.phone || 'N/A'},₹${row.classFee || 0},${row.studentCount || 0},${row.createdAt},${row.partnerCode || 'Direct'}\n`
        })
        break

      case 'partners':
        // Partner report with earnings from partner_earnings table
        const partnerData = await db.select({
          name: partners.name,
          email: partners.email,
          phone: partners.phone,
          partnerCode: partners.partnerCode,
          totalEarnings: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`,
          createdAt: partners.createdAt
        })
        .from(partners)
        .leftJoin(partnerEarnings, eq(partners.id, partnerEarnings.partnerId))
        .where(gte(partners.createdAt, reportStartDate))
        .groupBy(partners.id, partners.name, partners.email, partners.phone, partners.partnerCode, partners.createdAt)
        .orderBy(desc(partners.createdAt))

        csvData = 'Name,Email,Phone,Partner Code,Total Earnings,Created Date\n'
        partnerData.forEach(row => {
          csvData += `${row.name},${row.email},${row.phone || 'N/A'},${row.partnerCode},₹${row.totalEarnings || 0},${row.createdAt}\n`
        })
        break

      case 'payments':
        // Payment report
        const paymentData = await db.select({
          date: billingPayments.createdAt,
          clientName: clients.schoolName,
          monthlyAmount: billingPayments.amount,
          status: billingPayments.status,
          paymentMethod: billingPayments.paymentMethod,
          razorpayPaymentId: billingPayments.razorpayPaymentId,
          paidDate: billingPayments.createdAt
        })
        .from(billingPayments)
        .leftJoin(clients, eq(billingPayments.clientId, clients.id))
        .where(gte(billingPayments.createdAt, reportStartDate))
        .orderBy(desc(billingPayments.createdAt))

        csvData = 'Date,Client Name,Amount,Status,Payment Method,Payment ID,Paid Date\n'
        paymentData.forEach(row => {
          csvData += `${row.date},${row.clientName || 'Unknown'},₹${row.monthlyAmount},${row.status},${row.paymentMethod || 'N/A'},${row.razorpayPaymentId || 'N/A'},${row.paidDate || 'N/A'}\n`
        })
        break

      case 'comprehensive':
      default:
        // Comprehensive report with all key metrics
        const [totalRevenue] = await db.select({
          total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
        })
        .from(billingInvoices)
        .where(and(
          eq(billingInvoices.status, 'paid'),
          gte(billingInvoices.createdAt, reportStartDate)
        ))

        const [totalClients] = await db.select({ count: count() })
          .from(clients)
          .where(gte(clients.createdAt, reportStartDate))

        const [totalLeads] = await db.select({ count: count() })
          .from(leads)
          .where(gte(leads.createdAt, reportStartDate))

        const [totalPartners] = await db.select({ count: count() })
          .from(partners)
          .where(gte(partners.createdAt, reportStartDate))

        csvData = 'Metric,Value,Period\n'
        csvData += `Total Revenue,₹${totalRevenue.total?.toLocaleString() || 0},${period} days\n`
        csvData += `New Clients,${totalClients.count},${period} days\n`
        csvData += `New Leads,${totalLeads.count},${period} days\n`
        csvData += `New Partners,${totalPartners.count},${period} days\n`
        csvData += `Conversion Rate,${totalLeads.count > 0 ? Math.round((totalClients.count / totalLeads.count) * 100) : 0}%,${period} days\n`
        break
    }

    return new Response(csvData, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    })

  } catch (error) {
    console.error('Export report error:', error)
    return c.json({ error: 'Failed to export report' }, 500)
  }
})

// ===== RAZORPAY PAYMENT MANAGEMENT =====

// Create Razorpay order for invoice payment
app.post('/payments/create-order', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', z.object({
  invoiceId: z.string().uuid(),
  monthlyAmount: z.number().min(1),
  currency: z.string().default('INR'),
  receipt: z.string().optional()
})), async (c) => {
  try {
    const { invoiceId, monthlyAmount, currency, receipt } = c.req.valid('json')

    // Verify invoice exists
    const invoice = await db.select().from(billingInvoices).where(eq(billingInvoices.id, invoiceId)).limit(1)
    if (invoice.length === 0) {
      return c.json({ success: false, error: 'Invoice not found' }, 404)
    }

    // Create Razorpay order
    const orderResult = await razorpayService.createOrder({
      amount: monthlyAmount * 100, // Convert to paise
      currency,
      receipt: receipt || `INV-${invoiceId.slice(-8)}`,
      notes: {
        invoice_id: invoiceId,
        client_id: invoice[0].clientId || ''
      }
    })

    if (!orderResult.success) {
      return c.json({ success: false, error: orderResult.error }, 500)
    }

    return c.json({
      success: true,
      order: orderResult.order
    })

  } catch (error) {
    console.error('Create Razorpay order error:', error)
    return c.json({ success: false, error: 'Failed to create payment order' }, 500)
  }
})

// Record manual payment
app.post('/payments/record', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  invoiceId: z.string().uuid(),
  monthlyAmount: z.number().min(0.01),
  paymentMethod: z.string().min(1),
  transactionId: z.string().optional(),
  notes: z.string().optional()
})), async (c) => {
  try {
    const { invoiceId, monthlyAmount, paymentMethod, transactionId, notes } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Verify invoice exists and is not already paid
    const [invoice] = await db.select().from(billingInvoices).where(eq(billingInvoices.id, invoiceId)).limit(1)
    if (!invoice) {
      return c.json({ error: 'Invoice not found' }, 404)
    }

    if (invoice.status === 'paid') {
      return c.json({ error: 'Invoice is already paid' }, 400)
    }

    // Use database transaction to ensure atomicity
    const result = await db.transaction(async (tx) => {
      // Lock the invoice to prevent concurrent payment processing
      const [lockedInvoice] = await tx.select()
        .from(billingInvoices)
        .where(eq(billingInvoices.id, invoiceId))
        .for('update')
        .limit(1)

      if (!lockedInvoice) {
        throw new Error('Invoice not found or already locked')
      }

      // Create payment record
      const [newPayment] = await tx.insert(billingPayments).values({
        invoiceId,
        subscriptionId: invoice.subscriptionId,
        clientId: invoice.clientId,
        amount: monthlyAmount.toString(),
        currency: 'INR',
        status: 'succeeded',
        paymentMethod,
        razorpayPaymentId: transactionId || `MANUAL-${Date.now()}`,
        createdAt: new Date()
      }).returning()

      // Update invoice status
      await tx.update(billingInvoices)
        .set({
          status: 'paid',
          paidDate: new Date().toISOString().split('T')[0]
        })
        .where(eq(billingInvoices.id, invoiceId))

      // Update billing subscription status if applicable (with row-level locking)
      if (invoice.subscriptionId) {
        // Lock the billing subscription row to prevent concurrent updates
        const [billingSubscription] = await tx.select()
          .from(billingSubscriptions)
          .where(eq(billingSubscriptions.id, invoice.subscriptionId))
          .for('update')
          .limit(1)

        if (billingSubscription) {
          await tx.update(billingSubscriptions)
            .set({ status: 'paid' })
            .where(eq(billingSubscriptions.id, invoice.subscriptionId))
        }
      }

      return newPayment
    })

    return c.json({
      message: 'Payment recorded successfully',
      payment: result,
      recordedBy: admin.name,
      notes: notes || ''
    })

  } catch (error) {
    console.error('Record payment error:', error)
    return c.json({ error: 'Failed to record payment' }, 500)
  }
})

// Get pending referral verifications
app.get('/referrals/pending', adminAuthMiddleware, requirePermission('partners:read'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const offset = (page - 1) * limit

    // Get pending referrals (not verified yet)
    const pendingReferrals = await db
      .select({
        id: schoolReferrals.id,
        school: {
          id: clients.id,
          schoolName: clients.schoolName,
          schoolCode: clients.schoolCode,
          email: clients.email,
          phone: clients.phone,
          address: clients.address,
          contactPerson: clients.contactPerson,
          actualStudentCount: clients.actualStudentCount
        },
        partner: {
          id: partners.id,
          name: partners.name,
          email: partners.email,
          companyName: partners.companyName,
          partnerCode: partners.partnerCode
        },
        referralCode: referralCodes.code,
        referredAt: schoolReferrals.referredAt,
        referralSource: schoolReferrals.referralSource,
        ipAddress: schoolReferrals.ipAddress,
        userAgent: schoolReferrals.userAgent,
        appliedBy: {
          id: clientUsers.id,
          name: clientUsers.name,
          email: clientUsers.email
        }
      })
      .from(schoolReferrals)
      .innerJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .innerJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .innerJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .leftJoin(clientUsers, eq(schoolReferrals.appliedBy, clientUsers.id))
      .where(and(
        eq(schoolReferrals.isActive, true),
        sql`${schoolReferrals.verifiedAt} IS NULL`
      ))
      .orderBy(desc(schoolReferrals.referredAt))
      .limit(limit)
      .offset(offset)

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(and(
        eq(schoolReferrals.isActive, true),
        sql`${schoolReferrals.verifiedAt} IS NULL`
      ))

    return c.json({
      referrals: pendingReferrals,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching pending referrals:', error)
    return c.json({ error: 'Failed to fetch pending referrals' }, 500)
  }
})

// Verify referral (approve)
app.put('/referrals/:id/verify', adminAuthMiddleware, requirePermission('partners:write'), async (c) => {
  try {
    const referralId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Check if referral exists and is pending
    const [referral] = await db
      .select({
        id: schoolReferrals.id,
        clientId: schoolReferrals.clientId,
        partnerId: schoolReferrals.partnerId,
        verifiedAt: schoolReferrals.verifiedAt,
        schoolName: clients.schoolName,
        partnerName: partners.name,
        referralCode: referralCodes.code
      })
      .from(schoolReferrals)
      .innerJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .innerJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .innerJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .where(eq(schoolReferrals.id, referralId))
      .limit(1)

    if (!referral) {
      return c.json({ error: 'Referral not found' }, 404)
    }

    if (referral.verifiedAt) {
      return c.json({ error: 'Referral already verified' }, 400)
    }

    // Update referral as verified
    const [updatedReferral] = await db
      .update(schoolReferrals)
      .set({
        verifiedAt: new Date(),
        verifiedBy: admin.id
      })
      .where(eq(schoolReferrals.id, referralId))
      .returning()

    // Log audit trail
    await db.insert(auditLogs).values({
      adminId: admin.id,
      action: 'VERIFY_REFERRAL',
      resource: 'school_referral',
      resourceId: referralId,
      details: {
        schoolName: referral.schoolName,
        partnerName: referral.partnerName,
        referralCode: referral.referralCode,
        verifiedAt: new Date().toISOString()
      },
      ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      success: true,
      severity: 'medium',
      category: 'admin'
    })

    return c.json({
      success: true,
      message: 'Referral verified successfully',
      referral: {
        id: updatedReferral.id,
        verifiedAt: updatedReferral.verifiedAt,
        verifiedBy: admin.name
      }
    })

  } catch (error) {
    console.error('Error verifying referral:', error)
    return c.json({ error: 'Failed to verify referral' }, 500)
  }
})

// Reject referral
app.put('/referrals/:id/reject', adminAuthMiddleware, requirePermission('partners:write'), zValidator('json', z.object({
  reason: z.string().min(10, 'Rejection reason must be at least 10 characters')
})), async (c) => {
  try {
    const referralId = c.req.param('id')
    const { reason } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Check if referral exists and is pending
    const [referral] = await db
      .select({
        id: schoolReferrals.id,
        clientId: schoolReferrals.clientId,
        partnerId: schoolReferrals.partnerId,
        verifiedAt: schoolReferrals.verifiedAt,
        schoolName: clients.schoolName,
        partnerName: partners.name,
        referralCode: referralCodes.code
      })
      .from(schoolReferrals)
      .innerJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .innerJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .innerJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
      .where(eq(schoolReferrals.id, referralId))
      .limit(1)

    if (!referral) {
      return c.json({ error: 'Referral not found' }, 404)
    }

    if (referral.verifiedAt) {
      return c.json({ error: 'Referral already verified, cannot reject' }, 400)
    }

    // Deactivate referral (reject)
    const [updatedReferral] = await db
      .update(schoolReferrals)
      .set({
        isActive: false,
        verifiedBy: admin.id, // Track who rejected it
        rejectionReason: reason // Save rejection reason
      })
      .where(eq(schoolReferrals.id, referralId))
      .returning()

    // Log audit trail
    await db.insert(auditLogs).values({
      adminId: admin.id,
      action: 'REJECT_REFERRAL',
      resource: 'school_referral',
      resourceId: referralId,
      details: {
        schoolName: referral.schoolName,
        partnerName: referral.partnerName,
        referralCode: referral.referralCode,
        rejectionReason: reason,
        rejectedAt: new Date().toISOString()
      },
      ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      success: true,
      severity: 'medium',
      category: 'admin'
    })

    return c.json({
      success: true,
      message: 'Referral rejected successfully',
      referral: {
        id: updatedReferral.id,
        isActive: updatedReferral.isActive,
        rejectedBy: admin.name,
        rejectionReason: reason
      }
    })

  } catch (error) {
    console.error('Error rejecting referral:', error)
    return c.json({ error: 'Failed to reject referral' }, 500)
  }
})

// Verify Razorpay payment
app.post('/payments/verify', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', z.object({
  razorpayOrderId: z.string(),
  razorpayPaymentId: z.string(),
  razorpaySignature: z.string(),
  invoiceId: z.string().uuid()
})), async (c) => {
  try {
    const { razorpayOrderId, razorpayPaymentId, razorpaySignature, invoiceId } = c.req.valid('json')

    // Verify payment signature
    const isValid = razorpayService.verifyPaymentSignature({
      razorpayOrderId,
      razorpayPaymentId,
      razorpaySignature
    })

    if (!isValid) {
      return c.json({ success: false, error: 'Invalid payment signature' }, 400)
    }

    // Get payment details from Razorpay
    const paymentResult = await razorpayService.getPayment(razorpayPaymentId)
    if (!paymentResult.success) {
      return c.json({ success: false, error: 'Failed to fetch payment details' }, 500)
    }

    const payment = paymentResult.payment

    // Get invoice details
    const [invoice] = await db.select()
      .from(billingInvoices)
      .where(eq(billingInvoices.id, invoiceId))
      .limit(1)

    if (!invoice) {
      return c.json({ success: false, error: 'Invoice not found' }, 404)
    }

    // Update invoice status
    await db.update(billingInvoices)
      .set({
        status: 'paid',
        paidDate: new Date().toISOString().split('T')[0]
      })
      .where(eq(billingInvoices.id, invoiceId))

    // Create payment record
    const [newPayment] = await db.insert(billingPayments).values({
      invoiceId,
      subscriptionId: invoice.subscriptionId,
      clientId: payment.notes?.client_id || '',
      razorpayPaymentId,
      razorpayOrderId,
      amount: (payment.amount / 100).toString(), // Convert from paise
      currency: payment.currency,
      status: 'succeeded',
      paymentMethod: payment.method,
      createdAt: new Date(payment.created_at * 1000)
    }).returning()

    return c.json({
      success: true,
      payment: newPayment,
      message: 'Payment verified and recorded successfully'
    })

  } catch (error) {
    console.error('Verify payment error:', error)
    return c.json({ success: false, error: 'Failed to verify payment' }, 500)
  }
})

// Get payment history for a client
app.get('/payments/client/:clientId', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const clientId = c.req.param('clientId')

    const clientPayments = await db.select({
      id: billingPayments.id,
      invoiceId: billingPayments.invoiceId,
      razorpayPaymentId: billingPayments.razorpayPaymentId,
      monthlyAmount: billingPayments.amount,
      currency: billingPayments.currency,
      status: billingPayments.status,
      paymentMethod: billingPayments.paymentMethod,
      createdAt: billingPayments.createdAt,
      invoice: {
        invoiceNumber: billingInvoices.invoiceNumber,
        issuedDate: billingInvoices.issuedDate,
        nextBillingDate: billingInvoices.dueDate
      }
    })
    .from(billingPayments)
    .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
    .where(eq(billingPayments.clientId, clientId))
    .orderBy(desc(billingPayments.createdAt))

    return c.json({
      success: true,
      payments: clientPayments
    })

  } catch (error) {
    console.error('Get client payments error:', error)
    return c.json({ success: false, error: 'Failed to fetch payment history' }, 500)
  }
})

// Health check for admin routes
// ===== SUPPORT TICKET MANAGEMENT =====

// Validation schemas for support tickets
const createTicketMessageSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  attachments: z.array(z.string()).optional()
})

const updateTicketSchema = z.object({
  status: z.enum(['open', 'in_progress', 'resolved', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  assignedTo: z.string().uuid().optional(),
  category: z.string().optional()
})

// Get all support tickets with filtering and pagination
app.get('/support/tickets', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '20')
    const status = c.req.query('status')
    const priority = c.req.query('priority')
    const assignedTo = c.req.query('assignedTo')
    const clientId = c.req.query('clientId')
    const search = c.req.query('search') || ''
    const sortBy = c.req.query('sortBy') || 'createdAt'
    const sortOrder = c.req.query('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // Build where conditions
    let whereConditions = []

    if (status) {
      whereConditions.push(eq(supportTickets.status, status))
    }

    if (priority) {
      whereConditions.push(eq(supportTickets.priority, priority))
    }

    if (assignedTo) {
      whereConditions.push(eq(supportTickets.assignedTo, assignedTo))
    }

    if (clientId) {
      whereConditions.push(eq(supportTickets.clientId, clientId))
    }

    if (search) {
      whereConditions.push(
        sql`(${supportTickets.title} ILIKE ${`%${search}%`} OR ${supportTickets.description} ILIKE ${`%${search}%`})`
      )
    }

    // Get tickets with client and assigned admin info
    const tickets = await db.select({
      id: supportTickets.id,
      title: supportTickets.title,
      description: supportTickets.description,
      priority: supportTickets.priority,
      status: supportTickets.status,
      category: supportTickets.category,
      assignedTo: supportTickets.assignedTo,
      resolvedAt: supportTickets.resolvedAt,
      createdAt: supportTickets.createdAt,
      updatedAt: supportTickets.updatedAt,
      // Client information
      clientId: clients.id,
      clientName: clients.schoolName,
      clientEmail: clients.email,
      // Created by information
      createdById: clientUsers.id,
      createdByName: clientUsers.name,
      createdByEmail: clientUsers.email
    })
    .from(supportTickets)
    .leftJoin(clients, eq(supportTickets.clientId, clients.id))
    .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .orderBy(sortOrder === 'desc' ? desc(supportTickets.createdAt) : supportTickets.createdAt)
    .limit(limit)
    .offset(offset)

    // Get total count for pagination
    const [{ count: totalCount }] = await db.select({ count: count() })
      .from(supportTickets)
      .leftJoin(clients, eq(supportTickets.clientId, clients.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    // Get assigned admin/partner names for tickets
    const ticketsWithAssignees = await Promise.all(
      tickets.map(async (ticket) => {
        let assignedAdminName = null
        let assignedPartnerName = null

        if (ticket.assignedTo) {
          // First check if assigned to admin
          const [admin] = await db.select({ name: adminUsers.name })
            .from(adminUsers)
            .where(eq(adminUsers.id, ticket.assignedTo))
            .limit(1)

          if (admin) {
            assignedAdminName = admin.name
          } else {
            // Check if assigned to partner
            const [partner] = await db.select({
              name: partners.name,
              companyName: partners.companyName
            })
              .from(partners)
              .where(eq(partners.id, ticket.assignedTo))
              .limit(1)

            if (partner) {
              assignedPartnerName = `${partner.name} (${partner.companyName})`
            }
          }
        }

        return {
          ...ticket,
          assignedAdminName: assignedAdminName || assignedPartnerName || null,
          assignedType: assignedAdminName ? 'admin' : assignedPartnerName ? 'partner' : null
        }
      })
    )

    return c.json({
      tickets: ticketsWithAssignees,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    })

  } catch (error) {
    console.error('Get support tickets error:', error)
    return c.json({ error: 'Failed to fetch support tickets' }, 500)
  }
})

// Get single support ticket with messages
app.get('/support/tickets/:id', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const ticketId = c.req.param('id')

    // Get ticket details
    const [ticket] = await db.select({
      id: supportTickets.id,
      title: supportTickets.title,
      description: supportTickets.description,
      priority: supportTickets.priority,
      status: supportTickets.status,
      category: supportTickets.category,
      assignedTo: supportTickets.assignedTo,
      resolvedAt: supportTickets.resolvedAt,
      createdAt: supportTickets.createdAt,
      updatedAt: supportTickets.updatedAt,
      // Client information
      clientId: clients.id,
      clientName: clients.schoolName,
      clientEmail: clients.email,
      clientPhone: clients.phone,
      // Created by information
      createdById: clientUsers.id,
      createdByName: clientUsers.name,
      createdByEmail: clientUsers.email
    })
    .from(supportTickets)
    .leftJoin(clients, eq(supportTickets.clientId, clients.id))
    .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
    .where(eq(supportTickets.id, ticketId))
    .limit(1)

    if (!ticket) {
      return c.json({ error: 'Ticket not found' }, 404)
    }

    // Get assigned admin name
    let assignedAdminName = null
    if (ticket.assignedTo) {
      const [admin] = await db.select({ name: adminUsers.name, email: adminUsers.email })
        .from(adminUsers)
        .where(eq(adminUsers.id, ticket.assignedTo))
        .limit(1)
      assignedAdminName = admin?.name || null
    }

    // Get ticket messages
    const messages = await db.select({
      id: ticketMessages.id,
      message: ticketMessages.message,
      senderType: ticketMessages.senderType,
      senderId: ticketMessages.senderId,
      attachments: ticketMessages.attachments,
      createdAt: ticketMessages.createdAt
    })
    .from(ticketMessages)
    .where(eq(ticketMessages.ticketId, ticketId))
    .orderBy(ticketMessages.createdAt)

    // Get sender names for messages
    const messagesWithSenders = await Promise.all(
      messages.map(async (message) => {
        let senderName = 'Unknown'
        let senderEmail = ''

        if (message.senderType === 'admin') {
          const [admin] = await db.select({ name: adminUsers.name, email: adminUsers.email })
            .from(adminUsers)
            .where(eq(adminUsers.id, message.senderId))
            .limit(1)
          senderName = admin?.name || 'Admin'
          senderEmail = admin?.email || ''
        } else if (message.senderType === 'client') {
          const [client] = await db.select({ name: clientUsers.name, email: clientUsers.email })
            .from(clientUsers)
            .where(eq(clientUsers.id, message.senderId))
            .limit(1)
          senderName = client?.name || 'Client'
          senderEmail = client?.email || ''
        }

        return {
          ...message,
          senderName,
          senderEmail
        }
      })
    )

    return c.json({
      ticket: {
        ...ticket,
        assignedAdminName
      },
      messages: messagesWithSenders
    })

  } catch (error) {
    console.error('Get support ticket error:', error)
    return c.json({ error: 'Failed to fetch support ticket' }, 500)
  }
})

// Update support ticket (status, priority, assignment, etc.)
app.put('/support/tickets/:id', adminAuthMiddleware, requirePermission('support:write'), zValidator('json', updateTicketSchema), async (c) => {
  try {
    const ticketId = c.req.param('id')
    const updateData = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Verify ticket exists
    const [existingTicket] = await db.select({
      id: supportTickets.id,
      status: supportTickets.status,
      assignedTo: supportTickets.assignedTo,
      priority: supportTickets.priority
    })
      .from(supportTickets)
      .where(eq(supportTickets.id, ticketId))
      .limit(1)

    if (!existingTicket) {
      return c.json({ error: 'Ticket not found' }, 404)
    }

    // Prepare update data
    const updateFields: any = {
      updatedAt: new Date()
    }

    if (updateData.status) {
      updateFields.status = updateData.status
      if (updateData.status === 'resolved' || updateData.status === 'closed') {
        updateFields.resolvedAt = new Date()
      }
    }

    if (updateData.priority) {
      updateFields.priority = updateData.priority
    }

    if (updateData.assignedTo !== undefined) {
      updateFields.assignedTo = updateData.assignedTo
    }

    if (updateData.category) {
      updateFields.category = updateData.category
    }

    // Update ticket
    await db.update(supportTickets)
      .set(updateFields)
      .where(eq(supportTickets.id, ticketId))

    // Add system message for status changes
    if (updateData.status && updateData.status !== existingTicket.status) {
      await db.insert(ticketMessages).values({
        ticketId,
        senderType: 'admin',
        senderId: admin.id,
        message: `Ticket status changed from "${existingTicket.status}" to "${updateData.status}" by ${admin.name}`
      })

      // Send status update notification
      await supportNotificationService.notifyTicketStatusUpdate(
        ticketId,
        existingTicket.status || 'open',
        updateData.status
      )
    }

    // Send assignment notification if ticket was assigned
    if (updateData.assignedTo && updateData.assignedTo !== existingTicket.assignedTo) {
      await supportNotificationService.notifyTicketAssigned(ticketId, updateData.assignedTo)
    }

    // Escalate to partner if priority is set to urgent
    if (updateData.priority === 'urgent' && updateData.priority !== existingTicket.priority) {
      await supportNotificationService.escalateToPartner(
        ticketId,
        'Ticket priority escalated to urgent - immediate attention required'
      )
    }

    return c.json({
      message: 'Ticket updated successfully',
      ticketId,
      updatedBy: admin.name,
      updatedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Update support ticket error:', error)
    return c.json({ error: 'Failed to update support ticket' }, 500)
  }
})

// Add message to support ticket
app.post('/support/tickets/:id/messages', adminAuthMiddleware, requirePermission('support:write'), zValidator('json', createTicketMessageSchema), async (c) => {
  try {
    const ticketId = c.req.param('id')
    const { message, attachments } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Verify ticket exists
    const [ticket] = await db.select({ id: supportTickets.id })
      .from(supportTickets)
      .where(eq(supportTickets.id, ticketId))
      .limit(1)

    if (!ticket) {
      return c.json({ error: 'Ticket not found' }, 404)
    }

    // Insert message
    const [newMessage] = await db.insert(ticketMessages).values({
      ticketId,
      senderType: 'admin',
      senderId: admin.id,
      message,
      attachments: attachments || null
    }).returning()

    // Update ticket's updatedAt timestamp
    await db.update(supportTickets)
      .set({ updatedAt: new Date() })
      .where(eq(supportTickets.id, ticketId))

    // Send notification to school about admin reply
    await supportNotificationService.notifyNewMessage(ticketId, newMessage.id, 'admin')

    return c.json({
      message: 'Message added successfully',
      messageId: newMessage.id,
      ticketId,
      sentBy: admin.name,
      sentAt: newMessage.createdAt
    })

  } catch (error) {
    console.error('Add ticket message error:', error)
    return c.json({ error: 'Failed to add message to ticket' }, 500)
  }
})

// Get support ticket analytics
app.get('/support/analytics', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const period = c.req.query('period') || '30' // days

    const supportStartDate = new Date()
    supportStartDate.setDate(supportStartDate.getDate() - parseInt(period))

    // Get ticket counts by status
    const statusCounts = await db.select({
      status: supportTickets.status,
      count: count()
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, supportStartDate))
    .groupBy(supportTickets.status)

    // Get ticket counts by priority
    const priorityCounts = await db.select({
      priority: supportTickets.priority,
      count: count()
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, supportStartDate))
    .groupBy(supportTickets.priority)

    // Get ticket counts by category
    const categoryCounts = await db.select({
      category: supportTickets.category,
      count: count()
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, supportStartDate))
    .groupBy(supportTickets.category)

    // Get assigned admin performance
    const adminPerformance = await db.select({
      adminId: supportTickets.assignedTo,
      totalAssigned: count(),
      resolved: sql<number>`COUNT(CASE WHEN ${supportTickets.status} IN ('resolved', 'closed') THEN 1 END)`
    })
    .from(supportTickets)
    .where(and(
      gte(supportTickets.createdAt, supportStartDate),
      isNotNull(supportTickets.assignedTo)
    ))
    .groupBy(supportTickets.assignedTo)

    // Get admin names for performance data
    const adminPerformanceWithNames = await Promise.all(
      adminPerformance.map(async (perf) => {
        const [admin] = await db.select({ name: adminUsers.name })
          .from(adminUsers)
          .where(eq(adminUsers.id, perf.adminId!))
          .limit(1)

        return {
          adminName: admin?.name || 'Unknown',
          totalAssigned: perf.totalAssigned,
          resolved: perf.resolved,
          resolutionRate: perf.totalAssigned > 0 ? Math.round((perf.resolved / perf.totalAssigned) * 100) : 0
        }
      })
    )

    // Get total metrics
    const [totalMetrics] = await db.select({
      totalTickets: count(),
      openTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.status} = 'open' THEN 1 END)`,
      inProgressTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.status} = 'in_progress' THEN 1 END)`,
      resolvedTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.status} IN ('resolved', 'closed') THEN 1 END)`,
      urgentTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.priority} = 'urgent' THEN 1 END)`,
      unassignedTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.assignedTo} IS NULL THEN 1 END)`
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, supportStartDate))

    return c.json({
      period: `${period} days`,
      totalMetrics,
      statusBreakdown: statusCounts,
      priorityBreakdown: priorityCounts,
      categoryBreakdown: categoryCounts,
      adminPerformance: adminPerformanceWithNames
    })

  } catch (error) {
    console.error('Support analytics error:', error)
    return c.json({ error: 'Failed to fetch support analytics' }, 500)
  }
})

// Get available admins for ticket assignment
app.get('/support/available-admins', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const admins = await db.select({
      id: adminUsers.id,
      name: adminUsers.name,
      email: adminUsers.email,
      role: adminUsers.role
    })
    .from(adminUsers)
    .where(and(
      eq(adminUsers.isActive, true),
      sql`${adminUsers.permissions} ? 'support:write'` // Has support write permission
    ))
    .orderBy(adminUsers.name)

    return c.json({ admins })

  } catch (error) {
    console.error('Get available admins error:', error)
    return c.json({ error: 'Failed to fetch available admins' }, 500)
  }
})

// Get support tickets by partner (for cross-role visibility)
app.get('/support/tickets/by-partner/:partnerId', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const partnerId = c.req.param('partnerId')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const status = c.req.query('status')
    const priority = c.req.query('priority')

    const offset = (page - 1) * limit

    // Build query conditions
    let whereConditions = [eq(schoolReferrals.partnerId, partnerId)]

    if (status) {
      whereConditions.push(eq(supportTickets.status, status))
    }

    if (priority) {
      whereConditions.push(eq(supportTickets.priority, priority))
    }

    // Get tickets with school and partner information
    const tickets = await db
      .select({
        ticket: supportTickets,
        school: {
          id: clients.id,
          name: clients.schoolName,
          email: clients.email
        },
        partner: {
          id: partners.id,
          name: partners.name,
          email: partners.email
        },
        creator: {
          name: clientUsers.name,
          email: clientUsers.email
        }
      })
      .from(supportTickets)
      .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
      .leftJoin(clients, eq(supportTickets.clientId, clients.id))
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
      .where(and(...whereConditions))
      .orderBy(desc(supportTickets.updatedAt))
      .limit(limit)
      .offset(offset)

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(supportTickets)
      .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
      .where(and(...whereConditions))

    return c.json({
      tickets,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        pages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partner tickets error:', error)
    return c.json({ error: 'Failed to fetch partner tickets' }, 500)
  }
})

// Escalate ticket to partner
app.post('/support/tickets/:id/escalate-to-partner', adminAuthMiddleware, requirePermission('support:write'), zValidator('json', z.object({
  reason: z.string().min(10, 'Escalation reason must be at least 10 characters'),
  priority: z.enum(['high', 'urgent']).optional()
})), async (c) => {
  try {
    const ticketId = c.req.param('id')
    const { reason, priority } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Verify ticket exists and get partner information
    const [ticketData] = await db
      .select({
        ticket: supportTickets,
        partner: {
          id: partners.id,
          name: partners.name,
          email: partners.email
        }
      })
      .from(supportTickets)
      .leftJoin(schoolReferrals, eq(supportTickets.clientId, schoolReferrals.clientId))
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .where(eq(supportTickets.id, ticketId))
      .limit(1)

    if (!ticketData) {
      return c.json({ error: 'Ticket not found' }, 404)
    }

    if (!ticketData.partner) {
      return c.json({ error: 'No partner associated with this school' }, 400)
    }

    // Update ticket priority if specified
    if (priority) {
      await db.update(supportTickets)
        .set({
          priority,
          updatedAt: new Date()
        })
        .where(eq(supportTickets.id, ticketId))
    }

    // Add escalation message
    await db.insert(ticketMessages).values({
      ticketId,
      senderType: 'admin',
      senderId: admin.id,
      message: `Ticket escalated to partner: ${reason}`
    })

    // Send escalation notification
    await supportNotificationService.escalateToPartner(ticketId, reason)

    return c.json({
      message: 'Ticket escalated to partner successfully',
      ticketId,
      partnerId: ticketData.partner.id,
      partnerName: ticketData.partner.name,
      escalatedBy: admin.name,
      escalatedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Escalate ticket to partner error:', error)
    return c.json({ error: 'Failed to escalate ticket to partner' }, 500)
  }
})

// ===== SECURITY ENDPOINTS =====

// Get security dashboard data
app.get('/security/dashboard', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    const timeRange = c.req.query('timeRange') as 'hour' | 'day' | 'week' | 'month' || 'day'

    // Get security dashboard data
    const dashboardData = await securityMonitor.getSecurityDashboard(timeRange)

    // Log admin access to security dashboard
    await auditLogger.logAdmin('security_dashboard_access', {
      adminId: admin.id,
      resource: 'security_dashboard',
      details: {
        timeRange,
        accessTime: new Date().toISOString()
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({
      success: true,
      data: dashboardData
    })

  } catch (error) {
    console.error('Security dashboard error:', error)
    return c.json({ error: 'Failed to fetch security dashboard data' }, 500)
  }
})

// Get audit logs
app.get('/security/audit-logs', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '50')
    const category = c.req.query('category') as string
    const severity = c.req.query('severity') as string
    const startDate = c.req.query('startDate') as string
    const endDate = c.req.query('endDate') as string

    const offset = (page - 1) * limit

    // Build query conditions
    const conditions = []
    if (category) {
      conditions.push(eq(auditLogs.category, category))
    }
    if (severity) {
      conditions.push(eq(auditLogs.severity, severity))
    }
    if (startDate) {
      conditions.push(gte(auditLogs.timestamp, new Date(startDate)))
    }
    if (endDate) {
      conditions.push(lte(auditLogs.timestamp, new Date(endDate)))
    }

    // Get audit logs
    const logs = await db
      .select()
      .from(auditLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(auditLogs.timestamp))
      .limit(limit)
      .offset(offset)

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(auditLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined)

    // Log admin access to audit logs
    await auditLogger.logAdmin('audit_logs_access', {
      adminId: admin.id,
      resource: 'audit_logs',
      details: {
        filters: { category, severity, startDate, endDate },
        page,
        limit,
        accessTime: new Date().toISOString()
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({
      success: true,
      data: {
        logs,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      }
    })

  } catch (error) {
    console.error('Audit logs error:', error)
    return c.json({ error: 'Failed to fetch audit logs' }, 500)
  }
})

// Get security events
app.get('/security/events', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '50')
    const eventType = c.req.query('eventType') as string
    const severity = c.req.query('severity') as string
    const resolved = c.req.query('resolved') as string

    const offset = (page - 1) * limit

    // Build query conditions
    const conditions = []
    if (eventType) {
      conditions.push(eq(securityEvents.eventType, eventType))
    }
    if (severity) {
      conditions.push(eq(securityEvents.severity, severity))
    }
    if (resolved !== undefined) {
      conditions.push(eq(securityEvents.resolved, resolved === 'true'))
    }

    // Get security events
    const events = await db
      .select()
      .from(securityEvents)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(securityEvents.createdAt))
      .limit(limit)
      .offset(offset)

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(securityEvents)
      .where(conditions.length > 0 ? and(...conditions) : undefined)

    return c.json({
      success: true,
      data: {
        events,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      }
    })

  } catch (error) {
    console.error('Security events error:', error)
    return c.json({ error: 'Failed to fetch security events' }, 500)
  }
})

// ===== BILLING MONITORING & AUTOMATION ENDPOINTS =====

app.get('/billing/health', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const healthReport = await billingMonitor.monitorBillingHealth()

    await auditLogger.logAdmin('billing_health_access', {
      adminId: 'admin-user',
      resource: 'billing_health',
      details: {
        status: healthReport.status,
        alertCount: healthReport.alerts.length
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(healthReport)

  } catch (error) {
    console.error('Billing health error:', error)
    return c.json({ error: 'Failed to get billing health' }, 500)
  }
})

// ===== PAYMENT MONITORING & ALERT SYSTEM =====

// Get payment monitoring dashboard
app.get('/payments/monitoring/dashboard', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const dashboard = await paymentMonitoringService.getMonitoringDashboard()

    await auditLogger.logAdmin('payment_monitoring_access', {
      adminId: getCurrentAdmin(c)?.id || 'unknown',
      resource: 'payment_monitoring',
      details: {
        totalOverdue: dashboard.summary.totalOverdue,
        alertCount: dashboard.alerts.length,
        overdueAccounts: dashboard.overdueAccounts.length
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(dashboard)
  } catch (error) {
    console.error('Payment monitoring dashboard error:', error)
    return c.json({ error: 'Failed to get payment monitoring dashboard' }, 500)
  }
})

// Process overdue payments and send alerts
app.post('/payments/monitoring/process-overdue', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    console.log(`🔄 Processing overdue payments triggered by admin: ${admin.name} (${admin.email})`)

    const result = await paymentMonitoringService.processOverduePayments()

    await auditLogger.logAdmin('payment_monitoring_process', {
      adminId: admin.id,
      resource: 'payment_monitoring',
      details: {
        processed: result.processed,
        alertsSent: result.alertsSent,
        penaltiesApplied: result.penaltiesApplied,
        errorCount: result.errors.length
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: result.errors.length === 0
    })

    return c.json({
      message: 'Overdue payment processing completed',
      result,
      triggeredBy: admin.name,
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Process overdue payments error:', error)
    return c.json({ error: 'Failed to process overdue payments' }, 500)
  }
})

// Get overdue payment alerts
app.get('/payments/monitoring/alerts', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { severity, limit = '50' } = c.req.query()
    const dashboard = await paymentMonitoringService.getMonitoringDashboard()

    let alerts = dashboard.alerts
    if (severity) {
      alerts = alerts.filter(alert => alert.severity === severity)
    }

    alerts = alerts.slice(0, parseInt(limit))

    return c.json({
      alerts,
      summary: {
        total: dashboard.alerts.length,
        critical: dashboard.alerts.filter(a => a.severity === 'critical').length,
        high: dashboard.alerts.filter(a => a.severity === 'high').length,
        medium: dashboard.alerts.filter(a => a.severity === 'medium').length,
        low: dashboard.alerts.filter(a => a.severity === 'low').length
      }
    })
  } catch (error) {
    console.error('Get payment alerts error:', error)
    return c.json({ error: 'Failed to get payment alerts' }, 500)
  }
})

// Get penalty calculations
app.get('/payments/monitoring/penalties', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const dashboard = await paymentMonitoringService.getMonitoringDashboard()

    const totalPenalties = dashboard.penaltyCalculations.reduce((sum, calc) => sum + calc.penaltyAmount, 0)
    const totalBase = dashboard.penaltyCalculations.reduce((sum, calc) => sum + calc.baseAmount, 0)

    return c.json({
      penalties: dashboard.penaltyCalculations,
      summary: {
        totalAccounts: dashboard.penaltyCalculations.length,
        totalPenalties,
        totalBase,
        averagePenalty: dashboard.penaltyCalculations.length > 0 ? totalPenalties / dashboard.penaltyCalculations.length : 0
      }
    })
  } catch (error) {
    console.error('Get penalty calculations error:', error)
    return c.json({ error: 'Failed to get penalty calculations' }, 500)
  }
})

// ===== ADVANCED FINANCIAL ANALYTICS =====

// Get advanced financial analytics dashboard
app.get('/analytics/financial/advanced', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const { startDate, endDate } = c.req.query()

    const start = startDate ? new Date(startDate) : undefined
    const end = endDate ? new Date(endDate) : undefined

    console.log(`📊 Generating advanced financial analytics from ${start?.toISOString()} to ${end?.toISOString()}`)

    const analytics = await advancedFinancialAnalytics.getAdvancedFinancialMetrics(start, end)

    await auditLogger.logAdmin('advanced_financial_analytics_access', {
      adminId: getCurrentAdmin(c)?.id || 'unknown',
      resource: 'advanced_financial_analytics',
      details: {
        startDate: start?.toISOString(),
        endDate: end?.toISOString(),
        mrr: analytics.revenueMetrics.mrr,
        arr: analytics.revenueMetrics.arr,
        grossMargin: analytics.profitabilityMetrics.grossMargin
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({
      analytics,
      metadata: {
        generatedAt: new Date().toISOString(),
        period: {
          startDate: start?.toISOString(),
          endDate: end?.toISOString()
        },
        dataQuality: {
          completeness: 95,
          accuracy: 98,
          timeliness: 100
        }
      }
    })
  } catch (error) {
    console.error('Advanced financial analytics error:', error)
    return c.json({ error: 'Failed to generate advanced financial analytics' }, 500)
  }
})

// Get financial KPI summary
app.get('/analytics/financial/kpis', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const analytics = await advancedFinancialAnalytics.getAdvancedFinancialMetrics()

    const kpiSummary = {
      revenue: {
        mrr: analytics.revenueMetrics.mrr,
        arr: analytics.revenueMetrics.arr,
        mrrGrowthRate: analytics.revenueMetrics.mrrGrowthRate,
        arpu: analytics.revenueMetrics.arpu
      },
      profitability: {
        grossMargin: analytics.profitabilityMetrics.grossMargin,
        operatingMargin: analytics.profitabilityMetrics.operatingMargin,
        netProfitMargin: analytics.profitabilityMetrics.netProfitMargin,
        ebitda: analytics.profitabilityMetrics.ebitda
      },
      operational: {
        cashFlow: analytics.operationalMetrics.cashFlowFromOperations,
        collectionRate: analytics.operationalMetrics.paymentCollectionRate,
        burnRate: analytics.operationalMetrics.burnRate
      },
      partner: {
        totalROI: analytics.partnerMetrics.totalPartnerROI,
        averageCommissionRate: analytics.partnerMetrics.averageCommissionRate,
        commissionEfficiency: analytics.partnerMetrics.commissionEfficiency
      }
    }

    return c.json({
      kpis: kpiSummary,
      lastUpdated: new Date().toISOString()
    })
  } catch (error) {
    console.error('Financial KPIs error:', error)
    return c.json({ error: 'Failed to get financial KPIs' }, 500)
  }
})

app.get('/billing/dunning/stats', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const stats = await dunningManager.getDunningStats()

    await auditLogger.logAdmin('dunning_stats_access', {
      adminId: 'admin-user',
      resource: 'dunning_stats',
      details: { totalCases: stats.totalCases },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(stats)

  } catch (error) {
    console.error('Dunning stats error:', error)
    return c.json({ error: 'Failed to get dunning stats' }, 500)
  }
})

app.get('/billing/dunning/cases', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const cases = await dunningManager.getActiveDunningCases()

    await auditLogger.logAdmin('dunning_cases_access', {
      adminId: 'admin-user',
      resource: 'dunning_cases',
      details: { caseCount: cases.length },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({ cases })

  } catch (error) {
    console.error('Dunning cases error:', error)
    return c.json({ error: 'Failed to get dunning cases' }, 500)
  }
})

app.get('/billing/payment-failures/stats', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const days = parseInt(c.req.query('days') || '30')
    const stats = await paymentFailureHandler.getPaymentFailureStats(days)

    await auditLogger.logAdmin('payment_failure_stats_access', {
      adminId: 'admin-user',
      resource: 'payment_failure_stats',
      details: { days, totalFailures: stats.totalFailures },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(stats)

  } catch (error) {
    console.error('Payment failure stats error:', error)
    return c.json({ error: 'Failed to get payment failure stats' }, 500)
  }
})

app.get('/billing/payment-failures', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const failures = await paymentFailureHandler.getActivePaymentFailures()

    await auditLogger.logAdmin('payment_failures_access', {
      adminId: 'admin-user',
      resource: 'payment_failures',
      details: { failureCount: failures.length },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({ failures })

  } catch (error) {
    console.error('Payment failures error:', error)
    return c.json({ error: 'Failed to get payment failures' }, 500)
  }
})

app.post('/billing/payment-failures/:paymentId/retry', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const paymentId = c.req.param('paymentId')
    const adminId = 'admin-user'

    const result = await paymentFailureHandler.manualRetryPayment(paymentId, adminId)

    return c.json(result)

  } catch (error) {
    console.error('Manual payment retry error:', error)
    return c.json({ error: 'Failed to retry payment' }, 500)
  }
})

app.post('/billing/dunning/process', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const adminId = 'admin-user'

    const result = await dunningManager.processDunningActions()

    await auditLogger.logAdmin('manual_dunning_process', {
      adminId,
      resource: 'dunning_manager',
      details: {
        processed: result.processed,
        actionsExecuted: result.actionsExecuted,
        errors: result.errors
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(result)

  } catch (error) {
    console.error('Manual dunning process error:', error)
    return c.json({ error: 'Failed to process dunning actions' }, 500)
  }
})

// ===== COMMISSION MANAGEMENT =====

// Get commission escrow overview
app.get('/commissions/escrow', adminAuthMiddleware, requirePermission('partners:read'), async (c) => {
  try {
    const { status, partner_id, page = '1', limit = '20' } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    const whereConditions = []

    if (status) {
      whereConditions.push(eq(partnerCommissionEscrow.escrowStatus, status))
    }

    if (partner_id) {
      whereConditions.push(eq(partnerCommissionEscrow.partnerId, partner_id))
    }

    // Get escrow records with partner and school information
    const escrowRecords = await db
      .select({
        escrow: partnerCommissionEscrow,
        partner: {
          id: partners.id,
          name: partners.name,
          partnerCode: partners.partnerCode
        },
        school: {
          id: clients.id,
          schoolName: clients.schoolName,
          schoolCode: clients.schoolCode
        }
      })
      .from(partnerCommissionEscrow)
      .leftJoin(partners, eq(partnerCommissionEscrow.partnerId, partners.id))
      .leftJoin(clients, eq(partnerCommissionEscrow.schoolId, clients.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(desc(partnerCommissionEscrow.createdAt))
      .limit(limitNum)
      .offset(offset)

    // Get total count
    const [totalCount] = whereConditions.length > 0
      ? await db.select({ count: count() }).from(partnerCommissionEscrow).where(and(...whereConditions))
      : await db.select({ count: count() }).from(partnerCommissionEscrow)

    // Get summary statistics
    const [summaryStats] = await db
      .select({
        totalEscrow: sql<number>`COALESCE(SUM(CAST(${partnerCommissionEscrow.commissionAmount} AS DECIMAL)), 0)`,
        pendingRelease: sql<number>`COALESCE(SUM(CASE WHEN ${partnerCommissionEscrow.escrowStatus} = 'school_paid' THEN CAST(${partnerCommissionEscrow.commissionAmount} AS DECIMAL) ELSE 0 END), 0)`,
        readyForRelease: sql<number>`COALESCE(SUM(CASE WHEN ${partnerCommissionEscrow.escrowStatus} = 'ready_for_release' THEN CAST(${partnerCommissionEscrow.commissionAmount} AS DECIMAL) ELSE 0 END), 0)`,
        totalReleased: sql<number>`COALESCE(SUM(CASE WHEN ${partnerCommissionEscrow.escrowStatus} = 'released' THEN CAST(${partnerCommissionEscrow.commissionAmount} AS DECIMAL) ELSE 0 END), 0)`
      })
      .from(partnerCommissionEscrow)

    return c.json({
      escrowRecords,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      },
      summary: summaryStats
    })

  } catch (error) {
    console.error('Commission escrow overview error:', error)
    return c.json({ error: 'Failed to fetch commission escrow data' }, 500)
  }
})

// Process eligible escrow releases (automated)
app.post('/commissions/process-releases', adminAuthMiddleware, requirePermission('partners:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const results = await commissionProcessor.processEligibleReleases(admin.id)

    // Calculate summary statistics
    const processedCount = results.length
    const successfulReleases = results.filter(r => r.success)
    const totalReleased = successfulReleases.reduce((sum, r) => sum + r.releasedAmount, 0)
    const errors = results.filter(r => !r.success).map(r => r.error).filter(Boolean)

    // Log admin action
    await auditLogger.logAdmin('commission_batch_release', {
      adminId: admin.id,
      resource: 'commission_escrow',
      details: {
        processedCount,
        successfulCount: successfulReleases.length,
        totalReleased,
        errors
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: errors.length === 0
    })

    return c.json({
      success: true,
      processedCount,
      successfulCount: successfulReleases.length,
      totalReleased,
      errors,
      results
    })

  } catch (error) {
    console.error('Commission release processing error:', error)
    return c.json({ error: 'Failed to process commission releases' }, 500)
  }
})

// Manual commission release
app.post('/commissions/escrow/:escrowId/release', adminAuthMiddleware, requirePermission('partners:write'), async (c) => {
  try {
    const escrowId = c.req.param('escrowId')
    const { reason } = await c.req.json()
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await commissionProcessor.releaseCommissionFromEscrow(escrowId, admin.id, 'manual')

    // Log admin action
    await auditLogger.logAdmin('commission_manual_release', {
      adminId: admin.id,
      resource: 'commission_escrow',
      resourceId: escrowId,
      details: {
        reason: reason || 'Manual release by admin',
        releasedAmount: result.releasedAmount,
        releaseType: result.releaseType
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: result.success
    })

    return c.json(result)

  } catch (error) {
    console.error('Manual commission release error:', error)
    return c.json({ error: 'Failed to release commission manually' }, 500)
  }
})

// Get commission calculation details
app.get('/commissions/calculation/:partnerId/:schoolId/:monthYear', adminAuthMiddleware, requirePermission('partners:read'), async (c) => {
  try {
    const partnerId = c.req.param('partnerId')
    const schoolId = c.req.param('schoolId')
    const monthYear = c.req.param('monthYear')

    // Get the latest payment for this school in the specified month
    const [latestPayment] = await db
      .select()
      .from(billingPayments)
      .leftJoin(billingInvoices, eq(billingPayments.invoiceId, billingInvoices.id))
      .where(and(
        eq(billingInvoices.clientId, schoolId),
        sql`TO_CHAR(${billingPayments.createdAt}, 'YYYY-MM') = ${monthYear}`
      ))
      .orderBy(desc(billingPayments.createdAt))
      .limit(1)

    if (!latestPayment) {
      return c.json({ error: 'No payment found for the specified period' }, 404)
    }

    const calculation = await commissionCalculationEngine.calculateMonthlyCommission(
      schoolId,
      partnerId,
      parseFloat(latestPayment.billing_payments.amount),
      monthYear,
      latestPayment.billing_payments.id
    )

    return c.json(calculation)

  } catch (error) {
    console.error('Commission calculation error:', error)
    return c.json({ error: 'Failed to calculate commission' }, 500)
  }
})

// ===== ESCROW MANAGEMENT ENDPOINTS =====

// Get escrow release job status
app.get("/escrow/job-status", async (c) => {
  try {
    const status = await escrowReleaseJob.getJobStatus()
    return c.json(status)
  } catch (error) {
    console.error('Get job status error:', error)
    return c.json({ error: 'Failed to get job status' }, 500)
  }
})

// Execute escrow release job manually
app.post("/escrow/execute-job", async (c) => {
  try {
    const body = await c.req.json()
    const config = {
      maxBatchSize: body.maxBatchSize || 50,
      maxDailyAmount: body.maxDailyAmount || 500000,
      emergencyStopEnabled: body.emergencyStopEnabled || false,
      dryRun: body.dryRun || false,
      riskThreshold: body.riskThreshold || 70
    }

    const result = await escrowReleaseJob.executeReleaseJob(config)
    return c.json(result)
  } catch (error) {
    console.error('Execute job error:', error)
    return c.json({ error: 'Failed to execute release job' }, 500)
  }
})

// Emergency stop escrow releases
app.post("/escrow/emergency-stop", async (c) => {
  try {
    const body = await c.req.json()
    const { reason } = body

    // Get admin user from session/token
    const adminUserId = 'admin_user' // This should come from authentication

    const result = await escrowReleaseJob.emergencyStop(reason, adminUserId)
    return c.json(result)
  } catch (error) {
    console.error('Emergency stop error:', error)
    return c.json({ error: 'Failed to activate emergency stop' }, 500)
  }
})

// Get escrow summary for partner
app.get("/escrow/partner/:partnerId/summary", async (c) => {
  try {
    const partnerId = c.req.param('partnerId')
    const summary = await commissionProcessor.getPartnerCommissionSummary(partnerId)
    return c.json(summary)
  } catch (error) {
    console.error('Get partner escrow summary error:', error)
    return c.json({ error: 'Failed to get partner escrow summary' }, 500)
  }
})

// ===== PARTNER COMMISSION MICROSERVICE =====

// Run partner commission audit and update missing commissions
app.post('/commissions/audit-and-update', adminAuthMiddleware, requirePermission('partners:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log(`🔍 Partner commission audit initiated by admin: ${admin.email}`)

    const results = await partnerCommissionMicroservice.checkAndUpdatePartnerCommissions()

    // Log the audit activity
    await auditLogger.log({
      action: 'PARTNER_COMMISSION_AUDIT',
      resource: 'partner_commissions',
      adminId: admin.id,
      success: true,
      severity: 'low',
      category: 'admin',
      details: {
        totalChecked: results.totalChecked,
        commissionsCreated: results.commissionsCreated,
        errorCount: results.errors.length
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown'
    })

    return c.json({
      success: true,
      message: 'Partner commission audit completed',
      results: {
        totalInvoicesChecked: results.totalChecked,
        newCommissionsCreated: results.commissionsCreated,
        errors: results.errors,
        details: results.details
      }
    })

  } catch (error) {
    console.error('❌ Partner commission audit error:', error)
    return c.json({
      error: 'Failed to run partner commission audit',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Get partner commission summary
app.get('/commissions/partner/:partnerId/summary', adminAuthMiddleware, requirePermission('partners:read'), async (c) => {
  try {
    const partnerId = c.req.param('partnerId')

    const summary = await partnerCommissionMicroservice.getPartnerCommissionSummary(partnerId)

    return c.json({
      success: true,
      data: summary
    })

  } catch (error) {
    console.error('❌ Get partner commission summary error:', error)
    return c.json({
      error: 'Failed to get partner commission summary',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Verify specific school payment and partner commission
app.get('/commissions/verify-school/:clientId', adminAuthMiddleware, requirePermission('partners:read'), async (c) => {
  try {
    const clientId = c.req.param('clientId')

    const verification = await partnerCommissionMicroservice.verifySchoolPaymentCommission(clientId)

    return c.json({
      success: true,
      data: verification
    })

  } catch (error) {
    console.error('❌ Verify school payment commission error:', error)
    return c.json({
      error: 'Failed to verify school payment commission',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})

// Get escrow details by ID
app.get("/escrow/:escrowId", async (c) => {
  try {
    const escrowId = c.req.param('escrowId')
    const escrowDetails = await commissionProcessor.getEscrowDetails(escrowId)
    return c.json(escrowDetails)
  } catch (error) {
    console.error('Get escrow details error:', error)
    return c.json({ error: 'Failed to get escrow details' }, 500)
  }
})

// Manual escrow release
app.post("/escrow/:escrowId/release", async (c) => {
  try {
    const escrowId = c.req.param('escrowId')
    const body = await c.req.json()
    const { reason } = body

    // Get admin user from session/token
    const adminUserId = 'admin_user' // This should come from authentication

    const result = await commissionProcessor.releaseCommissionFromEscrow(
      escrowId,
      adminUserId,
      'manual'
    )

    return c.json(result)
  } catch (error) {
    console.error('Manual escrow release error:', error)
    return c.json({ error: 'Failed to release escrow manually' }, 500)
  }
})

// Get all escrows with filters
app.get("/escrows", async (c) => {
  try {
    const query = c.req.query()
    const page = parseInt(query.page || '1')
    const limit = parseInt(query.limit || '20')
    const status = query.status
    const partnerId = query.partnerId
    const schoolId = query.schoolId

    // Build filters
    const filters = []
    if (status) {
      filters.push(eq(partnerCommissionEscrow.escrowStatus, status))
    }
    if (partnerId) {
      filters.push(eq(partnerCommissionEscrow.partnerId, partnerId))
    }
    if (schoolId) {
      filters.push(eq(partnerCommissionEscrow.schoolId, schoolId))
    }

    const escrows = await db
      .select({
        id: partnerCommissionEscrow.id,
        partnerId: partnerCommissionEscrow.partnerId,
        schoolId: partnerCommissionEscrow.schoolId,
        monthYear: partnerCommissionEscrow.monthYear,
        commissionAmount: partnerCommissionEscrow.commissionAmount,
        escrowStatus: partnerCommissionEscrow.escrowStatus,
        riskScore: partnerCommissionEscrow.riskScore,
        holdUntilDate: partnerCommissionEscrow.holdUntilDate,
        autoReleaseEnabled: partnerCommissionEscrow.autoReleaseEnabled,
        createdAt: partnerCommissionEscrow.createdAt
      })
      .from(partnerCommissionEscrow)
      .where(filters.length > 0 ? and(...filters) : undefined)
      .orderBy(desc(partnerCommissionEscrow.createdAt))
      .limit(limit)
      .offset((page - 1) * limit)

    const [totalResult] = await db
      .select({ count: count() })
      .from(partnerCommissionEscrow)
      .where(filters.length > 0 ? and(...filters) : undefined)

    return c.json({
      escrows,
      pagination: {
        page,
        limit,
        total: totalResult.count,
        pages: Math.ceil(totalResult.count / limit)
      }
    })
  } catch (error) {
    console.error('Get escrows error:', error)
    return c.json({ error: 'Failed to get escrows' }, 500)
  }
})

// ===== ESCROW SCHEDULER MANAGEMENT =====

// Get scheduler status
app.get("/escrow/scheduler/status", async (c) => {
  try {
    const status = escrowScheduler.getStatus()
    return c.json(status)
  } catch (error) {
    console.error('Get scheduler status error:', error)
    return c.json({ error: 'Failed to get scheduler status' }, 500)
  }
})

// Update scheduler configuration
app.post("/escrow/scheduler/config", async (c) => {
  try {
    const body = await c.req.json()
    const config = {
      enabled: body.enabled,
      intervalMinutes: body.intervalMinutes,
      maxBatchSize: body.maxBatchSize,
      maxDailyAmount: body.maxDailyAmount,
      riskThreshold: body.riskThreshold,
      emergencyStopEnabled: body.emergencyStopEnabled
    }

    escrowScheduler.updateConfig(config)
    return c.json({ message: 'Scheduler configuration updated', config })
  } catch (error) {
    console.error('Update scheduler config error:', error)
    return c.json({ error: 'Failed to update scheduler configuration' }, 500)
  }
})

// Start scheduler
app.post("/escrow/scheduler/start", async (c) => {
  try {
    const body = await c.req.json()
    escrowScheduler.start(body)
    return c.json({ message: 'Scheduler started' })
  } catch (error) {
    console.error('Start scheduler error:', error)
    return c.json({ error: 'Failed to start scheduler' }, 500)
  }
})

// Stop scheduler
app.post("/escrow/scheduler/stop", async (c) => {
  try {
    escrowScheduler.stop()
    return c.json({ message: 'Scheduler stopped' })
  } catch (error) {
    console.error('Stop scheduler error:', error)
    return c.json({ error: 'Failed to stop scheduler' }, 500)
  }
})

// Emergency stop scheduler
app.post("/escrow/scheduler/emergency-stop", async (c) => {
  try {
    const body = await c.req.json()
    const { reason } = body

    const result = escrowScheduler.emergencyStop(reason)
    return c.json(result)
  } catch (error) {
    console.error('Emergency stop scheduler error:', error)
    return c.json({ error: 'Failed to emergency stop scheduler' }, 500)
  }
})

// Resume scheduler operations
app.post("/escrow/scheduler/resume", async (c) => {
  try {
    const result = escrowScheduler.resumeOperations()
    return c.json(result)
  } catch (error) {
    console.error('Resume scheduler error:', error)
    return c.json({ error: 'Failed to resume scheduler' }, 500)
  }
})

app.get("/health", (c) => {
  return c.json({
    status: "ok",
    service: "Admin API",
    timestamp: new Date().toISOString()
  })
})

// Test commission calculation system
app.post('/test-commission-calculation', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const currentAdmin = getCurrentAdmin(c)

    // Test data
    const testPaymentAmount = 24000
    const testExpenses = 5200
    const testPartnerShare = 50 // 50%

    const expectedNetProfit = testPaymentAmount - testExpenses // ₹18,800
    const expectedPartnerCommission = (expectedNetProfit * testPartnerShare) / 100 // ₹9,400
    const expectedAdminEarnings = expectedNetProfit - expectedPartnerCommission // ₹9,400

    console.log('🧪 Testing Commission Calculation:', {
      grossRevenue: testPaymentAmount,
      operationalExpenses: testExpenses,
      netProfit: expectedNetProfit,
      partnerSharePercentage: testPartnerShare,
      expectedPartnerCommission,
      expectedAdminEarnings
    })

    // Get actual data from database
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1
    const currentYear = currentDate.getFullYear()
    const startOfMonth = new Date(currentYear, currentMonth - 1, 1)
    const endOfMonth = new Date(currentYear, currentMonth, 0)

    // Get monthly revenue
    const [monthlyRevenue] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${billingInvoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(billingInvoices)
      .where(and(
        eq(billingInvoices.status, 'paid'),
        gte(billingInvoices.paidDate, startOfMonth.toISOString().split('T')[0]),
        lte(billingInvoices.paidDate, endOfMonth.toISOString().split('T')[0])
      ))

    // Get monthly expenses
    const [monthlyExpenses] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${subscriptionExpenses.monthlyOperationalCost} AS DECIMAL)), 0)`
      })
      .from(subscriptionExpenses)
      .where(and(
        eq(subscriptionExpenses.isActive, true),
        gte(subscriptionExpenses.effectiveFrom, startOfMonth.toISOString().split('T')[0])
      ))

    // Get all commission sources
    const [partnerEarningsTotal] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`
      })
      .from(partnerEarnings)
      .where(and(
        gte(partnerEarnings.calculatedAt, startOfMonth),
        lte(partnerEarnings.calculatedAt, endOfMonth)
      ))

    const [escrowCommissionsTotal] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerCommissionEscrow.commissionAmount} AS DECIMAL)), 0)`
      })
      .from(partnerCommissionEscrow)
      .where(and(
        gte(partnerCommissionEscrow.createdAt, startOfMonth),
        lte(partnerCommissionEscrow.createdAt, endOfMonth)
      ))

    const [commissionTransactionsTotal] = await db
      .select({
        total: sql<number>`COALESCE(SUM(CAST(${partnerCommissionTransactions.commissionAmount} AS DECIMAL)), 0)`
      })
      .from(partnerCommissionTransactions)
      .where(and(
        gte(partnerCommissionTransactions.createdAt, startOfMonth),
        lte(partnerCommissionTransactions.createdAt, endOfMonth)
      ))

    const actualData = {
      grossRevenue: monthlyRevenue.total || 0,
      operationalExpenses: monthlyExpenses.total || 0,
      partnerCommissions: {
        partnerEarnings: partnerEarningsTotal.total || 0,
        escrowCommissions: escrowCommissionsTotal.total || 0,
        commissionTransactions: commissionTransactionsTotal.total || 0,
        total: (partnerEarningsTotal.total || 0) + (escrowCommissionsTotal.total || 0) + (commissionTransactionsTotal.total || 0)
      },
      netProfit: (monthlyRevenue.total || 0) - (monthlyExpenses.total || 0),
      adminNetEarnings: Math.max(0, (monthlyRevenue.total || 0) - (monthlyExpenses.total || 0) - ((partnerEarningsTotal.total || 0) + (escrowCommissionsTotal.total || 0) + (commissionTransactionsTotal.total || 0)))
    }

    return c.json({
      success: true,
      testScenario: {
        grossRevenue: testPaymentAmount,
        operationalExpenses: testExpenses,
        netProfit: expectedNetProfit,
        partnerSharePercentage: testPartnerShare,
        expectedPartnerCommission,
        expectedAdminEarnings
      },
      actualData,
      analysis: {
        revenueMatch: actualData.grossRevenue === testPaymentAmount,
        expenseMatch: actualData.operationalExpenses === testExpenses,
        commissionCalculationWorking: actualData.partnerCommissions.total > 0,
        adminEarningsCorrect: actualData.adminNetEarnings === expectedAdminEarnings,
        systemStatus: actualData.partnerCommissions.total > 0 ? 'Commission system working' : 'No commissions calculated yet'
      },
      recommendations: [
        actualData.partnerCommissions.total === 0 ? 'Run /recalculate-commissions to process existing payments' : null,
        actualData.grossRevenue !== testPaymentAmount ? 'Check if ₹24,000 payment exists in billing_invoices' : null,
        actualData.operationalExpenses !== testExpenses ? 'Verify ₹5,200 expense in subscription_expenses table' : null
      ].filter(Boolean)
    })

  } catch (error) {
    console.error('Test commission calculation error:', error)
    return c.json({ error: 'Failed to test commission calculation' }, 500)
  }
})

// ===== SYSTEM INITIALIZATION & HEALTH =====

// System initialization endpoint
app.get('/system/init', async (c) => {
  try {
    const { initializeServices, getServiceStatus, isHealthy } = await import('@/src/services/startup')

    // Auto-initialize services if not already done
    await initializeServices()

    const status = getServiceStatus()
    const healthy = isHealthy()

    return c.json({
      success: true,
      initialized: true,
      healthy,
      status,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('System initialization error:', error)

    return c.json({
      success: false,
      initialized: false,
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500)
  }
})

// Force re-initialization (admin only)
app.post('/system/init', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    console.log('🔄 Force re-initializing services...')

    const { initializeServices, getServiceStatus, isHealthy } = await import('@/src/services/startup')

    // Re-initialize services
    await initializeServices()

    const status = getServiceStatus()
    const healthy = isHealthy()

    return c.json({
      success: true,
      message: 'Services re-initialized successfully',
      initialized: true,
      healthy,
      status,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Force re-initialization error:', error)

    return c.json({
      success: false,
      message: 'Force re-initialization failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500)
  }
})

// Health check endpoint
app.get('/health', async (c) => {
  const startTime = Date.now()

  try {
    const { getServiceStatus, isHealthy } = await import('@/src/services/startup')

    // Get service status
    const serviceStatus = getServiceStatus()
    const systemHealthy = isHealthy()

    // Check database health
    const dbStartTime = Date.now()
    const activeSubsResult = await db.select({ count: count() })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.status, 'active'))
    const dbConnectionTime = Date.now() - dbStartTime
    const activeSubscriptions = activeSubsResult[0]?.count || 0

    // Get system metrics
    const memUsage = process.memoryUsage()
    const systemLoad = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)

    // Determine overall status
    const dbHealthy = dbConnectionTime < 1000
    const overallStatus = systemHealthy && dbHealthy ? 'healthy' : 'unhealthy'

    const healthResult = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      services: {
        billing: {
          status: systemHealthy ? 'healthy' : 'unhealthy',
          scheduler: serviceStatus.services?.['billing-scheduler']?.healthy || false,
        },
        database: {
          status: dbHealthy ? 'healthy' : 'unhealthy',
          connectionTime: dbConnectionTime,
          activeSubscriptions
        },
        external: {
          razorpay: process.env.RAZORPAY_KEY_ID ? 'configured' : 'missing',
          email: process.env.RESEND_API_KEY ? 'configured' : 'missing'
        }
      },
      metrics: {
        activeSubscriptions,
        systemLoad,
        memoryUsage: {
          used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
          total: Math.round(memUsage.heapTotal / 1024 / 1024) // MB
        }
      },
      responseTime: `${Date.now() - startTime}ms`
    }

    return c.json(healthResult, overallStatus === 'healthy' ? 200 : 503)

  } catch (error) {
    console.error('Health check error:', error)

    return c.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      responseTime: `${Date.now() - startTime}ms`
    }, 503)
  }
})

// Simple health check for load balancers
app.get('/health/simple', async (c) => {
  try {
    const { isHealthy } = await import('@/src/services/startup')
    const healthy = isHealthy()
    return c.text(healthy ? 'OK' : 'UNHEALTHY', healthy ? 200 : 503)
  } catch (error) {
    return c.text('UNHEALTHY', 503)
  }
})

export default app
