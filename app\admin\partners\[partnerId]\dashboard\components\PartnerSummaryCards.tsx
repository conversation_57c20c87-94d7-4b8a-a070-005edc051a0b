'use client'

import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  CreditCard,
  DollarSign,
  Pause,
  AlertTriangle,
  BarChart3
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/Card'

interface PartnerBillingData {
  pendingAmount: number
  holdAmount: number
  paidAmount: number
  totalEarned: number
  totalTdsDeducted: number
  netPayoutAmount: number
  totalTransactions: number
  successfulPayouts: number
  failedPayouts: number
  lastPayoutDate?: string
}

interface PartnerSummaryCardsProps {
  billing: PartnerBillingData
}

export default function PartnerSummaryCards({ billing }: PartnerSummaryCardsProps) {
  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  const calculateSuccessRate = () => {
    if (billing.totalTransactions === 0) return 0
    return Math.round((billing.successfulPayouts / billing.totalTransactions) * 100)
  }

  const summaryCards = [
    {
      title: 'Total Earned',
      value: formatCurrency(billing.totalEarned),
      subtitle: 'Lifetime earnings',
      icon: TrendingUp,
      color: 'blue',
      delay: 0.1
    },
    {
      title: 'Pending Amount',
      value: formatCurrency(billing.pendingAmount),
      subtitle: 'In 3-day hold',
      icon: Clock,
      color: 'yellow',
      delay: 0.2
    },
    {
      title: 'Hold Amount',
      value: formatCurrency(billing.holdAmount),
      subtitle: 'Currently locked',
      icon: Pause,
      color: 'orange',
      delay: 0.3
    },
    {
      title: 'Paid Amount',
      value: formatCurrency(billing.paidAmount),
      subtitle: 'Successfully transferred',
      icon: CheckCircle,
      color: 'green',
      delay: 0.4
    },
    {
      title: 'Net Payout',
      value: formatCurrency(billing.netPayoutAmount),
      subtitle: 'After TDS deduction',
      icon: CreditCard,
      color: 'purple',
      delay: 0.5
    },
    {
      title: 'TDS Deducted',
      value: formatCurrency(billing.totalTdsDeducted),
      subtitle: '5% tax deducted',
      icon: DollarSign,
      color: 'red',
      delay: 0.6
    },
    {
      title: 'Total Transactions',
      value: billing.totalTransactions.toString(),
      subtitle: `${billing.successfulPayouts} successful`,
      icon: BarChart3,
      color: 'indigo',
      delay: 0.7
    },
    {
      title: 'Success Rate',
      value: `${calculateSuccessRate()}%`,
      subtitle: `${billing.failedPayouts} failed`,
      icon: billing.failedPayouts > 0 ? AlertTriangle : CheckCircle,
      color: billing.failedPayouts > 0 ? 'red' : 'green',
      delay: 0.8
    }
  ]

  const getCardStyles = (color: string) => {
    const styles = {
      blue: 'border-blue-200 bg-blue-50',
      yellow: 'border-yellow-200 bg-yellow-50',
      orange: 'border-orange-200 bg-orange-50',
      green: 'border-green-200 bg-green-50',
      purple: 'border-purple-200 bg-purple-50',
      red: 'border-red-200 bg-red-50',
      indigo: 'border-indigo-200 bg-indigo-50'
    }
    return styles[color as keyof typeof styles] || 'border-gray-200 bg-gray-50'
  }

  const getIconStyles = (color: string) => {
    const styles = {
      blue: 'text-blue-600',
      yellow: 'text-yellow-600',
      orange: 'text-orange-600',
      green: 'text-green-600',
      purple: 'text-purple-600',
      red: 'text-red-600',
      indigo: 'text-indigo-600'
    }
    return styles[color as keyof typeof styles] || 'text-gray-600'
  }

  const getTextStyles = (color: string) => {
    const styles = {
      blue: 'text-blue-900',
      yellow: 'text-yellow-900',
      orange: 'text-orange-900',
      green: 'text-green-900',
      purple: 'text-purple-900',
      red: 'text-red-900',
      indigo: 'text-indigo-900'
    }
    return styles[color as keyof typeof styles] || 'text-gray-900'
  }

  const getSubtitleStyles = (color: string) => {
    const styles = {
      blue: 'text-blue-700',
      yellow: 'text-yellow-700',
      orange: 'text-orange-700',
      green: 'text-green-700',
      purple: 'text-purple-700',
      red: 'text-red-700',
      indigo: 'text-indigo-700'
    }
    return styles[color as keyof typeof styles] || 'text-gray-700'
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {summaryCards.map((card, index) => {
        const IconComponent = card.icon
        
        return (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: card.delay }}
          >
            <Card className={getCardStyles(card.color)}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${getSubtitleStyles(card.color)}`}>
                      {card.title}
                    </p>
                    <p className={`text-2xl font-bold ${getTextStyles(card.color)} mt-1`}>
                      {card.value}
                    </p>
                    <p className={`text-xs ${getSubtitleStyles(card.color)} mt-1`}>
                      {card.subtitle}
                    </p>
                  </div>
                  <IconComponent className={`w-8 h-8 ${getIconStyles(card.color)}`} />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )
      })}
    </div>
  )
}
