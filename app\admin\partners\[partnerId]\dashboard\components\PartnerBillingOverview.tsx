'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  DollarSign,
  Calendar
} from 'lucide-react'

interface PartnerData {
  id: string
  name: string
  email: string
  phone: string
  partnerCode: string
  profitSharePercentage: number
  panCard: string
  city: string
  age: number
  bankAccountHolderName: string
  totalSchools: number
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
}

interface PartnerBillingData {
  pendingAmount: number
  holdAmount: number
  paidAmount: number
  totalEarned: number
  totalTdsDeducted: number
  netPayoutAmount: number
  totalTransactions: number
  successfulPayouts: number
  failedPayouts: number
  lastPayoutDate?: string
}

interface PartnerBillingOverviewProps {
  partner: PartnerData
  billing: PartnerBillingData
}

export default function PartnerBillingOverview({ partner, billing }: PartnerBillingOverviewProps) {
  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  const calculatePayoutProgress = () => {
    if (billing.totalEarned === 0) return 0
    return Math.round((billing.paidAmount / billing.totalEarned) * 100)
  }

  const getPayoutStatus = () => {
    if (billing.pendingAmount > 0) {
      return {
        status: 'pending',
        label: 'Pending Payout',
        color: 'yellow',
        icon: Clock
      }
    } else if (billing.holdAmount > 0) {
      return {
        status: 'hold',
        label: 'In Hold Period',
        color: 'orange',
        icon: AlertTriangle
      }
    } else {
      return {
        status: 'up_to_date',
        label: 'Up to Date',
        color: 'green',
        icon: CheckCircle
      }
    }
  }

  const payoutStatus = getPayoutStatus()
  const StatusIcon = payoutStatus.icon

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="w-5 h-5" />
          <span>Billing Overview</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Payout Status */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-slate-700">Payout Status</h3>
              <Badge 
                variant="outline" 
                className={`
                  ${payoutStatus.color === 'green' ? 'bg-green-100 text-green-800 border-green-200' : ''}
                  ${payoutStatus.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : ''}
                  ${payoutStatus.color === 'orange' ? 'bg-orange-100 text-orange-800 border-orange-200' : ''}
                `}
              >
                <StatusIcon className="w-3 h-3 mr-1" />
                {payoutStatus.label}
              </Badge>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Total Earned</span>
                <span className="font-medium">{formatCurrency(billing.totalEarned)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Paid Amount</span>
                <span className="font-medium text-green-600">{formatCurrency(billing.paidAmount)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Pending Amount</span>
                <span className="font-medium text-yellow-600">{formatCurrency(billing.pendingAmount)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Hold Amount</span>
                <span className="font-medium text-orange-600">{formatCurrency(billing.holdAmount)}</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-xs text-slate-500">
                <span>Payout Progress</span>
                <span>{calculatePayoutProgress()}%</span>
              </div>
              <Progress value={calculatePayoutProgress()} className="h-2" />
            </div>
          </div>

          {/* TDS & Net Payout */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <h3 className="text-sm font-medium text-slate-700">TDS & Net Payout</h3>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                5% TDS
              </Badge>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Gross Amount</span>
                <span className="font-medium">{formatCurrency(billing.totalEarned)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">TDS Deducted</span>
                <span className="font-medium text-red-600">-{formatCurrency(billing.totalTdsDeducted)}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between text-sm font-semibold">
                  <span className="text-slate-900">Net Payout</span>
                  <span className="text-green-600">{formatCurrency(billing.netPayoutAmount)}</span>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center space-x-2 text-blue-800">
                <DollarSign className="w-4 h-4" />
                <span className="text-xs font-medium">TDS Certificate Available</span>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                Download TDS certificate for tax filing
              </p>
            </div>
          </div>

          {/* Transaction Summary */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <h3 className="text-sm font-medium text-slate-700">Transaction Summary</h3>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Total Transactions</span>
                <span className="font-medium">{billing.totalTransactions}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Successful</span>
                <span className="font-medium text-green-600">{billing.successfulPayouts}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Failed</span>
                <span className="font-medium text-red-600">{billing.failedPayouts}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Success Rate</span>
                <span className="font-medium">
                  {billing.totalTransactions > 0 
                    ? Math.round((billing.successfulPayouts / billing.totalTransactions) * 100)
                    : 0}%
                </span>
              </div>
            </div>

            <div className="bg-slate-50 p-3 rounded-lg">
              <div className="flex items-center space-x-2 text-slate-700">
                <Calendar className="w-4 h-4" />
                <span className="text-xs font-medium">Last Payout</span>
              </div>
              <p className="text-xs text-slate-600 mt-1">
                {formatDate(billing.lastPayoutDate)}
              </p>
            </div>
          </div>
        </div>

        {/* Partner Information Summary */}
        <div className="mt-6 pt-6 border-t border-slate-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-slate-600">Commission Rate:</span>
              <span className="ml-2 font-medium">{partner.profitSharePercentage}%</span>
            </div>
            <div>
              <span className="text-slate-600">Total Schools:</span>
              <span className="ml-2 font-medium">{partner.totalSchools}</span>
            </div>
            <div>
              <span className="text-slate-600">PAN Card:</span>
              <span className="ml-2 font-medium">{partner.panCard}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
