import { Hono } from 'hono'
import { db } from '@/src/db'
import { 
  partnerBilling,
  partnerCommissions,
  partnerTdsRecords,
  partnerHoldPeriods,
  razorpayPayoutLogs,
  partners
} from '@/src/db/schema'
import { eq, and, desc, sum, count } from 'drizzle-orm'
import { partnerAuthMiddleware } from '@/src/middleware/partner-auth'

const app = new Hono()

// Get partner billing dashboard
app.get('/dashboard', partnerAuthMiddleware, async (c) => {
  try {
    const partnerId = c.get('partnerId')

    console.log(`💰 [Partner] Fetching billing dashboard for partner: ${partnerId}`)

    // Get partner billing summary
    let [billing] = await db
      .select()
      .from(partnerBilling)
      .where(eq(partnerBilling.partnerId, partnerId))
      .limit(1)

    // If no billing record exists, create one with default values
    if (!billing) {
      [billing] = await db
        .insert(partnerBilling)
        .values({
          partnerId,
          pendingAmount: '0',
          holdAmount: '0',
          paidAmount: '0',
          totalEarned: '0',
          totalTdsDeducted: '0',
          netPayoutAmount: '0',
          totalTransactions: 0,
          successfulPayouts: 0,
          failedPayouts: 0
        })
        .returning()
    }

    // Get recent commission transactions
    const recentCommissions = await db
      .select({
        id: partnerCommissions.id,
        schoolName: partnerCommissions.schoolName,
        paymentMonth: partnerCommissions.paymentMonth,
        grossAmount: partnerCommissions.commissionAmount,
        payoutStatus: partnerCommissions.payoutStatus,
        payoutDate: partnerCommissions.payoutDate,
        razorpayPayoutId: partnerCommissions.razorpayPayoutId
      })
      .from(partnerCommissions)
      .where(eq(partnerCommissions.partnerId, partnerId))
      .orderBy(desc(partnerCommissions.createdAt))
      .limit(20)

    // Get TDS records for recent transactions
    const tdsRecords = await db
      .select({
        commissionId: partnerTdsRecords.commissionId,
        tdsAmount: partnerTdsRecords.tdsAmount,
        netAmount: partnerTdsRecords.netAmount
      })
      .from(partnerTdsRecords)
      .where(eq(partnerTdsRecords.partnerId, partnerId))

    // Combine commission and TDS data
    const transactionsWithTds = recentCommissions.map(commission => {
      const tdsRecord = tdsRecords.find(tds => tds.commissionId === commission.id)
      return {
        ...commission,
        tdsAmount: parseFloat(tdsRecord?.tdsAmount || '0'),
        netAmount: parseFloat(tdsRecord?.netAmount || commission.grossAmount)
      }
    })

    // Get hold period information
    const holdPeriods = await db
      .select({
        commissionId: partnerHoldPeriods.commissionId,
        holdAmount: partnerHoldPeriods.holdAmount,
        holdStatus: partnerHoldPeriods.holdStatus,
        holdEndDate: partnerHoldPeriods.holdEndDate
      })
      .from(partnerHoldPeriods)
      .where(eq(partnerHoldPeriods.partnerId, partnerId))
      .orderBy(desc(partnerHoldPeriods.createdAt))
      .limit(10)

    // Calculate current hold amount
    const currentHoldAmount = holdPeriods
      .filter(hold => hold.holdStatus === 'active')
      .reduce((sum, hold) => sum + parseFloat(hold.holdAmount), 0)

    // Prepare response data
    const billingData = {
      totalEarnings: parseFloat(billing.totalEarned || '0'),
      pendingAmount: parseFloat(billing.pendingAmount || '0'),
      holdAmount: currentHoldAmount,
      paidAmount: parseFloat(billing.paidAmount || '0'),
      totalTdsDeducted: parseFloat(billing.totalTdsDeducted || '0'),
      netPayoutAmount: parseFloat(billing.netPayoutAmount || '0'),
      totalTransactions: billing.totalTransactions || 0,
      successfulPayouts: billing.successfulPayouts || 0,
      failedPayouts: billing.failedPayouts || 0,
      lastPayoutDate: billing.lastPayoutDate
    }

    console.log(`✅ [Partner] Billing dashboard data fetched`)

    return c.json({
      billing: billingData,
      transactions: transactionsWithTds.map(transaction => ({
        ...transaction,
        grossAmount: parseFloat(transaction.grossAmount || '0')
      })),
      holdPeriods: holdPeriods.map(hold => ({
        ...hold,
        holdAmount: parseFloat(hold.holdAmount || '0')
      }))
    })

  } catch (error) {
    console.error('❌ [Partner] Error fetching billing dashboard:', error)
    return c.json({ error: 'Failed to fetch billing dashboard' }, 500)
  }
})

// Get detailed transaction history
app.get('/transactions', partnerAuthMiddleware, async (c) => {
  try {
    const partnerId = c.get('partnerId')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '20')
    const status = c.req.query('status') || 'all'

    console.log(`📊 [Partner] Fetching transaction history for partner: ${partnerId}`)

    // Build query conditions
    let whereConditions = [eq(partnerCommissions.partnerId, partnerId)]
    
    if (status !== 'all') {
      whereConditions.push(eq(partnerCommissions.payoutStatus, status))
    }

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(partnerCommissions)
      .where(and(...whereConditions))

    // Get paginated transactions
    const offset = (page - 1) * limit
    const transactions = await db
      .select({
        id: partnerCommissions.id,
        schoolName: partnerCommissions.schoolName,
        paymentMonth: partnerCommissions.paymentMonth,
        grossAmount: partnerCommissions.commissionAmount,
        payoutStatus: partnerCommissions.payoutStatus,
        payoutDate: partnerCommissions.payoutDate,
        razorpayPayoutId: partnerCommissions.razorpayPayoutId,
        createdAt: partnerCommissions.createdAt
      })
      .from(partnerCommissions)
      .where(and(...whereConditions))
      .orderBy(desc(partnerCommissions.createdAt))
      .limit(limit)
      .offset(offset)

    // Get TDS data for these transactions
    const commissionIds = transactions.map(t => t.id)
    const tdsRecords = await db
      .select({
        commissionId: partnerTdsRecords.commissionId,
        tdsAmount: partnerTdsRecords.tdsAmount,
        netAmount: partnerTdsRecords.netAmount
      })
      .from(partnerTdsRecords)
      .where(eq(partnerTdsRecords.partnerId, partnerId))

    // Combine data
    const transactionsWithTds = transactions.map(transaction => {
      const tdsRecord = tdsRecords.find(tds => tds.commissionId === transaction.id)
      return {
        ...transaction,
        grossAmount: parseFloat(transaction.grossAmount || '0'),
        tdsAmount: parseFloat(tdsRecord?.tdsAmount || '0'),
        netAmount: parseFloat(tdsRecord?.netAmount || transaction.grossAmount)
      }
    })

    const pagination = {
      currentPage: page,
      totalPages: Math.ceil((totalCount?.count || 0) / limit),
      totalRecords: totalCount?.count || 0,
      recordsPerPage: limit
    }

    console.log(`✅ [Partner] Transaction history fetched: ${transactions.length} records`)

    return c.json({
      transactions: transactionsWithTds,
      pagination
    })

  } catch (error) {
    console.error('❌ [Partner] Error fetching transaction history:', error)
    return c.json({ error: 'Failed to fetch transaction history' }, 500)
  }
})

// Get TDS certificate
app.get('/tds-certificate/:year', partnerAuthMiddleware, async (c) => {
  try {
    const partnerId = c.get('partnerId')
    const year = c.req.param('year')

    console.log(`📄 [Partner] Generating TDS certificate for partner: ${partnerId}, year: ${year}`)

    // Get all TDS records for the specified financial year
    const tdsRecords = await db
      .select({
        id: partnerTdsRecords.id,
        grossAmount: partnerTdsRecords.grossAmount,
        tdsAmount: partnerTdsRecords.tdsAmount,
        netAmount: partnerTdsRecords.netAmount,
        financialYear: partnerTdsRecords.financialYear,
        quarter: partnerTdsRecords.quarter,
        panCard: partnerTdsRecords.panCard,
        deductorPan: partnerTdsRecords.deductorPan,
        createdAt: partnerTdsRecords.createdAt
      })
      .from(partnerTdsRecords)
      .where(and(
        eq(partnerTdsRecords.partnerId, partnerId),
        eq(partnerTdsRecords.financialYear, year)
      ))
      .orderBy(desc(partnerTdsRecords.createdAt))

    // Calculate totals
    const totals = tdsRecords.reduce((acc, record) => ({
      grossAmount: acc.grossAmount + parseFloat(record.grossAmount),
      tdsAmount: acc.tdsAmount + parseFloat(record.tdsAmount),
      netAmount: acc.netAmount + parseFloat(record.netAmount)
    }), { grossAmount: 0, tdsAmount: 0, netAmount: 0 })

    // Get partner details
    const [partner] = await db
      .select({
        name: partners.name,
        panCard: partners.panCard,
        address: partners.address
      })
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    console.log(`✅ [Partner] TDS certificate data prepared for ${year}`)

    return c.json({
      partner,
      financialYear: year,
      tdsRecords: tdsRecords.map(record => ({
        ...record,
        grossAmount: parseFloat(record.grossAmount),
        tdsAmount: parseFloat(record.tdsAmount),
        netAmount: parseFloat(record.netAmount)
      })),
      totals,
      certificateNumber: `TDS-${partnerId.slice(-6)}-${year}`,
      issueDate: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ [Partner] Error generating TDS certificate:', error)
    return c.json({ error: 'Failed to generate TDS certificate' }, 500)
  }
})

export default app
