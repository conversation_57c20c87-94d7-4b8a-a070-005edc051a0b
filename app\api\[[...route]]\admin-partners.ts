import { Hono } from 'hono'
import { db } from '@/src/db'
import { 
  partners, 
  schoolReferrals, 
  partnerBilling,
  partnerCommissions
} from '@/src/db/schema'
import { eq, and, desc, count } from 'drizzle-orm'
import { adminAuthMiddleware } from '@/src/middleware/authMiddleware'

const app = new Hono()

// GET /admin/partners - Get all partners
app.get('/', adminAuthMiddleware, async (c) => {
  try {
    console.log('📊 [Admin] Fetching partners list')

    // Parse query parameters
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '20')
    const search = c.req.query('search') || ''
    const status = c.req.query('status') || 'all'

    // Calculate pagination
    const offset = (page - 1) * limit

    // Build query conditions
    let whereConditions = []
    
    if (status !== 'all') {
      whereConditions.push(eq(partners.isActive, status === 'active'))
    }

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(partners)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    // Get partners with pagination
    const partnersList = await db
      .select({
        id: partners.id,
        name: partners.name,
        email: partners.email,
        phone: partners.phone,
        partnerCode: partners.partnerCode,
        profitSharePercentage: partners.profitSharePercentage,
        isActive: partners.isActive,
        createdAt: partners.createdAt
      })
      .from(partners)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(desc(partners.createdAt))
      .limit(limit)
      .offset(offset)

    // Get school counts for each partner
    const schoolCounts = await db
      .select({
        partnerId: schoolReferrals.partnerId,
        count: count()
      })
      .from(schoolReferrals)
      .groupBy(schoolReferrals.partnerId)

    // Get billing information for each partner
    const billingInfo = await db
      .select({
        partnerId: partnerBilling.partnerId,
        totalEarned: partnerBilling.totalEarned,
        paidAmount: partnerBilling.paidAmount,
        pendingAmount: partnerBilling.pendingAmount
      })
      .from(partnerBilling)

    // Combine data
    const partnersWithDetails = partnersList.map(partner => {
      const schoolCount = schoolCounts.find(sc => sc.partnerId === partner.id)?.count || 0
      const billing = billingInfo.find(bi => bi.partnerId === partner.id) || {
        totalEarned: '0',
        paidAmount: '0',
        pendingAmount: '0'
      }
      
      return {
        ...partner,
        schoolCount,
        profitSharePercentage: parseFloat(partner.profitSharePercentage || '0'),
        totalEarned: parseFloat(billing.totalEarned || '0'),
        paidAmount: parseFloat(billing.paidAmount || '0'),
        pendingAmount: parseFloat(billing.pendingAmount || '0')
      }
    })

    console.log(`✅ [Admin] Partners list fetched: ${partnersWithDetails.length} partners`)

    return c.json({
      partners: partnersWithDetails,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil((totalCount?.count || 0) / limit),
        totalRecords: totalCount?.count || 0,
        recordsPerPage: limit
      }
    })

  } catch (error) {
    console.error('❌ [Admin] Error fetching partners list:', error)
    return c.json({ error: 'Failed to fetch partners list' }, 500)
  }
})

// POST /admin/partners - Create new partner
app.post('/', adminAuthMiddleware, async (c) => {
  try {
    console.log('🆕 [Admin] Creating new partner')

    const data = await c.req.json()
    
    // Validate required fields
    const requiredFields = ['name', 'email', 'phone', 'profitSharePercentage']
    for (const field of requiredFields) {
      if (!data[field]) {
        return c.json({ error: `Missing required field: ${field}` }, 400)
      }
    }

    // Check if email already exists
    const [existingPartner] = await db
      .select({ id: partners.id })
      .from(partners)
      .where(eq(partners.email, data.email))
      .limit(1)

    if (existingPartner) {
      return c.json({ error: 'Partner with this email already exists' }, 400)
    }

    // Generate unique partner code
    const partnerCode = generatePartnerCode()

    // Create partner
    const [newPartner] = await db
      .insert(partners)
      .values({
        name: data.name,
        email: data.email,
        phone: data.phone,
        partnerCode,
        passwordHash: 'temp_password_hash', // This should be properly hashed
        address: data.address || 'Address not provided',
        profitSharePercentage: data.profitSharePercentage.toString(),
        isActive: true,
        createdBy: 'admin-default' // Get from auth context
      })
      .returning()

    console.log(`✅ [Admin] New partner created: ${newPartner.name}`)

    return c.json({ 
      success: true,
      partner: newPartner
    })

  } catch (error) {
    console.error('❌ [Admin] Error creating partner:', error)
    return c.json({ error: 'Failed to create partner' }, 500)
  }
})

// GET /admin/partners/:id - Get partner details
app.get('/:id', adminAuthMiddleware, async (c) => {
  try {
    const partnerId = c.req.param('id')
    console.log(`📊 [Admin] Fetching partner details for: ${partnerId}`)

    // Get partner details
    const [partner] = await db
      .select()
      .from(partners)
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!partner) {
      return c.json({ error: 'Partner not found' }, 404)
    }

    // Get school count
    const [schoolCount] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(eq(schoolReferrals.partnerId, partnerId))

    // Get billing information
    const [billing] = await db
      .select()
      .from(partnerBilling)
      .where(eq(partnerBilling.partnerId, partnerId))
      .limit(1)

    // Get recent commissions
    const recentCommissions = await db
      .select({
        id: partnerCommissions.id,
        schoolName: partnerCommissions.schoolName,
        commissionAmount: partnerCommissions.commissionAmount,
        paymentDate: partnerCommissions.paymentDate,
        payoutStatus: partnerCommissions.payoutStatus
      })
      .from(partnerCommissions)
      .where(eq(partnerCommissions.partnerId, partnerId))
      .orderBy(desc(partnerCommissions.createdAt))
      .limit(5)

    // Prepare response
    const partnerDetails = {
      ...partner,
      profitSharePercentage: parseFloat(partner.profitSharePercentage || '0'),
      schoolCount: schoolCount?.count || 0,
      billing: billing ? {
        totalEarned: parseFloat(billing.totalEarned || '0'),
        paidAmount: parseFloat(billing.paidAmount || '0'),
        pendingAmount: parseFloat(billing.pendingAmount || '0'),
        totalTdsDeducted: parseFloat(billing.totalTdsDeducted || '0'),
        netPayoutAmount: parseFloat(billing.netPayoutAmount || '0'),
        totalTransactions: billing.totalTransactions || 0,
        successfulPayouts: billing.successfulPayouts || 0,
        failedPayouts: billing.failedPayouts || 0,
        lastPayoutDate: billing.lastPayoutDate
      } : null,
      recentCommissions: recentCommissions.map(commission => ({
        ...commission,
        commissionAmount: parseFloat(commission.commissionAmount || '0')
      }))
    }

    console.log(`✅ [Admin] Partner details fetched for: ${partner.name}`)

    return c.json({ partner: partnerDetails })

  } catch (error) {
    console.error('❌ [Admin] Error fetching partner details:', error)
    return c.json({ error: 'Failed to fetch partner details' }, 500)
  }
})

// Helper function to generate unique partner code
function generatePartnerCode(): string {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
  let code = ''
  for (let i = 0; i < 6; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return code
}

export default app
